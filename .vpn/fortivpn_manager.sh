#!/bin/bash

# --- 配置部分 ---
CONFIG_FILE="vpn_config.conf"
EXPECT_CONNECT_SCRIPT_PATH="./.fortivpn_expect_connect.exp" # 连接用的 expect 脚本
EXPECT_EDIT_SCRIPT_PATH="./.fortivpn_expect_edit.exp"     # 编辑用的 expect 脚本

# --- 颜色定义 ---
COLOR_RED='\033[0;31m'
COLOR_GREEN='\033[0;32m'
COLOR_YELLOW='\033[0;33m'
COLOR_BLUE='\033[0;34m'
COLOR_NC='\033[0m' # No Color (reset)

# --- 脚本内部变量 (从配置文件加载) ---
VPN_HOST=""
VPN_PORT=""
VPN_USERNAME=""
VPN_PASSWORD=""
VPN_PROFILE_NAME=""
VPN_EDIT_TYPE=""            # 新增
VPN_EDIT_AUTH_METHOD=""     # 新增
VPN_EDIT_CERT_TYPE=""       # 新增
FORTICLIENT_RPM=""
CONNECT_TIMEOUT=""
FORTICLIENT_RPM_INSTALLED_NAME=""

# --- 辅助函数 ---
# 打印带颜色的消息
# 参数1: 颜色 (例如 $COLOR_RED)
# 参数2: 消息
print_color() {
    printf "${1}%s${COLOR_NC}\n" "$2"
}

# 打印错误消息 (红色)
print_error() {
    print_color "$COLOR_RED" "Error: $1" >&2
}

# 打印成功消息 (绿色)
print_success() {
    print_color "$COLOR_GREEN" "$1"
}

# 打印警告消息 (黄色)
print_warning() {
    print_color "$COLOR_YELLOW" "Warning: $1"
}

# 打印信息消息 (蓝色)
print_info() {
    print_color "$COLOR_BLUE" "$1"
}


# --- 函数定义 ---

# 显示使用说明
display_usage() {
    echo "Usage: $0 [connect|disconnect|reconnect|status|info|configure]"
    echo "  connect     : Connects to the VPN."
    echo "  disconnect  : Disconnects from the VPN."
    echo "  reconnect   : Disconnects and then connects to the VPN."
    echo "  status      : Checks the current VPN connection status."
    echo "  info        : Displays VPN profile information."
    echo "  configure   : Checks and configures the VPN profile if not present."
    echo ""
    echo "Requires 'vpn_config.conf' in the same directory for parameters."
    echo "Requires 'expect' package for automated interactions."
}

# 加载配置文件 (增加新的配置项)
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        if [[ -z "$VPN_HOST" || -z "$VPN_USERNAME" || -z "$FORTICLIENT_RPM" || \
              -z "$VPN_PROFILE_NAME" || -z "$FORTICLIENT_RPM_INSTALLED_NAME" || \
              -z "$VPN_EDIT_TYPE" || -z "$VPN_EDIT_AUTH_METHOD" || -z "$VPN_EDIT_CERT_TYPE" ]]; then
            print_error "Required parameters are missing in $CONFIG_FILE. Check VPN_HOST, VPN_USERNAME, FORTICLIENT_RPM, VPN_PROFILE_NAME, FORTICLIENT_RPM_INSTALLED_NAME, VPN_EDIT_TYPE, VPN_EDIT_AUTH_METHOD, VPN_EDIT_CERT_TYPE."
            exit 1
        fi
        VPN_PORT="${VPN_PORT:-443}" # 确保VPN_PORT有值，用于edit和connect
        CONNECT_TIMEOUT="${CONNECT_TIMEOUT:-60}"
        print_info "Configuration loaded from $CONFIG_FILE."
    else
        print_error "Configuration file '$CONFIG_FILE' not found."
        echo "Please create it based on the template." >&2
        exit 1
    fi
}

# --- 系统检测函数 ---
detect_system_type() {
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        case "$ID" in
            "ubuntu")
                echo "ubuntu"
                ;;
            "centos"|"rhel"|"fedora")
                echo "rhel"
                ;;
            *)
                print_error "Unsupported system type: $ID"
                exit 1
                ;;
        esac
    else
        print_error "Cannot detect system type: /etc/os-release not found"
        exit 1
    fi
}

# Ubuntu系统安装FortiClient
install_forticlient_ubuntu() {
    print_info "Installing FortiClient VPN on Ubuntu..."
    
    # 检查是否已安装
    if dpkg -l | grep -q "forticlient"; then
        print_success "FortiClient VPN is already installed."
        return 0
    fi
    
    # 添加FortiClient仓库
    if [[ ! -f /etc/apt/sources.list.d/forticlient.list ]]; then
        echo "deb [arch=amd64] https://repo.fortinet.com/repo/ubuntu/ /bionic multiverse" | sudo tee /etc/apt/sources.list.d/forticlient.list
        wget -O - https://repo.fortinet.com/repo/ubuntu/DEB-GPG-KEY | sudo apt-key add -
    fi
    
    # 更新包列表并安装
    sudo apt-get update
    if ! sudo apt-get install -y forticlient; then
        print_error "Failed to install FortiClient VPN"
        return 1
    fi
    
    # 验证安装
    if ! command -v forticlient &> /dev/null; then
        print_error "FortiClient command not found after installation"
        return 1
    fi
    
    print_success "FortiClient VPN installed successfully on Ubuntu"
    return 0
}

# 检查 FortiClient 是否安装（修改后支持多系统）
check_forticlient_install() {
    print_info "Checking FortiClient VPN installation..."
    
    # 检查系统类型
    local system_type=$(detect_system_type)
    
    case "$system_type" in
        "rhel")
            if ! rpm -q "$FORTICLIENT_RPM_INSTALLED_NAME" &> /dev/null; then
                print_warning "FortiClient VPN is not installed."
                if [[ -f "$FORTICLIENT_RPM" ]]; then
                    read -p "$(print_color "$COLOR_YELLOW" "Do you want to install FortiClient VPN from $FORTICLIENT_RPM? (y/n): ")" confirm
                    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
                        print_info "Installing FortiClient VPN from $FORTICLIENT_RPM..."
                        sudo yum localinstall "$FORTICLIENT_RPM"
                        if [[ $? -ne 0 ]]; then
                            print_error "FortiClient VPN installation failed."
                            print_error "Please check the RPM file path and try again."
                            exit 1
                        fi
                        print_success "FortiClient VPN installed successfully."
                    else
                        print_info "Installation skipped. Exiting."
                        exit 1
                    fi
                else
                    print_error "FortiClient RPM file not found at '$FORTICLIENT_RPM'."
                    print_error "Please update FORTICLIENT_RPM variable in '$CONFIG_FILE'."
                    exit 1
                fi
            fi
            ;;
        "ubuntu")
            install_forticlient_ubuntu || exit 1
            ;;
    esac
    
    # 检查expect包
    if ! command -v expect &> /dev/null; then
        print_error "'expect' package is required for automated interactions, but it's not installed."
        case "$system_type" in
            "rhel")
                print_error "Please install it using: sudo yum install expect"
                ;;
            "ubuntu")
                print_error "Please install it using: sudo apt-get install expect"
                ;;
        esac
        exit 1
    fi
    
    print_success "FortiClient VPN and expect are installed."
}

# 获取 VPN 连接状态
get_vpn_status() {
    local status_output=$(forticlient vpn status 2>/dev/null)
    if echo "$status_output" | grep -q "Connected"; then
        echo "connected"
    else
        echo "disconnected"
    fi
}

# 生成用于连接的 expect 脚本
generate_connect_expect_script() {
    cat <<EOF >"$EXPECT_CONNECT_SCRIPT_PATH"
#!/usr/bin/expect -f

set timeout $CONNECT_TIMEOUT
set profile_name [lindex \$argv 0]
set username [lindex \$argv 1]
set password [lindex \$argv 2]
set token [lindex \$argv 3]

spawn forticlient vpn connect \$profile_name -u \$username

expect {
    timeout { send_user "Timeout waiting for Password prompt.\n"; exit 1 }
    "Password:" { send "\$password\\r" }
    eof { send_user "EOF before Password prompt.\n"; exit 1 }
}
expect {
    timeout { send_user "Timeout waiting for Confirm prompt.\n"; exit 1 }
    "Confirm (y/n)" { send "y\\r" }
    eof { send_user "EOF before Confirm prompt.\n"; exit 1 }
}
expect {
    timeout { send_user "Timeout waiting for Token prompt.\n"; exit 1 }
    "Token:" { send "\$token\\r" }
    eof { send_user "EOF before Token prompt.\n"; exit 1 }
}
expect {
    timeout { send_user "Timeout waiting for connection result.\n"; exit 1 }
    "Status: Connected" { send_user "VPN connected successfully.\n"; expect eof; exit 0 }
    "Error:" { send_user "Connection Error: \$expect_out(buffer)\n"; expect eof; exit 1 }
    eof { send_user "EOF after prompts, assuming connection failed or state unknown.\n"; exit 1 }
}
EOF
    chmod +x "$EXPECT_CONNECT_SCRIPT_PATH"
}

# 生成用于编辑 VPN Profile 的 expect 脚本
generate_edit_expect_script() {
    cat <<EOF >"$EXPECT_EDIT_SCRIPT_PATH"
#!/usr/bin/expect -f

# 设置更长的超时时间，以适应不同网络环境
set timeout 30

# 获取参数
set profile_name [lindex \$argv 0]
set vpn_type [lindex \$argv 1]
set remote_gateway [lindex \$argv 2]
set port [lindex \$argv 3]
set auth_method [lindex \$argv 4]
set cert_type [lindex \$argv 5]

# 错误处理函数
proc handle_error {msg} {
    send_user "Error: \$msg\\n"
    exit 1
}

# 通用expect处理函数
proc expect_and_respond {patterns response {timeout_msg "Timeout waiting for prompt"}} {
    global timeout
    expect {
        -re "\[Ee]rror:.*" {
            handle_error "Received error: \$expect_out(0,string)"
        }
        -re "Operation cancelled|Cancelled" {
            handle_error "Operation was cancelled"
        }
        -re "Profile.*exists" {
            # 如果配置文件已存在，继续编辑
            exp_continue
        }
        timeout {
            handle_error \$timeout_msg
        }
        -re \$patterns {
            send "\$response\\r"
        }
        eof {
            # 某些情况下正常退出也会收到EOF
            if {[string length \$response] == 0} {
                return
            }
            handle_error "Unexpected EOF while waiting for prompt"
        }
    }
}

# 启动编辑命令
spawn forticlient vpn edit \$profile_name

# 处理VPN类型选择
expect_and_respond {[Tt]ype.*(?:1.*SSL|2.*IPsec).*|Select VPN type} \$vpn_type "Timeout waiting for Type selection"

# 处理远程网关
expect_and_respond {[Rr]emote [Gg]ateway.*|[Ss]erver.*|[Hh]ost.*} \$remote_gateway "Timeout waiting for Remote Gateway"

# 处理端口
expect_and_respond {[Pp]ort.*|[Pp]ort number.*} \$port "Timeout waiting for Port"

# 处理认证方法
expect_and_respond {[Aa]uthentication.*|[Aa]uth.*[Mm]ethod.*} \$auth_method "Timeout waiting for Authentication method"

# 处理证书类型
expect_and_respond {[Cc]ertificate [Tt]ype.*|[Cc]ert.*[Tt]ype.*} \$cert_type "Timeout waiting for Certificate Type"

# 等待保存确认或自动保存
expect {
    timeout {
        # 某些版本可能不显示保存确认，视为成功
        send_user "No explicit save confirmation received, assuming success\\n"
        exit 0
    }
    -re {[Ss]av(e|ed|ing)} {
        send_user "Profile saved successfully\\n"
        exit 0
    }
    -re "\[Ee]rror:.*" {
        handle_error "Error during save: \$expect_out(0,string)"
    }
    eof {
        # 某些版本在保存后直接退出
        send_user "Profile configuration completed\\n"
        exit 0
    }
}
EOF
    chmod +x "$EXPECT_EDIT_SCRIPT_PATH"
}


# 配置 VPN Profile (新增)
configure_vpn_profile() {
    print_info "Attempting to configure VPN profile '$VPN_PROFILE_NAME'..."

    generate_edit_expect_script
    if [[ ! -x "$EXPECT_EDIT_SCRIPT_PATH" ]]; then
        print_error "Failed to create or make executable edit expect script at '$EXPECT_EDIT_SCRIPT_PATH'."
        return 1
    fi

    # 调用 expect 脚本进行配置
    "$EXPECT_EDIT_SCRIPT_PATH" \
        "$VPN_PROFILE_NAME" \
        "$VPN_EDIT_TYPE" \
        "$VPN_HOST" \
        "$VPN_PORT" \
        "$VPN_EDIT_AUTH_METHOD" \
        "$VPN_EDIT_CERT_TYPE"

    local expect_exit_code=$?
    rm -f "$EXPECT_EDIT_SCRIPT_PATH"

    if [[ $expect_exit_code -eq 0 ]]; then
        print_success "VPN profile '$VPN_PROFILE_NAME' configured successfully (or was already correct)."
        return 0
    else
        print_error "Failed to configure VPN profile '$VPN_PROFILE_NAME' (Expect script exited with code $expect_exit_code)."
        return 1
    fi
}

# 检查 VPN 配置文件是否存在，如果不存在则尝试配置
check_and_configure_vpn_profile() {
    print_info "Checking VPN profile '$VPN_PROFILE_NAME' using 'forticlient vpn view'..."
    # 'forticlient vpn view <profile>' might return an error or empty if not found.
    # Let's use 'list' as it's more reliable for presence check.
    if ! forticlient vpn list | grep -qw "$VPN_PROFILE_NAME"; then
        print_warning "VPN profile '$VPN_PROFILE_NAME' not found."
        read -p "$(print_color "$COLOR_YELLOW" "Do you want to attempt to configure it now? (y/n): ")" confirm_config
        if [[ "$confirm_config" == "y" || "$confirm_config" == "Y" ]]; then
            configure_vpn_profile
            # 再次检查配置是否成功
            if ! forticlient vpn list | grep -qw "$VPN_PROFILE_NAME"; then
                print_error "Configuration of '$VPN_PROFILE_NAME' failed or was not completed."
                return 1
            else
                print_success "VPN profile '$VPN_PROFILE_NAME' is now configured."
                return 0
            fi
        else
            print_info "Configuration skipped by user."
            return 1 # Indicate profile is not ready
        fi
    else
        print_success "VPN profile '$VPN_PROFILE_NAME' already exists."
        return 0
    fi
}

# connect_vpn (使用新的连接 expect 脚本)
connect_vpn() {
    local current_status=$(get_vpn_status)
    if [[ "$current_status" == "connected" ]]; then
        print_warning "VPN is already connected. Disconnecting first for a fresh connection."
        disconnect_vpn || return 1
        print_info "Waiting for disconnection..."
        sleep 3
    fi

    print_info "Attempting to connect to VPN profile '$VPN_PROFILE_NAME'..."

    local pass_to_use="$VPN_PASSWORD"
    local token_to_use=""

    if [[ -z "$pass_to_use" ]]; then
        printf "${COLOR_YELLOW}Please enter VPN password for ${VPN_USERNAME}: ${COLOR_NC}"
        read -s pass_to_use
        echo ""
    fi

    printf "${COLOR_YELLOW}Please enter VPN Token for ${VPN_USERNAME}: ${COLOR_NC}"
    read -s token_to_use
    echo ""

    generate_connect_expect_script # 使用连接专用的 expect 脚本
    if [[ ! -x "$EXPECT_CONNECT_SCRIPT_PATH" ]]; then
        print_error "Failed to create or make executable connect expect script at '$EXPECT_CONNECT_SCRIPT_PATH'."
        return 1
    fi

    "$EXPECT_CONNECT_SCRIPT_PATH" "$VPN_PROFILE_NAME" "$VPN_USERNAME" "$pass_to_use" "$token_to_use"
    local expect_exit_code=$?

    rm -f "$EXPECT_CONNECT_SCRIPT_PATH"
    unset pass_to_use token_to_use

    if [[ $expect_exit_code -ne 0 ]]; then
        print_error "VPN connection failed (Expect script exited with code $expect_exit_code)."
        return 1
    fi

    print_info "Waiting a few seconds for network interface to stabilize..."
    sleep 5
    print_info "Final check: Current VPN status: $(get_vpn_status)"
    if [[ "$(get_vpn_status)" == "connected" ]]; then
        print_success "VPN connected successfully."
        return 0
    else
        print_error "VPN connection status is still 'disconnected' after attempted connection."
        print_error "Please check your credentials, network, or server logs."
        return 1
    fi
}

# 断开 VPN
disconnect_vpn() {
    print_info "Attempting to disconnect VPN profile '$VPN_PROFILE_NAME'..."
    forticlient vpn disconnect "$VPN_PROFILE_NAME"
    if [[ $? -ne 0 ]]; then
        print_error "VPN disconnection command failed."
        return 1
    fi
    print_success "VPN disconnected."
    return 0
}

# 重连 VPN
reconnect_vpn() {
    disconnect_vpn && connect_vpn
}

# 查看 VPN 信息
view_vpn_info() {
    print_info "--- VPN Profile Information for '$VPN_PROFILE_NAME' ---"
    echo "FortiClient Version: $(forticlient --version 2>/dev/null || echo "N/A")"
    echo "Configured VPN Host: $VPN_HOST:$VPN_PORT"
    echo "Configured Username: $VPN_USERNAME"
    echo "VPN Profile Name: $VPN_PROFILE_NAME"
    echo ""
    print_info "FortiClient's stored profile details (if available):"
    forticlient vpn info "$VPN_PROFILE_NAME" 2>/dev/null || print_warning "Could not retrieve detailed info from FortiClient CLI."
    print_info "--- End of VPN Info ---"
}


# --- 主逻辑 ---

if [[ $# -eq 0 ]]; then
    display_usage
    exit 1
fi

ACTION="$1"

load_config
check_forticlient_install # 确保 forticlient 和 expect 已安装

# 对于需要 VPN Profile 的操作，先检查并尝试配置
case "$ACTION" in
    connect|disconnect|reconnect|status|info|configure)
        if ! check_and_configure_vpn_profile; then
            if [[ "$ACTION" != "configure" ]]; then # 如果不是主动配置命令，则因profile问题退出
                 print_error "VPN profile '$VPN_PROFILE_NAME' is not configured. Please run '$0 configure' or configure manually."
                 exit 1
            fi
        fi
        ;;
esac


case "$ACTION" in
    connect)
        connect_vpn
        ;;
    disconnect)
        disconnect_vpn
        ;;
    reconnect)
        reconnect_vpn
        ;;
    status)
        current_vpn_status=$(get_vpn_status)
        if [[ "$current_vpn_status" == "connected" ]]; then
            print_success "Current VPN status: Connected"
        else
            print_warning "Current VPN status: Disconnected"
        fi
        ;;
    info)
        view_vpn_info
        ;;
    configure)
        # check_and_configure_vpn_profile 已经在上面处理了
        # 如果到这里，说明用户可能想强制重新检查/配置，或者首次配置
        # 如果上面配置失败，check_and_configure_vpn_profile 会返回非零
        # 我们可以在这里再次调用 configure_vpn_profile 如果需要
        print_info "Configure action completed (or profile already existed)."
        ;;
    *)
        display_usage
        exit 1
        ;;
esac

exit 0