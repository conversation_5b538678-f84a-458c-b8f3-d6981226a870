# VPN 服务器地址 (例如: vpn.example.com 或 **********)
VPN_HOST="sslvpn.int.ybm100.com"

# VPN 服务器端口 (可选，默认为 443)
VPN_PORT="4433"

# VPN 用户名
VPN_USERNAME="caojialin"

# VPN 密码 (不推荐存储在这里，不安全！如果留空，脚本会提示您输入)
# 例如：VPN_PASSWORD="your_vpn_password"
VPN_PASSWORD=""

# --- FortiClient VPN Profile 'xyy' 编辑参数 ---
# Type (1.SSL VPN / 2.IPsec VPN) [default=1]:
VPN_EDIT_TYPE="1" # "1" for SSL VPN, "2" for IPsec VPN

# Authentication (1.prompt / 2.save / 3.disable) [default=1]:
# "1" for prompt, "2" for save (不推荐保存密码), "3" for disable (例如，仅证书认证)
VPN_EDIT_AUTH_METHOD="1"

# Certificate Type (1.local (pkcs12) / 2.smartcard (pkcs11) / 3.disable) [current=disable]:
# "1" for local, "2" for smartcard, "3" for disable
VPN_EDIT_CERT_TYPE="3"

# FortiClient VPN RPM 包的完整路径
# 确保文件存在，并且您的用户有读取权限
FORTICLIENT_RPM="./forticlient_vpn_7.2.10.0974_x86_64.rpm"

# RPM包在系统中的名称 (用于检测是否安装，根据实际安装的包名调整)
# 您可以通过 'rpm -qa | grep forticlient' 查看
FORTICLIENT_RPM_INSTALLED_NAME="forticlient"

# VPN 配置文件的名称 (在 FortiClient 中显示的名称，例如 "xyy")
VPN_PROFILE_NAME="xyy"

#-------------------------------------------------------------------------------
# 高级选项 (通常不需要修改)
# 连接超时时间 (秒)
CONNECT_TIMEOUT=30