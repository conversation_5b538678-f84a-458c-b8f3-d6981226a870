# LocalRedissonClient 使用说明

## 概述

LocalRedissonClient 是基于 LocalRedisTemplate 设计模式创建的本地化 RedissonClient 实现，为本地开发环境提供了一个不依赖外部 Redis 服务的 RedissonClient 替代方案。

## 设计特点

### 1. 遵循 LocalRedisTemplate 设计模式
- **继承原有接口**：实现 RedissonClient 接口，保持 API 兼容性
- **内存+持久化双重存储**：使用 ConcurrentHashMap 作为内存缓存，LocalKvStoreMapper 作为持久化存储
- **过期时间管理**：使用 expirationMap 管理 key 的过期时间
- **操作类封装**：提供 LocalRLock、LocalRBucket、LocalRMap 等操作类
- **配置类支持**：通过 @Profile("local") 在本地环境启用

### 2. 核心功能实现
- **分布式锁**：LocalRLock 基于 ReentrantLock 实现
- **分布式对象**：LocalRBucket 提供键值存储
- **分布式集合**：LocalRList、LocalRMap、LocalRSet、LocalRQueue
- **原子操作**：LocalRAtomicLong 提供原子长整型操作
- **键管理**：LocalRKeys 提供键的管理和查询功能

## 配置使用

### 1. 自动配置
在 `LocalRedisConfig` 中已经配置了 LocalRedissonClient Bean：

```java
@Bean
@Primary
@Profile("local")
public RedissonClient redissonClient() {
    return new LocalRedissonClient();
}
```

### 2. 激活本地环境
在 `application-local.yml` 或启动参数中设置：
```yaml
spring:
  profiles:
    active: local
```

或者启动时添加参数：
```bash
--spring.profiles.active=local
```

## 使用示例

### 1. 分布式锁使用
```java
@Autowired
private RedissonClient redissonClient;

public void distributedLockExample() {
    RLock lock = redissonClient.getLock("my-lock");
    
    try {
        // 尝试加锁，最多等待10秒，锁定后30秒自动解锁
        if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
            try {
                // 执行业务逻辑
                System.out.println("获得锁，执行业务逻辑");
            } finally {
                lock.unlock();
            }
        }
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
    }
}
```

### 2. 分布式对象存储
```java
public void bucketExample() {
    RBucket<String> bucket = redissonClient.getBucket("my-bucket");
    
    // 设置值
    bucket.set("Hello World");
    
    // 设置带过期时间的值
    bucket.set("Expire Value", 10, TimeUnit.MINUTES);
    
    // 获取值
    String value = bucket.get();
    
    // 原子操作
    String oldValue = bucket.getAndSet("New Value");
    
    // 条件设置
    boolean success = bucket.trySet("Try Set Value");
}
```

### 3. 分布式Map使用
```java
public void mapExample() {
    RMap<String, Object> map = redissonClient.getMap("my-map");
    
    // 基本操作
    map.put("key1", "value1");
    map.put("key2", 123);
    
    // 批量操作
    Map<String, Object> batch = new HashMap<>();
    batch.put("key3", "value3");
    batch.put("key4", "value4");
    map.putAll(batch);
    
    // 获取所有数据
    Map<String, Object> allData = map.readAllMap();
    
    // 原子操作
    Object oldValue = map.putIfAbsent("key5", "value5");
}
```

### 4. 分布式List使用
```java
public void listExample() {
    RList<String> list = redissonClient.getList("my-list");
    
    // 添加元素
    list.add("item1");
    list.add("item2");
    list.add(0, "first-item"); // 在指定位置插入
    
    // 获取元素
    String first = list.get(0);
    List<String> range = list.range(0, 2);
    
    // 删除元素
    list.remove("item1");
    list.remove(0); // 按索引删除
    
    // 获取所有数据
    List<String> allItems = list.readAll();
}
```

### 5. 分布式Set使用
```java
public void setExample() {
    RSet<String> set = redissonClient.getSet("my-set");
    
    // 添加元素
    set.add("element1");
    set.add("element2");
    
    // 检查存在
    boolean exists = set.contains("element1");
    
    // 随机获取元素
    String randomElement = set.random();
    Set<String> randomElements = set.random(2);
    
    // 随机删除元素
    String removedElement = set.removeRandom();
    
    // 获取所有数据
    Set<String> allElements = set.readAll();
}
```

### 6. 分布式Queue使用
```java
public void queueExample() {
    RQueue<String> queue = redissonClient.getQueue("my-queue");
    
    // 入队
    queue.offer("task1");
    queue.offer("task2");
    queue.offer("task3");
    
    // 出队
    String task = queue.poll(); // 返回并删除队首元素
    
    // 查看队首元素（不删除）
    String peek = queue.peek();
    
    // 获取所有数据
    List<String> allTasks = queue.readAll();
}
```

### 7. 原子操作使用
```java
public void atomicLongExample() {
    RAtomicLong atomicLong = redissonClient.getAtomicLong("my-counter");
    
    // 设置初始值
    atomicLong.set(0);
    
    // 原子递增
    long newValue = atomicLong.incrementAndGet();
    
    // 原子加法
    long result = atomicLong.addAndGet(10);
    
    // 比较并设置
    boolean success = atomicLong.compareAndSet(result, 100);
    
    // 获取当前值
    long currentValue = atomicLong.get();
}
```

### 8. 键管理使用
```java
public void keysExample() {
    RKeys keys = redissonClient.getKeys();
    
    // 获取所有键
    Iterable<String> allKeys = keys.getKeys();
    
    // 模式匹配查找键
    Collection<String> matchedKeys = keys.findKeysByPattern("user:*");
    
    // 删除键
    long deletedCount = keys.delete("key1", "key2", "key3");
    
    // 按模式删除键
    long deletedByPattern = keys.deleteByPattern("temp:*");
    
    // 设置过期时间
    boolean success = keys.expire("my-key", 10, TimeUnit.MINUTES);
    
    // 获取剩余生存时间
    long ttl = keys.remainTimeToLive("my-key");
}
```

## 支持的功能

### ✅ 已实现的功能
- RLock（分布式锁）
- RBucket（键值存储）
- RMap（分布式Map）
- RList（分布式List）
- RSet（分布式Set）
- RQueue（分布式Queue）
- RAtomicLong（原子长整型）
- RKeys（键管理）
- 过期时间管理
- 异步操作（基于CompletableFuture）

### ❌ 未实现的功能
- RReadWriteLock（读写锁）
- RCountDownLatch（倒计时锁存器）
- RSemaphore（信号量）
- RTopic（发布订阅）
- RScript（Lua脚本）
- RTransaction（事务）
- RBatch（批处理）
- 集群相关功能

## 注意事项

1. **仅用于本地开发**：LocalRedissonClient 仅适用于本地开发环境，不应在生产环境使用
2. **数据持久化**：数据会同时存储在内存和数据库中（如果配置了LocalKvStoreMapper）
3. **过期清理**：过期数据会在访问时自动清理，但不会主动清理
4. **线程安全**：使用了线程安全的数据结构，支持并发访问
5. **功能限制**：某些高级功能（如Lua脚本、事务等）未实现，会抛出UnsupportedOperationException

## 与真实RedissonClient的差异

1. **网络通信**：LocalRedissonClient 不涉及网络通信，所有操作都在本地内存中进行
2. **分布式特性**：真正的分布式特性（如跨JVM的锁）无法实现，但API保持一致
3. **性能特征**：内存操作性能更高，但缺少Redis的持久化和集群特性
4. **数据一致性**：在单JVM内保证一致性，但无法跨JVM保证
5. **功能完整性**：实现了核心功能，但某些高级功能未实现

## 总结

LocalRedissonClient 为本地开发提供了一个轻量级的 RedissonClient 实现，让开发者可以在不依赖外部 Redis 服务的情况下进行开发和测试。它遵循了 LocalRedisTemplate 的设计模式，保持了 API 的兼容性，同时提供了必要的功能支持。
