# Controller接口权限整理

## 商品模块 (saas-inquiry-product)

### ProductInfoController - 商品基本信息
- **路径前缀**: `/product/info`
- **权限列表**:
  - `POST /create` → `saas:product:info:create` (创建商品基本信息)
  - `PUT /update` → `saas:product:info:update` (更新商品基本信息)
  - `DELETE /delete` → `saas:product:info:delete` (删除商品)
  - `GET /get` → `saas:product:info:query` (获得商品基本信息)
  - `GET /page` → `saas:product:info:query` (获得商品基本信息分页)
  - `GET /export-excel` → `saas:product:info:export` (导出商品基本信息Excel)

### ProductStdlibController - 商品标准库信息
- **路径前缀**: `/product/stdlib`
- **权限列表**:
  - `GET /mixed-page` → `saas:product:info:query` (查询混合标准库商品信息分页)
  - `GET /self-page` → `saas:product:stdlib:query` (查询自建标准库商品信息分页)
  - `GET /get` → `saas:product:stdlib:query` (获得标准库商品信息)
  - `POST /sync` → `saas:product:stdlib:sync` (同步标准库商品)
  - `POST /update` → `saas:product:stdlib:update` (修改标准库商品)

### ProductStdlibSyncController - 商品标准库信息同步
- **路径前缀**: `/product/stdlib/sync`
- **权限列表**:
  - `GET /full` → `saas:product:stdlib:sync:create` (全量同步中台标准库数据)

### ProductPresentController - 商品提报
- **路径前缀**: `/product/present`
- **权限列表**:
  - `GET /self-by-barcode` → `saas:product:present:query` (根据条形码查询自建标准库商品)
  - `GET /page` → `saas:product:present:query` (获得商品提报分页)
  - `GET /get` → `saas:product:present:query` (获得商品提报详情)

### ProductUnbundledController - 拆零商品
- **路径前缀**: `/product/unbundled`
- **权限列表**:
  - `POST /create` → `saas:product:unbundled:create` (创建拆零商品)

### ProductRecycleController - 商品回收站
- **路径前缀**: `/product/recycle`
- **权限列表**:
  - `PUT /restore` → `saas:product:recycle:restore` (恢复商品)
  - `DELETE /remove` → `saas:product:recycle:remove` (永久删除商品)
  - `GET /page` → `saas:product:recycle:query` (获得商品回收站分页)
  - `GET /export-excel` → `saas:product:recycle:export` (导出商品回收站Excel)

### ProductCategoryController - 商品六级分类
- **路径前缀**: `/product/category`
- **权限列表**:
  - `POST /create` → `saas:product:category:create` (创建商品六级分类)
  - `PUT /update` → `saas:product:category:update` (更新商品六级分类)
  - `DELETE /delete` → `saas:product:category:delete` (删除商品六级分类)
  - `GET /get` → `saas:product:category:query` (获得商品六级分类)
  - `GET /page` → `saas:product:category:query` (获得商品六级分类分页)
  - `GET /tree` → `saas:product:category:query` (获得商品分类树)
  - `GET /export-excel` → `saas:product:category:export` (导出商品六级分类Excel)

### ProductQualityChangeRecordController - 质量变更申请操作记录
- **路径前缀**: `/product/quality-change`
- **权限列表**:
  - `POST /save` → `saas:product:quality-change:create` (暂存/提交质量变更申请)

### CatalogController - 目录
- **路径前缀**: `/product/catalog`
- **权限列表**:
  - `POST /create` → `saas:product:catalog:create` (创建目录)
  - `PUT /update` → `saas:product:catalog:update` (更新目录)
  - `DELETE /delete` → `saas:product:catalog:delete` (删除目录)
  - `GET /get` → `saas:product:catalog:query` (获得目录)
  - `GET /page` → `saas:product:catalog:query` (获得目录分页)
  - `GET /page-version` → `saas:product:catalog:query` (获得目录分页)
  - `POST /page-relation-tenant` → `saas:product:catalog:query` (获得目录关联的租户)

### RegulatoryCatalogDetailController - 监管目录药品信息
- **路径前缀**: `/product/regulatory-catalog-detail`
- **权限列表**:
  - `GET /regulatory-catalog-detail/list-by-catalog-id` → `saas:product:catalog:query` (获得监管目录明细列表)
  - `GET /export-all-data-excel` → `saas:product:catalog:export` (导出目录Excel)

## 用户系统模块 (saas-inquiry-user)

### MenuController - 菜单
- **路径前缀**: `/system/menu`
- **权限列表**:
  - `POST /create` → `system:menu:create` (创建菜单)
  - `PUT /update` → `system:menu:update` (修改菜单)
  - `DELETE /delete` → `system:menu:delete` (删除菜单)
  - `GET /get` → `system:menu:query` (获取菜单信息)

### RoleController - 角色
- **路径前缀**: `/system/role`
- **权限列表**:
  - `POST /create` → `system:role:create` (创建角色)
  - `PUT /update` → `system:role:update` (修改角色)
  - `DELETE /delete` → `system:role:delete` (删除角色)
  - `GET /get` → `system:role:query` (获得角色)
  - `GET /page` → `system:role:query` (获得角色分页)
  - `GET /export-excel` → `system:role:export` (导出角色Excel)

### PermissionController - 权限
- **路径前缀**: `/system/permission`
- **权限列表**:
  - `GET /list-role-menus` → `system:permission:assign-role-menu` (获得角色拥有的菜单编号)
  - `POST /assign-role-menu` → `system:permission:assign-role-menu` (赋予角色菜单)
  - `GET /list-role-data-scope` → `system:permission:assign-role-data-scope` (获得角色拥有的数据权限)
  - `POST /assign-role-data-scope` → `system:permission:assign-role-data-scope` (赋予角色数据权限)
  - `GET /list-user-roles` → `system:permission:assign-user-role` (获得用户拥有的角色编号)
  - `POST /assign-user-role` → `system:permission:assign-user-role` (赋予用户角色)

### UserController - 用户
- **路径前缀**: `/system/user`
- **权限列表**:
  - `POST /create` → `system:user:create` (新增用户)
  - `PUT /update` → `system:user:update` (修改用户)
  - `DELETE /delete` → `system:user:delete` (删除用户)
  - `PUT /update-password` → `system:user:update-password` (重置用户密码)
  - `PUT /update-status` → `system:user:update` (修改用户状态)
  - `GET /get` → `system:user:query` (获得用户)
  - `GET /page` → `system:user:list` (获得用户分页列表)
  - `GET /export-excel` → `system:user:export` (导出用户Excel)
  - `GET /system/page` → `system:user:list` (超管-获得用户分页列表)

### DeptController - 部门
- **路径前缀**: `/system/dept`
- **权限列表**:
  - `POST /create` → `system:dept:create` (创建部门)
  - `PUT /update` → `system:dept:update` (修改部门)
  - `DELETE /delete` → `system:dept:delete` (删除部门)
  - `GET /get` → `system:dept:query` (获得部门信息)

### DictTypeController - 字典类型
- **路径前缀**: `/system/dict-type`
- **权限列表**:
  - `POST /create` → `system:dict:create` (新增字典类型)
  - `PUT /update` → `system:dict:update` (修改字典类型)
  - `DELETE /delete` → `system:dict:delete` (删除字典类型)
  - `GET /get` → `system:dict:query` (查询字典类型详细)
  - `GET /page` → `system:dict:query` (获得字典类型分页)
  - `GET /export-excel` → `system:dict:export` (导出字典类型Excel)

### DictDataController - 字典数据
- **路径前缀**: `/system/dict-data`
- **权限列表**:
  - `POST /create` → `system:dict:create` (新增字典数据)
  - `PUT /update` → `system:dict:update` (修改字典数据)
  - `DELETE /delete` → `system:dict:delete` (删除字典数据)
  - `GET /get` → `system:dict:query` (查询字典数据详细)
  - `GET /page` → `system:dict:query` (获得字典数据分页)
  - `GET /export-excel` → `system:dict:export` (导出字典数据Excel)

### OperateLogController - 操作日志
- **路径前缀**: `/system/operate-log`
- **权限列表**:
  - `GET /page` → `system:operate-log:query` 或 `system:operate-log:log` (查看操作日志分页列表)

## 药师模块 (saas-inquiry-pharmacist)

### AppInquiryPrescriptionAuditController - 处方审核核心接口
- **路径前缀**: `/admin-api/kernel/pharmacist/prescription-audit` 或 `/app-api/kernel/pharmacist/prescription-audit`
- **权限列表**:
  - `GET /wait-receive-count` → `@ss.hasRole('pharmacist')` (获取当前药师待审核处方数量)
  - `GET /receive-prescription` → `@ss.hasRole('pharmacist')` (领取一个待审核的处方)
  - `GET /audit-timeout` → `@ss.hasRole('pharmacist')` (获取处方超时时间)
  - `POST /audit-pass` → `@ss.hasRole('pharmacist')` (审核通过)
  - `POST /audit-reject` → `@ss.hasRole('pharmacist')` (审核驳回)
  - `GET /page` → `@ss.hasRole('pharmacist')` (获得处方审核记录分页)

### AppInquiryPharmacistController - 药师基础信息
- **路径前缀**: `/admin-api/kernel/pharmacist/inquiry-pharmacist` 或 `/app-api/kernel/pharmacist/inquiry-pharmacist`
- **权限列表**:
  - `PUT /get-info` → `@ss.hasRole('pharmacist')` (药师信息查询接口)

### InquiryPharmacistSyncController - 药师信息
- **路径前缀**: `/pharmacist/inquiry-pharmacist`
- **权限列表**:
  - `GET /page-sync` → `saas:inquiry-pharmacist:query` (获得旧系统药师信息分页)

## 医院模块 (saas-inquiry-hospital)

### PrescriptionController - 处方开具核心接口
- **路径前缀**: `/admin-api/kernel/hospital/prescription` 或 `/app-api/kernel/hospital/prescription`
- **权限列表**:
  - `PUT /grabbing-prescription` → `@ss.hasRole('doctor')` (医生抢处方)

## 传输模块 (saas-transmitter)

### TransmissionOrganController - 医药行业行政机构
- **路径前缀**: `/admin-api/transmitter/transmission-organ` 或 `/app-api/transmitter/transmission-organ`
- **权限列表**:
  - `POST /create` → `saas:transmission-organ:create` (创建医药行业行政机构)

### TransmissionOrganDictController - 服务商字典
- **路径前缀**: `/transmitter/transmission-organ-dict`
- **权限列表**:
  - `POST /create` → `saas:transmission-provider-dict:create` (创建服务商字典)

## 无权限要求的接口

以下接口无需特殊权限：
- 地区相关接口 (AreaController, AppAreaController)
- 商品搜索接口 (AppInquiryProductSearchController)
- 商品提报APP接口 (AppProductPresentController)
- 患者查询接口 (InquiryPatientController, InquiryRecordQueryController)
- 医院接诊大厅接口 (ReceptionAreaController)
- 认证相关接口 (AuthController)
- 角色列表接口 (AppRoleController)

## 权限命名规范

从扫描结果可以看出，权限命名遵循以下规范：
- **系统模块**: `system:模块:操作` (如: `system:user:create`)
- **业务模块**: `saas:模块:子模块:操作` (如: `saas:product:info:create`)
- **角色权限**: `@ss.hasRole('角色名')` (如: `@ss.hasRole('pharmacist')`)

## 操作类型

常见的操作类型包括：
- `create` - 创建
- `update` - 更新
- `delete` - 删除
- `query` - 查询
- `export` - 导出
- `sync` - 同步
- `restore` - 恢复
- `remove` - 移除
