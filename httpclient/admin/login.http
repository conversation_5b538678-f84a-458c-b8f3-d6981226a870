### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "15926351000",
  "password": "Abc123456"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 2.查商品信息 - 西药
GET {{baseAppKernelUrl}}/kernel/drugstore/base-info/get-inquiry-ma-qrcode
Content-Type: application/json
Authorization: Bearer {{token}}


### ding
GET {{baseAdminKernelUrl}}/kernel/drugstore/qrcode/get-or-create-inquiry
Authorization: Bearer {{token}}


