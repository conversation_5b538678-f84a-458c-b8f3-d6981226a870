import App from './App';
import "./components/z-loading/animation.css"
import {
	createSSRApp
} from 'vue';
import uvUI from '@climblee/uv-ui'
import {
	setupPinia
} from './sheep/store';
import NoticeBox from "@/components/NoticeBox/index.vue"
import uviewPlus from 'uview-plus';
import "./styles/index.css"
export function createApp() {

	const app = createSSRApp(App);
	// ============uviewPlus
	app.use(uviewPlus);
	// ============uvUI	
	app.use(uvUI);
	// ============通知	
	app.use(NoticeBox);
	setupPinia(app);


	return {
		app,
	};
}