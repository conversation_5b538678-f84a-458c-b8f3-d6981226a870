{"name": "荷叶问诊", "appid": "__UNI__6AE0A23", "description": "基于 uni-app + Vue3 技术驱动的在线商城系统，内含诸多功能与丰富的活动，期待您的使用和反馈。", "versionName": "1.1.2", "versionCode": 112, "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "nvueLaunchMode": "fast", "splashscreen": {"alwaysShowBeforeRender": false, "waiting": false, "autoclose": false, "delay": 0}, "safearea": {"bottom": {"offset": "none"}}, "modules": {"Payment": {}, "Share": {}, "VideoPlayer": {}, "OAuth": {}, "Camera": {}, "Record": {}, "Barcode": {}, "Push": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.GET_TASKS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.READ_SMS\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.SEND_SMS\"/>", "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>", "<uses-permission android:name=\"android.permission.RECEIVE_USER_PRESENT\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/> "], "minSdkVersion": 21, "schemes": "shopro", "targetSdkVersion": 30, "abiFilters": ["arm64-v8a"]}, "ios": {"permissions": ["camera"], "urlschemewhitelist": ["b<PERSON><PERSON><PERSON>", "iosamap"], "dSYMs": false, "plistcmds": ["Set :NSMicrophoneUsageDescription 获取语音权限以进行语音输入", "Set :NSPhotoLibraryUsageDescription 从相册选择图片作为用户头像", "Set :NSCameraUsageDescription 用户使用相机拍摄图片作为自定义头像", "Set :NSPhotoLibraryAddUsageDescription 保存图片到相册"], "privacyDescription": {"NSMicrophoneUsageDescription": "需要同意访问您的摄像头才能完善该条目", "NSCameraUsageDescription": "需要同意访问您的摄像头拍摄照片才能完善该条目", "NSPhotoLibraryUsageDescription": "需要同意访问您的相册选取图片才能完善该条目", "NSPhotoLibraryAddUsageDescription": "需要同意访问您的相册才能保存该图片", "NSUserTrackingUsageDescription": "开启追踪并不会获取您在其它站点的隐私信息,该行为仅用于标识设备,保障服务安全和提升浏览体验"}, "urltypes": "shopro", "capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:shopro.sheepjs.com"]}}, "ios_bundle_id": "com.ybm100.inquiry", "CFBundleDisplayName": "荷叶问诊", "idfa": true}, "sdkConfigs": {"speech": {}, "ad": {}, "oauth": {"weixin": {"appid": "wxae7a0c156da9383b", "UniversalLinks": "https://shopro.sheepjs.com/uni-universallinks/__UNI__082C0BA/"}}, "payment": {"weixin": {"__platform__": ["ios", "android"], "appid": "wxae7a0c156da9383b", "UniversalLinks": "https://shopro.sheepjs.com/uni-universallinks/__UNI__082C0BA/"}, "alipay": {"__platform__": ["ios", "android"]}}, "share": {"weixin": {"appid": "wxae7a0c156da9383b", "UniversalLinks": "https://shopro.sheepjs.com/uni-universallinks/__UNI__082C0BA/"}}, "push": {}}, "orientation": ["portrait-primary"], "splashscreen": {"androidStyle": "default", "iosStyle": "storyboard", "useOriginalMsgbox": true, "android": {"hdpi": "static/screen/splash_screen_1.5x.png", "xhdpi": "static/screen/splash_screen_1.5x.png", "xxhdpi": "static/screen/splash_screen_1.5x.png"}, "ios": {"iphone": {"portrait-896h@3x": "static/screen/splash_screen_1.5x.png", "landscape-896h@3x": "static/screen/splash_screen_1.5x.png", "portrait-896h@2x": "static/screen/splash_screen_1.5x.png", "landscape-896h@2x": "static/screen/splash_screen_1.5x.png", "iphonex": "static/screen/splash_screen_1.5x.png", "iphonexl": "static/screen/splash_screen_1.5x.png", "retina55": "static/screen/splash_screen_1.5x.png", "retina55l": "static/screen/splash_screen_1.5x.png", "retina47": "static/screen/splash_screen_1.5x.png", "retina47l": "static/screen/splash_screen_1.5x.png", "retina40": "static/screen/splash_screen_1.5x.png", "retina40l": "static/screen/splash_screen_1.5x.png", "retina35": "static/screen/splash_screen_1.5x.png"}, "ipad": {"portrait-1366h@2x": "static/screen/splash_screen_1.5x.png", "landscape-1366h@2x": "static/screen/splash_screen_1.5x.png", "portrait-1194h@2x": "static/screen/splash_screen_1.5x.png", "landscape-1194h@2x": "static/screen/splash_screen_1.5x.png", "portrait-1112h@2x": "static/screen/splash_screen_1.5x.png", "landscape-1112h@2x": "static/screen/splash_screen_1.5x.png", "portrait-retina7": "static/screen/splash_screen_1.5x.png", "landscape-retina7": "static/screen/splash_screen_1.5x.png", "portrait7": "static/screen/splash_screen_1.5x.png", "landscape7": "static/screen/splash_screen_1.5x.png"}, "storyboard": "static/screen/CustomStoryboard.zip"}}, "icons": {"android": {"hdpi": "static/icons/72x72.png", "xhdpi": "static/icons/96x96.png", "xxhdpi": "static/icons/144x144.png", "xxxhdpi": "static/icons/192x192.png"}, "ios": {"appstore": "static/icons/1024x1024.png", "ipad": {"app": "static/icons/76x76.png", "app@2x": "static/icons/152x152.png", "notification": "static/icons/20x20.png", "notification@2x": "static/icons/40x40.png", "proapp@2x": "static/icons/167x167.png", "settings": "static/icons/29x29.png", "settings@2x": "static/icons/58x58.png", "spotlight": "static/icons/40x40.png", "spotlight@2x": "static/icons/80x80.png"}, "iphone": {"app@2x": "static/icons/120x120.png", "app@3x": "static/icons/180x180.png", "notification@2x": "static/icons/40x40.png", "notification@3x": "static/icons/60x60.png", "settings@2x": "static/icons/58x58.png", "settings@3x": "static/icons/87x87.png", "spotlight@2x": "static/icons/80x80.png", "spotlight@3x": "static/icons/120x120.png"}}}}, "nativePlugins": {"TRTCCloudUniPlugin-TRTCCloudImpl": {"__plugin_info__": {"name": "【官方】腾讯云实时音视频SDK", "description": "uni-app TRTC SDK 是腾讯云实时音视频通讯解决方案在 uni-app 上的 SDK，提供实时音视频服务", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=7774", "android_package_name": "", "ios_bundle_id": "", "isCloud": true, "bought": 1, "pid": "7774", "parameters": {}}}, "TencentCloud-TUICallKit": {"__plugin_info__": {"name": "【官方】腾讯云音视频通话插件TencentCloud-TUICallKit", "description": "TUICallKit 是腾讯云官方推出的音视频通话插件，支持 1v1 通话和群组通话，并提供“类微信\"的 UI 交互，开发者仅需三个 API 就可实现。", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=9035", "android_package_name": "", "ios_bundle_id": "", "isCloud": true, "bought": 1, "pid": "9035", "parameters": {}}}, "DCloud-PrivactAlert": {"__plugin_info__": {"name": "privact<PERSON><PERSON><PERSON>", "description": "示例插件", "platforms": "Android,iOS", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}, "Aliyun-Push": {"阿里云移动推送Android AppKey": "335547902", "阿里云移动推送Android AppSecret": "360c08906c5446a8a308184faa4505f5", "阿里云移动推送iOS AppKey": "335547905", "阿里云移动推送iOS AppSecret": "a019565de00743358c385109ed0882c6", "__plugin_info__": {"name": "阿里云移动推送", "description": "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=7628", "android_package_name": "com.ybm100.inquiry", "ios_bundle_id": "com.ybm100.inquiry1", "isCloud": true, "bought": 1, "pid": "7628", "parameters": {"阿里云移动推送Android AppKey": {"des": "阿里云EMAS移动应用标识", "key": "", "value": ""}, "阿里云移动推送Android AppSecret": {"des": "阿里云EMAS移动应用密钥", "key": "", "value": ""}, "阿里云移动推送iOS AppKey": {"des": "阿里云EMAS移动应用标识", "key": "aliyun:push:app<PERSON>ey", "value": ""}, "阿里云移动推送iOS AppSecret": {"des": "阿里云EMAS移动应用密钥", "key": "aliyun:push:appSecret", "value": ""}}}}, "Aliyun-ThirdPush": {"com.gcm.push.apiKey": "", "com.gcm.push.applicationid": "", "com.gcm.push.projectid": "", "com.gcm.push.sendid": "", "com.hihonor.push.app_id": "", "com.huawei.hms.client.appid": "113835851", "com.meizu.push.id": "", "com.meizu.push.key": "", "com.oppo.push.key": "1716b5165ea84325a39c3de0a87f2b64", "com.oppo.push.secret": "d7f25629e8424502a1fe17cb6a8e4e70", "com.vivo.push.api_key": "18d38ccb60ca1660ee7cf3f0412d87b4", "com.vivo.push.app_id": "105874975", "com.xiaomi.push.id": "2882303761520396127", "com.xiaomi.push.key": "5222039612127", "__plugin_info__": {"name": "阿里云移动推送-厂商通道", "description": "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。厂商通道是使用手机厂商提供的", "platforms": "Android", "url": "https://ext.dcloud.net.cn/plugin?id=7629", "android_package_name": "com.ybm100.inquiry", "ios_bundle_id": "com.ybm100.inquiry1", "isCloud": true, "bought": 1, "pid": "7629", "parameters": {"com.gcm.push.apiKey": {"des": "gcm推送apiKey", "key": "", "value": ""}, "com.gcm.push.applicationid": {"des": "gcm推送applicationId", "key": "", "value": ""}, "com.gcm.push.projectid": {"des": "gcm推送projectid", "key": "", "value": ""}, "com.gcm.push.sendid": {"des": "gcm推送sendId", "key": "", "value": ""}, "com.hihonor.push.app_id": {"des": "荣耀推送AppId", "key": "", "value": ""}, "com.huawei.hms.client.appid": {"des": "华为推送AppId", "key": "", "value": ""}, "com.meizu.push.id": {"des": "魅族推送ID", "key": "", "value": ""}, "com.meizu.push.key": {"des": "魅族推送Key", "key": "", "value": ""}, "com.oppo.push.key": {"des": "Oppo推送Key", "key": "", "value": ""}, "com.oppo.push.secret": {"des": "Oppo推送密钥", "key": "", "value": ""}, "com.vivo.push.api_key": {"des": "vivo推送Api Key", "key": "", "value": ""}, "com.vivo.push.app_id": {"des": "vivo推送App Id", "key": "", "value": ""}, "com.xiaomi.push.id": {"des": "小米推送ID", "key": "", "value": ""}, "com.xiaomi.push.key": {"des": "小米推送Key", "key": "", "value": ""}}}}}, "kernel": {"ios": "WKWebview", "recovery": "reload"}}, "quickapp": {}, "quickapp-native": {"icon": "/static/logo.png", "package": "com.example.demo", "features": [{"name": "system.clipboard"}]}, "quickapp-webview": {"icon": "/static/logo.png", "package": "com.example.demo", "minPlatformVersion": 1070, "versionName": "1.0.0", "versionCode": 100}, "mp-weixin": {"appid": "wxd1425c818778593e", "setting": {"urlCheck": false, "minified": true, "postcss": false, "es6": false}, "optimization": {"subPackages": true}, "plugins": {}, "lazyCodeLoading": "requiredComponents", "mergeVirtualHostAttributes": true, "usingComponents": {}, "permission": {}, "requiredPrivateInfos": ["<PERSON><PERSON><PERSON><PERSON>"]}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "mp-jd": {"usingComponents": true}, "h5": {"template": "index.html", "router": {"mode": "history", "base": "./"}, "sdkConfigs": {"maps": {}}, "async": {"timeout": 20000}, "title": "荷叶问诊", "optimization": {"treeShaking": {"enable": true}}}, "network": {"permission": [{"domain": "http://************", "always": true}]}, "vueVersion": "3", "_spaceID": "192b4892-5452-4e1d-9f09-eee1ece40639", "locale": "zh-Hans", "fallbackLocale": "zh-Hans"}