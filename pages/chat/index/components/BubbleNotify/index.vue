<template>
	<view class="bubble-notify">
		<view class="time">
			{{formatDate(time)}}
		</view>
		<view class="content mg-t-lg">
			{{content}}
		</view>

	</view>
</template>

<script>
	export default {
		props: {
			time: {
				type: String,
				default: ""
			},
			content: {
				type: String,
				default: ""
			}
		},
		methods: {
			formatDate(date) {

				var date = new Date(date);

				var YY = date.getFullYear() + '-';

				var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';

				var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());

				var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';

				var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';

				var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());

				return YY + MM + DD + " " + hh + mm + ss;

			}
		}
	}
</script>

<style lang="scss">
	.time {
		font-weight: 400;
		font-size: 28rpx;
		color: #888888;
		text-align: center;
	}

	.content {
		width: 608rpx;
		height: 54rpx;
		background: #DADFE4;
		border-radius: 8px;
		line-height: 54rpx;
		font-weight: 400;
		font-size: 24rpx;
		color: #222222;
		text-align: center;
		margin: 30rpx auto;
	}
</style>