<!-- 评价信息 -->
<template>
	<div class="evaluate">
		<div class='msg msg-recipe'>
			<div class='content'>
				<div class='head evalutate-card-wrap'>
					<div style='text-align: center;margin-bottom: 8px'>{{evalutateCardType==1||showView?'本次服务已结束':'请您对医生的本次服务进行评价'}}</div>
					<div class='head-tit-wrap ss-flex ss-align-center'>
						<image :src='doctorHeadImageUrl' class='evalutate-card-head'></image>
						<div class='doc-des'>
							<div><span class='name'>{{rowData.cloudCustomData.evaluateInfo.name}}</span><span
									class='pos'>{{rowData.cloudCustomData.evaluateInfo.titleName}}</span></div>
							<div class='num'>
								{{ rowData.cloudCustomData.evaluateInfo.hospital + ' ' + rowData.cloudCustomData.evaluateInfo.office }}
							</div>
						</div>
					</div>
					<div class="env-btn" @click="$emit('showPopup',evalutateCardType)">
						{{evalutateCardType==1||showView||selfIdentity=='physician' ? '查看评价' : '立即评价'}}
					</div>
				</div>
			</div>
		</div>


	</div>
</template>

<script>
	import dayjs from "dayjs"
	export default {
		props: {
			rowData: {
				type: Object,
				default: () => {}
			},

			doctorHeadImageUrl: {
				type: String,
				default: ""
			},	selfIdentity: {
				type: String,
				default: ""
			},
			evalutateCardType: {
				type: Number,
				default: 0
			},
		},
		
		watch: {
			evalutateCardType(newV) {
				console.info(newV)
			}
		},
		methods: {
			setShowView() {
				this.showView=true
			},
			openPopup(){
		
				this.$emit('showPopup',this.evalutateCardType)
			}
		},
		data() {
			return {
				dayjs: dayjs,
				showView: false
			}
		}
	}
</script>

<style scoped lang="scss">
	.evaluate {
		width:100%;
	}

	.msg {
		display: flex;
		// padding: 15px;

		&-recipe {
			// padding: 15px;

			.content {
				background-color: #fff;
				border-radius: 8px;
				color: #c3c3c5;
				font-size: 12px;
				flex: 1;

				.head {
					padding: 15px;

					.head-tit {
						display: flex;
						justify-content: space-between;

						>.h3 {
							font-weight: normal;
							font-size: 16px;
							color: #121826;
						}
					}

					p {
						margin-top: 5px;
					}
				}

				.foot {
					background-color: #eaf8f6;
					color: #16c2a3;
					font-size: 16px;
					text-align: center;
					height: 42px;
					line-height: 42px;
				}
			}
		}
	}


	.evalutate-card-wrap {
		background-image: linear-gradient(179deg, #C8F4DE 0%, #FFFFFF 24%);
		border-radius: 6px;
		overflow: hidden;
		font-weight: 500;
		font-size: 16px;
		color: #222222;

		.head-tit-wrap {
			margin-bottom: 12px;
			padding: 10px;
			background: #F0F2F5;
			border-radius: 4px;

			.evalutate-card-head {
				width: 40px;
				height: 40px;
				border-radius: 50%;
				border: 0.5px solid #CFCFCF;
				margin-right: 6px;
			}

			.doc-des {
				.name {
					font-weight: 600;
					font-size: 16px;
					color: #222222;
				}

				.pos {
					font-weight: 500;
					font-size: 14px;
					color: #222222;
					margin-left: 6px;
				}

				.num {
					font-weight: 400;
					font-size: 14px;
					color: #222222;
				}
			}
		}

		.env-btn {
			width: 145px;
			height: 38px;
			line-height: 38px;
			background: #00B955;
			border-radius: 19px;
			font-weight: 400;
			font-size: 16px;
			color: #FFFFFF;
			text-align: center;
			margin: 0 auto;
		}
	}
</style>