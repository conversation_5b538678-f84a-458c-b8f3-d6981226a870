package com.xyy.saas.inquiry.drugstore.server.dal.mysql.option;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionConfigPageReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问诊配置选项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryOptionConfigMapper extends BaseMapperX<InquiryOptionConfigDO> {

    default PageResult<InquiryOptionConfigDO> selectPage(InquiryOptionConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryOptionConfigDO>()
            .eqIfPresent(InquiryOptionConfigDO::getTargetType, reqVO.getTargetType())
            .eqIfPresent(InquiryOptionConfigDO::getOptionType, reqVO.getOptionType())
            .eqIfPresent(InquiryOptionConfigDO::getTargetId, reqVO.getTargetId())
            .inIfPresent(InquiryOptionConfigDO::getTargetId, reqVO.getTargetIds())
            .likeIfPresent(InquiryOptionConfigDO::getTargetName, reqVO.getTargetName())
            .eqIfPresent(InquiryOptionConfigDO::getProvince, reqVO.getProvince())
            .eqIfPresent(InquiryOptionConfigDO::getProvinceCode, reqVO.getProvinceCode())
            .eqIfPresent(InquiryOptionConfigDO::getCity, reqVO.getCity())
            .eqIfPresent(InquiryOptionConfigDO::getCityCode, reqVO.getCityCode())
            .eqIfPresent(InquiryOptionConfigDO::getUsed, reqVO.getUsed())
            .eqIfPresent(InquiryOptionConfigDO::getArea, reqVO.getArea())
            .eqIfPresent(InquiryOptionConfigDO::getAreaCode, reqVO.getAreaCode())
            .betweenIfPresent(InquiryOptionConfigDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryOptionConfigDO::getId));
    }

    default List<InquiryOptionConfigDO> queryList(InquiryOptionConfigQueryDto dto) {
        return selectList(getQueryWrapper(dto));
    }

    private static LambdaQueryWrapperX<InquiryOptionConfigDO> getQueryWrapper(InquiryOptionConfigQueryDto dto) {
        return new LambdaQueryWrapperX<InquiryOptionConfigDO>()
            .eqIfPresent(InquiryOptionConfigDO::getTargetType, dto.getTargetType())
            .inIfPresent(InquiryOptionConfigDO::getOptionType, dto.getOptionTypeList())
            .eqIfPresent(InquiryOptionConfigDO::getTargetId, dto.getTargetId())
            .inIfPresent(InquiryOptionConfigDO::getTargetId, dto.getTargetIds())
            .likeIfPresent(InquiryOptionConfigDO::getTargetId, dto.getTargetName())
            .eqIfPresent(InquiryOptionConfigDO::getProvince, dto.getProvince())
            .eqIfPresent(InquiryOptionConfigDO::getProvinceCode, dto.getProvinceCode())
            .eqIfPresent(InquiryOptionConfigDO::getCity, dto.getCity())
            .eqIfPresent(InquiryOptionConfigDO::getCityCode, dto.getCityCode())
            .eqIfPresent(InquiryOptionConfigDO::getArea, dto.getArea())
            .eqIfPresent(InquiryOptionConfigDO::getAreaCode, dto.getAreaCode())
            .orderByDesc(InquiryOptionConfigDO::getId);
    }

    default InquiryOptionConfigDO queryOne(InquiryOptionConfigQueryDto dto) {
        return selectOne(getQueryWrapper(dto), false);
    }

    int insertOrUpdateBatch(@Param("list") List<InquiryOptionConfigDO> list);


    List<InquiryOptionConfigDO> queryAllByTenantAndOptionType(@Param("tenant") TenantDto tenant, @Param("optionTypeList") List<Integer> optionTypeList);

}