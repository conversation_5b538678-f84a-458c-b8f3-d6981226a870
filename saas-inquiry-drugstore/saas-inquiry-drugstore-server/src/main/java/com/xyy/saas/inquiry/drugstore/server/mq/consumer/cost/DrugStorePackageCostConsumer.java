package com.xyy.saas.inquiry.drugstore.server.mq.consumer.cost;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantPackageCostConvert;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostService;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageCostSaveEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> {@link cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationServiceImpl#createTenantPackageRelation}
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_drugstore_server_mq_consumer_cost_DrugStorePackageCostConsumer",
    topic = TenantPackageCostSaveEvent.TOPIC)
public class DrugStorePackageCostConsumer {

    public static final String GROUP_ID = DrugStorePackageCostConsumer.class.getName();

    @Resource
    private TenantPackageCostService tenantPackageCostService;

    /**
     * init 或 updateStatus
     *
     * @param inquiryRecordCreateEvent 额度事件
     */
    @EventBusListener
    public void receiveDrugStorePackageCost(TenantPackageCostSaveEvent inquiryRecordCreateEvent) {
        TenantPackageCostDto packageCostDto = TenantPackageCostConvert.INSTANCE.convertCostDto(inquiryRecordCreateEvent.getMsg());
        // 状态变更
        if (packageCostDto.isUpdateStatus()) {
            TenantUtils.execute(packageCostDto.getTenantId(), () -> tenantPackageCostService.updateTenantPackageCostStatus(packageCostDto));
            return;
        }
        tenantPackageCostService.saveTenantPackageCost(packageCostDto);
    }


}
