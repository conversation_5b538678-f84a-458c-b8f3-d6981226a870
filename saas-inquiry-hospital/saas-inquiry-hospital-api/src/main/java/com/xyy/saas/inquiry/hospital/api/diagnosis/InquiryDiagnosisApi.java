package com.xyy.saas.inquiry.hospital.api.diagnosis;

import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationDto;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationReqDto;

import java.util.List;

/**
 * 诊断服务api
 *
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/09/30 10:45
 */
public interface InquiryDiagnosisApi {

    /**
     * 根据条件查询关联诊断科室
     *
     * @param req
     * @return 关联诊断科室
     */
    List<InquiryDiagnosisDepartmentRelationDto> queryDiagnosisDepartmentRelation(
        InquiryDiagnosisDepartmentRelationReqDto req);

}
