package com.xyy.saas.inquiry.hospital.api.prescription.dto;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryPrescriptionQueryDTO implements Serializable {

    @Schema(description = "处方ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27739")
    private Long id;

    @Schema(description = "租户ID", example = "1517")
    private Long tenantId;

    @Schema(description = "租户IDs", example = "1517")
    private List<Long> tenantIds;

    @Schema(description = "处方编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    // @NotNull(message = "处方编码不能为空")
    private String pref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    private String patientName;

    @Schema(description = "问诊单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private String inquiryPref;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    private String doctorPref;

    @Schema(description = "药师编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "20472")
    private String pharmacistPref;

    @Schema(description = "处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    /**
     * {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    @Schema(description = "当前审方人类型 1-医生,2-药店,3-平台,4-医院", example = "2")
    private Integer auditorType;

    @Schema(description = "处方分发状态 0-未分配,1-已分配", example = "2")
    private Integer distributeStatus;

    @Schema(description = "处方分配的用户id", example = "2")
    private Long distributeUserId;

    @Schema(description = "用药类型：0西药，1中药", example = "2")
    private Integer medicineType;

    @Schema(description = "用药类型：0西药，1中药", example = "2")
    private List<Integer> medicineTypes;

    @Schema(description = "使用状态：0 初始 1可用 2已用 3过期  4失效", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer useStatus;

    @Schema(description = "失效时间")
    private LocalDateTime invalidTime;

    @Schema(description = "医师出方时间")
    private LocalDateTime[] outPrescriptionTime;

    @Schema(description = "药师审方时间")
    private LocalDateTime[] auditPrescriptionTime;

    @Schema(description = "处方打印状态（0-未打印、1-已打印、NULL -未知）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer printStatus;

    @Schema(description = "签章平台 0-自绘 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer signPlatform;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "当前页号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageNum;

    @Schema(description = "页大小", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageSize;

}
