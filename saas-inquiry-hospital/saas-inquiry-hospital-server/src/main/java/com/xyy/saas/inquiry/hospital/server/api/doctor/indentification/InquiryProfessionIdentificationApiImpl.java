package com.xyy.saas.inquiry.hospital.server.api.doctor.indentification;

import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.enums.user.CertificateTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.hospital.api.doctor.indentification.InquiryProfessionIdentificationApi;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryProfessionIdentificationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:42
 */
// @Service
@DubboService
@Slf4j
public class InquiryProfessionIdentificationApiImpl implements InquiryProfessionIdentificationApi {

    @Resource
    private InquiryProfessionIdentificationService inquiryProfessionIdentificationService;


    @Override
    public void saveProfessionIdentifications(List<InquiryProfessionIdentificationDto> professionIdentifications) {
        inquiryProfessionIdentificationService.saveProfessionIdentifications(professionIdentifications);
    }


    @Override
    public List<InquiryProfessionIdentificationDto> getProfessionIdentifications(Long personId, DoctorTypeEnum doctorTypeEnum) {
        return inquiryProfessionIdentificationService.getInquiryProfessionIdentification(personId, doctorTypeEnum);
    }

    @Override
    public InquiryProfessionIdentificationDto getProfessionIdentifications(Long personId, DoctorTypeEnum doctorTypeEnum, CertificateTypeEnum certificateTypeEnum) {
        return inquiryProfessionIdentificationService.getProfessionIdentifications(personId, doctorTypeEnum, certificateTypeEnum);
    }

    @Override
    public void deleteByGuidType(Long personId, DoctorTypeEnum doctorTypeEnum) {
        inquiryProfessionIdentificationService.deleteInquiryProfessionIdentification(personId, doctorTypeEnum);
    }
}
