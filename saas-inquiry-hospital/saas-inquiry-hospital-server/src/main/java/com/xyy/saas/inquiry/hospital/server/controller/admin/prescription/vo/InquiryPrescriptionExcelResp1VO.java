package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "管理后台 - 处方记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryPrescriptionExcelResp1VO extends InquiryPrescriptionExcelRespVO {

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    @ExcelProperty(value = "提交门店", index = 17)
    private String tenantName;

}
