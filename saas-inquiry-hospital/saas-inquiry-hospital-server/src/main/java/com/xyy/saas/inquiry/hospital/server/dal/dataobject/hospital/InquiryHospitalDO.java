package com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 医院信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_hospital")
@KeySequence("saas_inquiry_hospital_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryHospitalDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医院编码
     */
    private String pref;
    /**
     * 医院名称
     */
    private String name;

    /**
     * 医疗机构编码
     */
    private String institutionCode;
    /**
     * 医院等级
     */
    private Integer level;
    /**
     * 医院地址
     */
    private String address;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 电子邮件
     */
    private String email;
    /**
     * 官方网站
     */
    private String website;
    /**
     * 是否有医保资质
     */
    private Integer hasMedicare;

    /**
     * 是否禁用
     */
    private Integer disable;

}