package com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.medical.MedicalInsuranceOrderExtDto;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 医保订单信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_medical_insurance_order", autoResultMap = true)
@KeySequence("saas_medical_insurance_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalInsuranceOrderDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医保订单编号
     */
    private String pref;
    /**
     * 业务id
     */
    private String bizId;
    /**
     * 业务类型 0-问诊,1-智慧脸...
     */
    private Integer bizType;
    /**
     * 门店ID
     */
    private Long tenantId;
    /**
     * 定点机构编号
     */
    private String fixMedicalInstitutionsCode;
    /**
     * 结算ID
     */
    private String setlId;
    /**
     * 就诊ID
     */
    private String medicalId;
    /**
     * 人员编号
     */
    private String psnNo;
    /**
     * 人员姓名
     */
    private String psnName;
    /**
     * 人员证件类型
     */
    private String psnCertType;
    /**
     * 证件号码
     */
    private String certNo;
    /**
     * 性别：1 男 2 女
     */
    private Integer sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 险种类型
     */
    private String insuranceType;
    /**
     * 人员类别
     */
    private String psnType;
    /**
     * 公务员标志
     */
    private String cvlServFlag;
    /**
     * 结算时间
     */
    private LocalDateTime setlTime;
    /**
     * 就诊凭证类型
     */
    private String medicalCertType;
    /**
     * 医疗类别
     */
    private String medType;
    /**
     * 医药机构结算ID
     */
    private String medicalInstitutionsSetlId;
    /**
     * 清算经办机构
     */
    private String clrOptions;
    /**
     * 清算方式
     */
    private String clrWay;
    /**
     * 清算类别
     */
    private String clrType;
    /**
     * 本次交易后的账户余额
     */
    private BigDecimal balance;
    /**
     * 医疗费总额
     */
    private BigDecimal medicalFeeSumAmt;
    /**
     * 个人负担总金额
     */
    private BigDecimal psnPartAmt;
    /**
     * 基金支付总额
     */
    private BigDecimal fundPaySumAmt;
    /**
     * 其他支出
     */
    private BigDecimal othPay;
    /**
     * 医院负担金额
     */
    private BigDecimal hospPartAmt;
    /**
     * 个人账户支出
     */
    private BigDecimal acctPay;
    /**
     * 个人现金支出
     */
    private BigDecimal psnCashPay;
    /**
     * 个人账户共济支付金额
     */
    private BigDecimal acctMutualAidPay;
    /**
     * 医保订单扩展字段
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private MedicalInsuranceOrderExtDto ext;

}