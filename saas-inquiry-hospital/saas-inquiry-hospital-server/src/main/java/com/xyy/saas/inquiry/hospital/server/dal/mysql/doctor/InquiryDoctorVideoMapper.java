package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorVideoDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医生录屏记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryDoctorVideoMapper extends BaseMapperX<InquiryDoctorVideoDO> {


    default List<InquiryDoctorVideoDO> selectListByCondition(InquiryDoctorVideoPageReqVO reqVO) {
        return selectList(getQueryWrapper(reqVO));
    }

    default PageResult<InquiryDoctorVideoDO> selectPage(InquiryDoctorVideoPageReqVO reqVO) {
        return selectPage(reqVO, getQueryWrapper(reqVO));
    }

    default LambdaQueryWrapperX<InquiryDoctorVideoDO> getQueryWrapper(InquiryDoctorVideoPageReqVO reqVO) {
        return new LambdaQueryWrapperX<InquiryDoctorVideoDO>()
            .eqIfPresent(InquiryDoctorVideoDO::getPref, reqVO.getPref())
            .eqIfPresent(InquiryDoctorVideoDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(InquiryDoctorVideoDO::getVideoUrl, reqVO.getVideoUrl())
            .eqIfPresent(InquiryDoctorVideoDO::getMd5, reqVO.getMd5())
            .eqIfPresent(InquiryDoctorVideoDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(InquiryDoctorVideoDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryDoctorVideoDO::getId);
    }

}