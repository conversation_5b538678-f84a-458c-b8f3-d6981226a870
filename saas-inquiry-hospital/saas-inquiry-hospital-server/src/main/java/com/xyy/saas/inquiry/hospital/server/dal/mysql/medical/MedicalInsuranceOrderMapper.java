package com.xyy.saas.inquiry.hospital.server.dal.mysql.medical;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicalInsuranceOrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医保订单信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicalInsuranceOrderMapper extends BaseMapperX<MedicalInsuranceOrderDO> {

    default PageResult<MedicalInsuranceOrderDO> selectPage(MedicalInsuranceOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MedicalInsuranceOrderDO>()
            .eqIfPresent(MedicalInsuranceOrderDO::getPref, reqVO.getPref())
            .eqIfPresent(MedicalInsuranceOrderDO::getBizId, reqVO.getBizId())
            .eqIfPresent(MedicalInsuranceOrderDO::getBizType, reqVO.getBizType())
            .eqIfPresent(MedicalInsuranceOrderDO::getFixMedicalInstitutionsCode, reqVO.getFixMedicalInstitutionsCode())
            .eqIfPresent(MedicalInsuranceOrderDO::getSetlId, reqVO.getSetlId())
            .eqIfPresent(MedicalInsuranceOrderDO::getMedicalId, reqVO.getMedicalId())
            .eqIfPresent(MedicalInsuranceOrderDO::getPsnNo, reqVO.getPsnNo())
            .likeIfPresent(MedicalInsuranceOrderDO::getPsnName, reqVO.getPsnName())
            .eqIfPresent(MedicalInsuranceOrderDO::getPsnCertType, reqVO.getPsnCertType())
            .eqIfPresent(MedicalInsuranceOrderDO::getCertNo, reqVO.getCertNo())
            .eqIfPresent(MedicalInsuranceOrderDO::getSex, reqVO.getSex())
            .eqIfPresent(MedicalInsuranceOrderDO::getAge, reqVO.getAge())
            .eqIfPresent(MedicalInsuranceOrderDO::getInsuranceType, reqVO.getInsuranceType())
            .eqIfPresent(MedicalInsuranceOrderDO::getPsnType, reqVO.getPsnType())
            .eqIfPresent(MedicalInsuranceOrderDO::getCvlServFlag, reqVO.getCvlServFlag())
            .betweenIfPresent(MedicalInsuranceOrderDO::getSetlTime, reqVO.getSetlTime())
            .eqIfPresent(MedicalInsuranceOrderDO::getMedicalCertType, reqVO.getMedicalCertType())
            .eqIfPresent(MedicalInsuranceOrderDO::getMedType, reqVO.getMedType())
            .eqIfPresent(MedicalInsuranceOrderDO::getMedicalInstitutionsSetlId, reqVO.getMedicalInstitutionsSetlId())
            .eqIfPresent(MedicalInsuranceOrderDO::getClrOptions, reqVO.getClrOptions())
            .eqIfPresent(MedicalInsuranceOrderDO::getClrWay, reqVO.getClrWay())
            .eqIfPresent(MedicalInsuranceOrderDO::getClrType, reqVO.getClrType())
            .eqIfPresent(MedicalInsuranceOrderDO::getBalance, reqVO.getBalance())
            .eqIfPresent(MedicalInsuranceOrderDO::getMedicalFeeSumAmt, reqVO.getMedicalFeeSumAmt())
            .eqIfPresent(MedicalInsuranceOrderDO::getPsnPartAmt, reqVO.getPsnPartAmt())
            .eqIfPresent(MedicalInsuranceOrderDO::getFundPaySumAmt, reqVO.getFundPaySumAmt())
            .eqIfPresent(MedicalInsuranceOrderDO::getOthPay, reqVO.getOthPay())
            .eqIfPresent(MedicalInsuranceOrderDO::getHospPartAmt, reqVO.getHospPartAmt())
            .eqIfPresent(MedicalInsuranceOrderDO::getAcctPay, reqVO.getAcctPay())
            .eqIfPresent(MedicalInsuranceOrderDO::getPsnCashPay, reqVO.getPsnCashPay())
            .eqIfPresent(MedicalInsuranceOrderDO::getAcctMutualAidPay, reqVO.getAcctMutualAidPay())
            .eqIfPresent(MedicalInsuranceOrderDO::getExt, reqVO.getExt())
            .betweenIfPresent(MedicalInsuranceOrderDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(MedicalInsuranceOrderDO::getId));
    }

}