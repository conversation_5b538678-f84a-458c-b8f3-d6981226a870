package com.xyy.saas.inquiry.hospital.server.service.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorFilingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorFilingSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import jakarta.validation.Valid;


/**
 * 医生备案信息 Service 接口
 *
 * <AUTHOR>
 */
public interface DoctorFilingService {

    /**
     * 创建医生备案信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDoctorFiling(@Valid DoctorFilingSaveReqVO createReqVO);

    /**
     * 更新医生备案信息
     *
     * @param updateReqVO 更新信息
     */
    void updateDoctorFiling(@Valid DoctorFilingSaveReqVO updateReqVO);

    /**
     * 删除医生备案信息
     *
     * @param id 编号
     */
    void deleteDoctorFiling(Long id);

    /**
     * 获得医生备案信息
     *
     * @param id 编号
     * @return 医生备案信息
     */
    DoctorFilingDO getDoctorFiling(Long id);

    /**
     * 获得医生备案信息分页
     *
     * @param pageReqVO 分页查询
     * @return 医生备案信息分页
     */
    PageResult<DoctorFilingDO> getDoctorFilingPage(DoctorFilingPageReqVO pageReqVO);

}