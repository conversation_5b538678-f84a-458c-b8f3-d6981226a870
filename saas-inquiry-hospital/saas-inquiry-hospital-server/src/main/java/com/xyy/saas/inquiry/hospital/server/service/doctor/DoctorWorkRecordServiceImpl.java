package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorWorkRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorWorkRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_WORK_RECORD_NOT_EXISTS;


/**
 * 医生工作履历记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorWorkRecordServiceImpl implements DoctorWorkRecordService {

    @Resource
    private DoctorWorkRecordMapper doctorWorkRecordMapper;

    @Override
    public Long createDoctorWorkRecord(DoctorWorkRecordSaveReqVO createReqVO) {
        // 插入
        DoctorWorkRecordDO doctorWorkRecord = BeanUtils.toBean(createReqVO, DoctorWorkRecordDO.class);
        doctorWorkRecordMapper.insert(doctorWorkRecord);
        // 返回
        return doctorWorkRecord.getId();
    }

    @Override
    public void updateDoctorWorkRecord(DoctorWorkRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateDoctorWorkRecordExists(updateReqVO.getId());
        // 更新
        DoctorWorkRecordDO updateObj = BeanUtils.toBean(updateReqVO, DoctorWorkRecordDO.class);
        doctorWorkRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteDoctorWorkRecord(Long id) {
        // 校验存在
        validateDoctorWorkRecordExists(id);
        // 删除
        doctorWorkRecordMapper.deleteById(id);
    }

    private void validateDoctorWorkRecordExists(Long id) {
        if (doctorWorkRecordMapper.selectById(id) == null) {
            throw exception(DOCTOR_WORK_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public DoctorWorkRecordDO getDoctorWorkRecord(Long id) {
        return doctorWorkRecordMapper.selectById(id);
    }

    @Override
    public PageResult<DoctorWorkRecordDO> getDoctorWorkRecordPage(DoctorWorkRecordPageReqVO pageReqVO) {
        return doctorWorkRecordMapper.selectPage(pageReqVO);
    }

}