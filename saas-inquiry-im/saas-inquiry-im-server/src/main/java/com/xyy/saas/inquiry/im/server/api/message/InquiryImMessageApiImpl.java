package com.xyy.saas.inquiry.im.server.api.message;

import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/11/28 16:02
 * @Description: 消息相关API实现
 */
// @Service
@DubboService
public class InquiryImMessageApiImpl implements InquiryImMessageApi {

    @Resource
    @Lazy
    private InquiryImMessageService inquiryImMessageService;

    /**
     * 发送常规用户消息
     *
     * @param messageDto 消息对象
     * @return boolean
     */
    @Override
    public Boolean sendUserMessage(InquiryImMessageDto messageDto) {
        return inquiryImMessageService.sendUserMessage(messageDto);
    }

    /**
     * 发送系统通知消息
     * @param messageDto 消息对象
     * @return
     */
    @Override
    public Boolean sendSystemMessage(InquiryImMessageDto messageDto) {
        return inquiryImMessageService.sendSystemMessage(messageDto);
    }

    /**
     * 批量发送系统通知消息
     * @param messageDto 消息对象
     * @return
     */
    @Override
    public Boolean batchSendSystemMessage(InquiryImMessageDto messageDto) {
        return inquiryImMessageService.batchSendSystemMessage(messageDto);
    }


    /**
     * 批量插入消息表（不同步IM）
     *
     * @param messageDtos 消息对象集合
     * @return 插入结果
     */
    @Override
    public Boolean batchInsertMessage(List<InquiryImMessageDto> messageDtos) {
        return inquiryImMessageService.batchInsertMessage(messageDtos);
    }

    /**
     * 批量获取问诊的最后一条消息
     *
     * @param inquiryPrefs 问诊单列表
     * @return 最后一条消息集合
     */
    @Override
    public List<InquiryLastMessageDto> getLastMessage(List<String> inquiryPrefs) {
        return inquiryImMessageService.getLastMessageByInquiryPrefs(inquiryPrefs);
    }
}
