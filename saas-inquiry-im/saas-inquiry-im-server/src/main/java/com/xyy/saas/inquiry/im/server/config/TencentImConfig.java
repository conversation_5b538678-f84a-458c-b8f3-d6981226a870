package com.xyy.saas.inquiry.im.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @ClassName：TencentConfig
 * @Author: xucao
 * @Date: 2024/11/27 13:49
 * @Description: 腾讯IM配置
 */
@Data
@ConfigurationProperties(prefix = "tencent.im")
public class TencentImConfig {
    /**
     * SDK App ID
     */
    private long sdkAppId;
    /**
     * IM 应用秘钥
     */
    private String secretKey;
    /**
     * 签名过期时间，单位秒，默认7天
     */
    private long expire;
    /**
     * IM服务调用域名
     */
    private String domain;

    /**
     * 管理员用户ID
     */
    private String adminUserId;
    /**
     * API配置
     */
    private Api api;

    // Getter and Setter methods

    // Nested class for API configuration
    @Data
    public static class Api {
        /**
         * 创建用户API
         */
        private String createUser;
        /**
         * 发送消息API
         */
        private String sendMessage;

        /**
         * 批量发送消息API
         */
        private String sendBatchMessage;

        // Getter and Setter methods

    }
}
