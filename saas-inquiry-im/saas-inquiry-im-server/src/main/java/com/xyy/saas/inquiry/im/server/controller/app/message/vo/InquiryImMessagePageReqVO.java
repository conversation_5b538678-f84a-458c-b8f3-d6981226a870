package com.xyy.saas.inquiry.im.server.controller.app.message.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 腾讯IM用户消息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class InquiryImMessagePageReqVO extends PageParam {

    @Schema(description = "消息唯一key")
    private String msgKey;

    @Schema(description = "问诊单号")
    private String inquiryPref;

    @Schema(description = "发送方IM用户名")
    private String msgFrom;

    @Schema(description = "接收方IM用户名")
    private String msgTo;

    @Schema(description = "消息发送时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] msgTime;

    @Schema(description = "消息已读状态 0-未读 1-已读", example = "1")
    private Integer readStatus;

    @Schema(description = "消息体内容")
    private String msgBody;

    @Schema(description = "客户端类型 0-app  1-pc  2-小程序", example = "2")
    private Integer clientChannelType;

    @Schema(description = "消息扩展内容")
    private String msgExt;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "IM账号列表")
    private List<String> accountList;

    @Schema(description = "用户id列表")
    private List<Long> userIdList;

}