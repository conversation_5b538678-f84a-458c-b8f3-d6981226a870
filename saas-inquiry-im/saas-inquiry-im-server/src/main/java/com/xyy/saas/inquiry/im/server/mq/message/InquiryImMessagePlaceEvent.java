package com.xyy.saas.inquiry.im.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: xucao
 * @DateTime: 2025/4/15 21:50
 * @Description: 问诊IM消息归档事件
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class InquiryImMessagePlaceEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "INQUIRY_IM_MESSAGE_PLACE";

    private String msg;


    @JsonCreator
    public InquiryImMessagePlaceEvent(@JsonProperty("msg") String msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }
}
