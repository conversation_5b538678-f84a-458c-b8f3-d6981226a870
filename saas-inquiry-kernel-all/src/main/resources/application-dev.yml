spring:
  cloud:
    nacos:
      server-addr: 172.20.7.26:8848
      discovery:
        namespace: dev
        group: http
      config:
        namespace: dev
        group: DEFAULT_GROUP
        server-addr: 172.20.7.26:8848
        prefix: ${spring.application.name}
        file-extension: yaml
        refresh-enabled: true
  config:
    import:
      - optional:nacos:${spring.application.name}-dev.yaml
      - optional:nacos:inquiry-global-dev.yaml



  datasource:
    dynamic:
      datasource:
        master:
          url: ************************************************************************************************************************************************************************************** # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ************************************************************************************************************************************************************************************** # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
  #  data:
  #    redis:
  #      host: ************** # 地址
  #      port: 6379 # 端口
  #      password: xj2023 # 密码，建议生产环境开启
  #      database: 0
  data:
    redis:
      host: db01-medicare-test.redis.ybm100.top # 地址
      port: 30002 # 端口
      password: JEf8CVrnY0G3RPEZ # 密码，建议生产环境开启
      database: 15

rocketmq:
#  name-server: **********:9876
  producer:
    group: saas_inquiry_kernel

#event:
#  bus:
#    append-profile: true
logging:
  level:
    com.baomidou.mybatisplus: debug
