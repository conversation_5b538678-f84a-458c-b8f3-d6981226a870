package com.xyy.saas.inquiry.hospital.server.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorStatusDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorStatusMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorStatusServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_STATUS_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;


/**
 * {@link InquiryDoctorStatusServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryDoctorStatusServiceImpl.class)
public class InquiryDoctorStatusServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryDoctorStatusServiceImpl inquiryDoctorStatusService;

    @Resource
    private InquiryDoctorStatusMapper inquiryDoctorStatusMapper;


    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateInquiryDoctorStatus_success() {
        // 准备参数
        InquiryDoctorStatusSaveReqVO createReqVO = randomPojo(InquiryDoctorStatusSaveReqVO.class);
        // 调用
        Long inquiryDoctorStatusId = inquiryDoctorStatusService.createInquiryDoctorStatus(createReqVO);
        // 断言
        assertNotNull(inquiryDoctorStatusId);
        // 校验记录的属性是否正确
        InquiryDoctorStatusDO inquiryDoctorStatus = inquiryDoctorStatusMapper.selectById(inquiryDoctorStatusId);
        assertPojoEquals(createReqVO, inquiryDoctorStatus, "id");
    }

    @Test
    public void testUpdateInquiryDoctorStatus_success() {
        // mock 数据
        InquiryDoctorStatusDO dbInquiryDoctorStatus = randomPojo(InquiryDoctorStatusDO.class);
        inquiryDoctorStatusMapper.insert(dbInquiryDoctorStatus);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryDoctorStatusSaveReqVO updateReqVO = randomPojo(InquiryDoctorStatusSaveReqVO.class);

        // 调用
        inquiryDoctorStatusService.updateInquiryDoctorStatus(updateReqVO);
    }

    @Test
    public void testUpdateInquiryDoctorStatus_notExists() {
        // 准备参数
        InquiryDoctorStatusSaveReqVO updateReqVO = randomPojo(InquiryDoctorStatusSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> inquiryDoctorStatusService.updateInquiryDoctorStatus(updateReqVO), INQUIRY_DOCTOR_STATUS_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryDoctorStatus_success() {
        // mock 数据
        InquiryDoctorStatusDO dbInquiryDoctorStatus = randomPojo(InquiryDoctorStatusDO.class);
        inquiryDoctorStatusMapper.insert(dbInquiryDoctorStatus);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryDoctorStatus.getId();

        // 调用
        inquiryDoctorStatusService.deleteInquiryDoctorStatus(id);
        // 校验数据不存在了
        assertNull(inquiryDoctorStatusMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryDoctorStatus_notExists() {
        // 准备参数
//        Long id = randomLong();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> inquiryDoctorStatusService.deleteInquiryDoctorStatus(id), INQUIRY_DOCTOR_STATUS_NOT_EXISTS);
    }

    @Test
    public void testGetInquiryDoctorStatusPage() {
        // mock 数据
        InquiryDoctorStatusDO dbInquiryDoctorStatus = randomPojo(InquiryDoctorStatusDO.class, o -> { // 等会查询到
            o.setInquiryWayType(1);
            o.setInquiryBizType(2);
            o.setStatus(1);
            o.setCreateTime(LocalDateTime.now());
        });
        inquiryDoctorStatusMapper.insert(dbInquiryDoctorStatus);
        // 测试 doctorGuid 不匹配
        // 测试 inquiryWayType 不匹配
        inquiryDoctorStatusMapper.insert(cloneIgnoreId(dbInquiryDoctorStatus, o -> o.setInquiryWayType(null)));
        // 测试 inquiryBizType 不匹配
        inquiryDoctorStatusMapper.insert(cloneIgnoreId(dbInquiryDoctorStatus, o -> o.setInquiryBizType(3)));
        // 测试 status 不匹配
        inquiryDoctorStatusMapper.insert(cloneIgnoreId(dbInquiryDoctorStatus, o -> o.setStatus(null)));
        // 测试 createTime 不匹配
        inquiryDoctorStatusMapper.insert(cloneIgnoreId(dbInquiryDoctorStatus, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryDoctorStatusPageReqVO reqVO = new InquiryDoctorStatusPageReqVO();
        reqVO.setInquiryBizType(3);
        // 调用
        PageResult<InquiryDoctorStatusDO> pageResult = inquiryDoctorStatusService.getInquiryDoctorStatusPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
    }

}