<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-system</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>saas-inquiry-multi-env</artifactId>
  <packaging>jar</packaging>

  <properties>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <xyy.soa.version>2.0.0-SNAPSHOT</xyy.soa.version>
    <!-- 依赖传递，不传递如果下游 -->
    <api.optional>true</api.optional>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <artifactId>saas-cloud-dependencies-bom</artifactId>
        <groupId>com.xyy.saas</groupId>
        <scope>import</scope>
        <type>pom</type>
        <version>2.0.0-SNAPSHOT</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <artifactId>dubbo-spring-boot-starter</artifactId>
      <groupId>org.apache.dubbo</groupId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
      <groupId>com.alibaba.cloud</groupId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
      <groupId>com.alibaba.cloud</groupId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <artifactId>nacos-client</artifactId>
      <groupId>com.alibaba.nacos</groupId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>com.xyy.common</groupId>
      <artifactId>xyy-common-dubbo-client</artifactId>
      <version>1.0-SNAPSHOT</version>
      <optional>true</optional>
    </dependency>


    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-drugstore-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-transmitter-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-hospital-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pharmacist-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-patient-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-signature-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-im-api</artifactId>
      <version>${project.version}</version>
      <optional>${api.optional}</optional>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-bpm-biz</artifactId>
      <version>${yudao.version}</version>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>

    <!-- junit -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
      <optional>true</optional>
    </dependency>

  </dependencies>


</project>