package cn.iocoder.yudao.framework.tenant.core.mq.rocketmq;


import cn.hutool.core.collection.ConcurrentHashSet;
import com.xyy.saas.inquiry.config.mq.FlowableRocketMQConsumeMessageHook;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.MQConsumer;
import org.apache.rocketmq.client.impl.consumer.DefaultMQPushConsumerImpl;
import org.apache.rocketmq.client.impl.producer.DefaultMQProducerImpl;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.MQProducer;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 * 多租户的 RocketMQ 初始化器 (覆盖yudao中的同名类)
 *      条件不生效：{@link cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration#tenantRocketMQInitializer()} 有自动装载TenantRocketMQInitializer
 * <AUTHOR>
 */
@Slf4j
// @Profile("!local")
@ConditionalOnClass(name = "org.apache.rocketmq.spring.core.RocketMQTemplate")
public class TenantRocketMQInitializer implements BeanPostProcessor, ApplicationContextAware, ApplicationListener<ContextRefreshedEvent> {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public TenantRocketMQInitializer() {
        log.info("【多环境】TenantRocketMQInitializer 初始化成功！");
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (!Objects.equals(event.getApplicationContext(), applicationContext)) {
            return;
        }
        initializeTenants();
    }

    private void initializeTenants() {
        // 获取所有 bean
        Map<String, Object> beans = applicationContext.getBeansOfType(Object.class);

        // 动态加载 EventBusConsumerHolder 类
        Class<?> eventBusConsumerHolderClass = null;
        try {
            eventBusConsumerHolderClass = Class.forName("com.xyy.saas.eventbus.rocketmq.core.original.EventBusConsumerHolder");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("EventBusConsumerHolder class not found", e);
        }

        for (Map.Entry<String, Object> entry: beans.entrySet()) {
            String beanName = entry.getKey();
            Object bean = entry.getValue();
            if (eventBusConsumerHolderClass.isInstance(bean)) {
                try {
                    Field consumerField = bean.getClass().getDeclaredField("consumer");
                    consumerField.setAccessible(true); // 设置为可访问
                    DefaultMQPushConsumer consumer = (DefaultMQPushConsumer) consumerField.get(bean);
                    initTenantConsumer(consumer, beanName);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        // 初始化其他类型的 bean
        applicationContext.getBeansOfType(DefaultRocketMQListenerContainer.class).forEach((beanName, bean) -> {
            initTenantConsumer(bean.getConsumer(), beanName);
        });

        // EvenBusRocketMQTemplate 内部封装RocketMQTemplate发送消息
        applicationContext.getBeansOfType(RocketMQTemplate.class).forEach((beanName, bean) -> {
            initTenantProducer(bean.getProducer(), beanName);
        });
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof DefaultRocketMQListenerContainer container) {
            initTenantConsumer(container.getConsumer(), beanName);
        } else if (bean instanceof RocketMQTemplate template) {
            initTenantProducer(template.getProducer(), beanName);
        }
        return bean;
    }

    // 解决重复注册hook问题
    private static final Set<MQProducer> MQ_PRODUCER_SET = new ConcurrentHashSet<>();

    private void initTenantProducer(DefaultMQProducer producer, String beanName) {
        if (producer == null) {
            return;
        }
        DefaultMQProducerImpl producerImpl = producer.getDefaultMQProducerImpl();
        if (producerImpl == null) {
            return;
        }

        synchronized (MQ_PRODUCER_SET) {
            if (MQ_PRODUCER_SET.contains(producer)) {
                return;
            }
            producerImpl.registerSendMessageHook(new TenantRocketMQSendMessageHook());
            log.info("[initTenantProducer][注册 SendMessageHook 成功] beanName: {}, producerGroup: {}", beanName, producer.getProducerGroup());
            MQ_PRODUCER_SET.add(producer);
        }
    }

    // 解决重复注册hook问题
    private static final Set<MQConsumer> MQ_CONSUMER_SET = new ConcurrentHashSet<>();

    private void initTenantConsumer(DefaultMQPushConsumer consumer, String beanName) {
        if (consumer == null) {
            return;
        }
        DefaultMQPushConsumerImpl consumerImpl = consumer.getDefaultMQPushConsumerImpl();
        if (consumerImpl == null) {
            return;
        }

        synchronized (MQ_CONSUMER_SET) {
            if (MQ_CONSUMER_SET.contains(consumer)) {
                return;
            }
            consumerImpl.registerConsumeMessageHook(new TenantRocketMQConsumeMessageHook());
            consumerImpl.registerConsumeMessageHook(new FlowableRocketMQConsumeMessageHook());
            log.info("[initTenantConsumer][注册 ConsumeMessageHook 成功] consumerGroup: {}", consumer.getConsumerGroup());
            MQ_CONSUMER_SET.add(consumer);
        }
    }


}