package com.xyy.saas.inquiry.config.anno;


import org.springframework.context.annotation.Conditional;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Conditional(ArrayContainsCondition.class)
public @interface ConditionalOnArrayContainsProperty {
    String prefix() default "";
    String value();
    String havingValue();
}
