package com.xyy.saas.inquiry.config.auto;

import cn.iocoder.yudao.framework.tenant.core.mq.rocketmq.TenantRocketMQInitializer;
import com.xyy.saas.inquiry.config.bean.DefaultTenantContextInfoProvider;
import com.xyy.saas.inquiry.config.dubbo.DubboBootstrapConfiguration;
import com.xyy.saas.inquiry.config.mq.LocalRocketMQConfig;
import com.xyy.saas.inquiry.config.redis.LocalRedisConfig;
import com.xyy.saas.inquiry.config.servlet.TomcatServerConfiguration;
import com.xyy.saas.inquiry.config.webclient.ForwardWebClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Import;

/**
 * 多环境自动配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@ConditionalOnProperty(prefix = "saas.inquiry.multi-env", name = "enabled", matchIfMissing = true)
@Import({
    // dubbo
    DubboBootstrapConfiguration.class,
    TomcatServerConfiguration.class,

    ForwardWebClientConfig.class,

    TenantRocketMQInitializer.class,

    DefaultTenantContextInfoProvider.class,

    LocalRedisConfig.class,
    LocalRocketMQConfig.class,
})
public class MultiEnvAutoConfiguration {
    
    public MultiEnvAutoConfiguration() {
        log.info("【多环境】MultiEnvAutoConfiguration 多环境自动配置 初始化完成！");
    }
}