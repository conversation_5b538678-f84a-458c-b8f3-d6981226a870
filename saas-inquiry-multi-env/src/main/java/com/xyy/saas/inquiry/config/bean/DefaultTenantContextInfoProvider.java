package com.xyy.saas.inquiry.config.bean;


import static com.xyy.saas.inquiry.constant.TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextInfoProvider;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class DefaultTenantContextInfoProvider implements TenantContextInfoProvider {

    @Resource
    private TenantApi tenantApi;

    @PostConstruct
    public void init() {
        log.info("""
            【多环境】DefaultTenantContextInfoProvider 初始化成功！使用说明：
                TenantDto tenantDto = TenantContextHolder.getTenantContextInfo(TENANT_CONTEXT_KEY_TENANT_DTO);
            """);
    }

    /**
     * 获取租户相关信息
     *
     * @param tenantId 租户ID
     * @return 租户相关信息的键值对
     */
    @Override
    public Map<String, Object> getTenantInfo(Long tenantId) {
        TenantDto tenant = tenantApi.getTenant(tenantId);
        if (tenant == null) {
            return Map.of();
        }

        return Map.of(TENANT_CONTEXT_KEY_TENANT_DTO, tenant);
    }
}
