package com.xyy.saas.inquiry.config.dubbo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * Dubbo Bootstrap 配置属性类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@ConfigurationProperties(prefix = "dubbo.bootstrap")
public class DubboBootstrapProperties {

    /**
     * 是否启用 Dubbo Bootstrap 自动配置
     */
    private boolean enabled = false;

    /**
     * 基础包路径，只有在这些包路径下的类才会被暴露为 Dubbo 服务
     * 支持多个包路径，使用逗号分隔或者配置为数组
     * 支持 Ant 风格的路径匹配模式：
     * - com.xyy.saas.inquiry.** (匹配所有子包)
     * - com.xyy.saas.inquiry.*.service (匹配单层子包下的service包)
     * - com.xyy.saas.inquiry.hospital.service.* (匹配指定包下的所有类)
     */
    private List<String> basePackage;

    /**
     * 排除包路径，在这些包路径下的类不会被暴露为 Dubbo 服务
     * 支持多个包路径，使用逗号分隔或者配置为数组
     * 支持 Ant 风格的路径匹配模式：
     * - com.xyy.saas.inquiry.**.internal.** (排除所有internal包)
     * - com.xyy.saas.inquiry.**.test.** (排除所有test包)
     * - com.xyy.saas.inquiry.hospital.service.PrivateApiImpl (排除特定类)
     */
    private List<String> excludePackage;
}
