package com.xyy.saas.inquiry.config.dubbo;


import com.xyy.saas.inquiry.config.anno.ConditionalOnArrayContainsProperty;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.InquiryClinicalCaseApi;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionDetailApi;
import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.registration.MedicalRegistrationApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureContractApi;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Configuration
@ConditionalOnArrayContainsProperty(prefix = "dubbo", value = "reference", havingValue = "kernel")
public class DubboReferenceKernelAutoConfiguration {

    @PostConstruct
    public void init() {
        log.info("【多环境】DubboReferenceKernelAutoConfiguration 初始化成功！");
    }


    @DubboReference
    private InquiryHospitalApi inquiryHospitalApi;
    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;




    @DubboReference
    private InquiryPharmacistApi inquiryPharmacistApi;






    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    private InquiryPrescriptionDetailApi inquiryPrescriptionDetailApi;

    @DubboReference
    private InquiryClinicalCaseApi inquiryClinicalCaseApi;

    @DubboReference
    private MedicalRegistrationApi medicalRegistrationApi;

    @DubboReference
    private InquirySignatureContractApi inquirySignatureContractApi;

    @DubboReference
    private InquiryApi inquiryApi;


    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;

    @DubboReference
    private TenantPackageCostApi tenantPackageCostApi;

    @DubboReference
    private InquiryImUserApi inquiryImUserApi;

    @DubboReference
    private InquiryImMessageApi inquiryImMessageApi;

}
