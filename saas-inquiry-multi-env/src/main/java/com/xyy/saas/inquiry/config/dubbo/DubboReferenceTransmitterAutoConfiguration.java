package com.xyy.saas.inquiry.config.dubbo;


import com.xyy.saas.inquiry.config.anno.ConditionalOnArrayContainsProperty;
import com.xyy.saas.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Configuration
@ConditionalOnArrayContainsProperty(prefix = "dubbo", value = "reference", havingValue = "transmitter")
public class DubboReferenceTransmitterAutoConfiguration {

    @PostConstruct
    public void init() {
        log.info("【多环境】DubboReferenceTransmitterAutoConfiguration 初始化成功！");
    }


    @DubboReference
    private TransmissionOrganApi transmissionOrganApi;
    @DubboReference
    private TransmissionServicePackApi transmissionServicePackApi;

    @DubboReference(retries = 0)
    private TransmissionApi transmissionApi;


}
