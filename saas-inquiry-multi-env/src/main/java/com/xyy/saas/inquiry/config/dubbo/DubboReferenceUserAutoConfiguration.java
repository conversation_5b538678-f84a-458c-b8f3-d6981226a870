package com.xyy.saas.inquiry.config.dubbo;


import cn.iocoder.yudao.framework.apilog.config.YudaoApiLogAutoConfiguration;
import cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.logger.ApiAccessLogApi;
import cn.iocoder.yudao.module.infra.api.logger.ApiErrorLogApi;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.dict.DictTypeApi;
import cn.iocoder.yudao.module.system.api.logger.OperateLogApi;
import cn.iocoder.yudao.module.system.api.oauth2.OAuth2TokenApi;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.permission.RoleApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantPackageApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantThirdAppApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantUserRelationApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.UserCompatApi;
import cn.iocoder.yudao.module.system.api.user.UserFingerPrintApi;
import com.xyy.saas.inquiry.config.anno.ConditionalOnArrayContainsProperty;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.catalog.CatalogApi;
import com.xyy.saas.inquiry.product.api.search.ProductSearchApi;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Configuration
@ConditionalOnArrayContainsProperty(prefix = "dubbo", value = "reference", havingValue = "user")
@AutoConfiguration(before = {YudaoTenantAutoConfiguration.class, YudaoApiLogAutoConfiguration.class})
public class DubboReferenceUserAutoConfiguration {

    @PostConstruct
    public void init() {
        log.info("【多环境】DubboReferenceUserAutoConfiguration 初始化成功！");
    }

    @DubboReference
    private TenantApi tenantApi;


    @DubboReference
    private ApiAccessLogApi apiAccessLogApi;

    @DubboReference
    private ApiErrorLogApi apiErrorLogApi;

    @DubboReference
    private OAuth2TokenApi oAuth2TokenApi;

    @DubboReference
    private PermissionApi permissionApi;

    @DubboReference
    private RoleApi roleApi;

    @DubboReference
    private ConfigApi configApi;

    @DubboReference
    private FileApi fileApi;

    @DubboReference
    private AdminUserApi adminUserApi;

    @DubboReference
    private TenantUserRelationApi tenantUserRelationApi;

    @DubboReference
    private TenantPackageApi tenantPackageApi;

    @DubboReference
    private OperateLogApi operateLogApi;

    @DubboReference
    private DictDataApi dictDataApi;

    @DubboReference
    private TenantThirdAppApi tenantThirdAppApi;

    @DubboReference
    private UserFingerPrintApi userFingerPrintApi;

    @DubboReference
    private UserCompatApi userCompatApi;

    @DubboReference
    private DictTypeApi dictTypeApi;

    @DubboReference
    private TenantServicePackRelationApi tenantServicePackRelationApi;




    @DubboReference
    private CatalogApi catalogApi;

    @DubboReference
    private ProductSearchApi productSearchApi;

    @DubboReference
    private ProductStdlibApi productStdlibApi;

    //
    // // 需要指定 Primary bean
    // // tenantApi : dubbo代理bean
    // // tenantApiImpl : spring本地bean
    // @Bean
    // @Primary
    // public TenantApi tenantApi() {
    //     return this.tenantApi;
    // }
    //
    // @Bean
    // @Primary
    // public ApiAccessLogApi apiAccessLogApi() {
    //     return this.apiAccessLogApi;
    // }
    //
    // @Bean
    // @Primary
    // public ApiErrorLogApi apiErrorLogApi() {
    //     return this.apiErrorLogApi;
    // }
    //
    // @Bean
    // @Primary
    // public OAuth2TokenApi oAuth2TokenApi() {
    //     return this.oAuth2TokenApi;
    // }
    //
    // @Bean
    // @Primary
    // public PermissionApi permissionApi() {
    //     return this.permissionApi;
    // }
    //
    // @Bean
    // @Primary
    // public RoleApi roleApi() {
    //     return this.roleApi;
    // }
    //
    // @Bean
    // @Primary
    // public ConfigApi configApi() {
    //     return this.configApi;
    // }
    //
    // @Bean
    // @Primary
    // public FileApi fileApi() {
    //     return this.fileApi;
    // }
    //
    // @Bean
    // @Primary
    // public AdminUserApi adminUserApi() {
    //     return this.adminUserApi;
    // }
    //
    // @Bean
    // @Primary
    // public TenantUserRelationApi tenantUserRelationApi() {
    //     return this.tenantUserRelationApi;
    // }
    //
    // @Bean
    // @Primary
    // public TenantPackageApi tenantPackageApi() {
    //     return this.tenantPackageApi;
    // }
    //
    // @Bean
    // @Primary
    // public OperateLogApi operateLogApi() {
    //     return this.operateLogApi;
    // }
    //
    // @Bean
    // @Primary
    // public DictDataApi dictDataApi() {
    //     return this.dictDataApi;
    // }
    //
    // @Bean
    // @Primary
    // public TenantThirdAppApi tenantThirdAppApi() {
    //     return this.tenantThirdAppApi;
    // }
    //
    // @Bean
    // @Primary
    // public UserFingerPrintApi userFingerPrintApi() {
    //     return this.userFingerPrintApi;
    // }
    //
    // @Bean
    // @Primary
    // public UserCompatApi userCompatApi() {
    //     return this.userCompatApi;
    // }
    //
    // @Bean
    // @Primary
    // public DictTypeApi dictTypeApi() {
    //     return this.dictTypeApi;
    // }
    //
    // @Bean
    // @Primary
    // public TenantServicePackRelationApi tenantServicePackRelationApi() {
    //     return this.tenantServicePackRelationApi;
    // }
    //
    // @Bean
    // @Primary
    // public CatalogApi catalogApi() {
    //     return this.catalogApi;
    // }
    //
    // @Bean
    // @Primary
    // public ProductSearchApi productSearchApi() {
    //     return this.productSearchApi;
    // }
    //
    // @Bean
    // @Primary
    // public ProductStdlibApi productStdlibApi() {
    //     return this.productStdlibApi;
    // }






}
