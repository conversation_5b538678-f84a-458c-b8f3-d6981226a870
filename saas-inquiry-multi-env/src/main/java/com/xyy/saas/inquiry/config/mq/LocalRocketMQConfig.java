package com.xyy.saas.inquiry.config.mq;

import com.xyy.saas.inquiry.config.mq.local.LocalRocketMQTemplate;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * 本地模式 RocketMQ 配置
 * 在本地模式下使用内存队列实现替代 RocketMQ
 */
@Configuration
@Profile("local")
public class LocalRocketMQConfig {

    /**
     * 本地模式下的 RocketMQTemplate 实现
     * 使用内存队列替代 RocketMQ
     */
    @Bean
    @Primary
    public RocketMQTemplate rocketMQTemplate() {
        return new LocalRocketMQTemplate();
    }
}
