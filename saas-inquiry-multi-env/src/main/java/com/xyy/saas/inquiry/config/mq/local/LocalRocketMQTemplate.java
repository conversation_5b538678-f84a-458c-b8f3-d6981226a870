package com.xyy.saas.inquiry.config.mq.local;

import com.xyy.saas.inquiry.localmock.mq.LocalMessageMapper;
import com.xyy.saas.inquiry.localmock.mq.LocalMessagePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 本地内存实现的 RocketMQTemplate
 * 使用内存队列模拟 RocketMQ 操作
 */
@Slf4j
public class LocalRocketMQTemplate extends RocketMQTemplate {

    @Autowired
    private LocalMessageMapper localMessageMapper;
    
    private final Map<String, BlockingQueue<Object>> topicQueues = new ConcurrentHashMap<>();
    
    @Override
    public void convertAndSend(String destination, Object payload) {
        log.info("本地模式发送消息到 {}: {}", destination, payload);

        // 持久化消息到数据库
        LocalMessagePO messagePO = new LocalMessagePO();
        messagePO.setTopic(destination);
        messagePO.setMessageBody(payload.toString()); // 假设payload是可转换为字符串的
        messagePO.setStatus("NEW"); // 初始状态
        localMessageMapper.insert(messagePO);
        log.debug("消息已持久化到数据库，ID: {}", messagePO.getId());
        
        BlockingQueue<Object> queue = topicQueues.computeIfAbsent(destination, 
            k -> new LinkedBlockingQueue<>());
        
        try {
            queue.put(payload);
            log.debug("消息已添加到本地队列 {}, 当前队列大小: {}", destination, queue.size());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("发送消息到本地队列失败: {}", e.getMessage());
        }
    }
    
    @Override
    public void send(String destination, Message<?> message) {
        log.info("本地模式发送消息到 {}: {}", destination, message.getPayload());
        convertAndSend(destination, message.getPayload());
    }
    
    @Override
    public void asyncSend(String destination, Object payload, SendCallback sendCallback) {
        log.info("本地模式异步发送消息到 {}: {}", destination, payload);
        
        convertAndSend(destination, payload);
        
        if (sendCallback != null) {
            try {
                sendCallback.onSuccess(null);
            } catch (Exception e) {
                log.error("回调执行失败: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 获取指定主题的本地队列（用于测试或消费）
     */
    public BlockingQueue<Object> getLocalQueue(String destination) {
        return topicQueues.get(destination);
    }
    
    /**
     * 清空所有本地队列
     */
    public void clearAllQueues() {
        topicQueues.clear();
        log.info("已清空所有本地消息队列");
    }
}
