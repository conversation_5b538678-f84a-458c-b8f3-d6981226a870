package com.xyy.saas.inquiry.config.redis;

import com.xyy.saas.inquiry.config.redis.local.LocalRedisConnectionFactory;
import com.xyy.saas.inquiry.config.redis.local.LocalRedisTemplate;
import com.xyy.saas.inquiry.config.redis.local.LocalRedissonClient;
import com.xyy.saas.inquiry.config.redis.local.LocalStringRedisTemplate;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
@Profile("local")
public class LocalRedisConfig {

    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        return new LocalRedisConnectionFactory();
    }

    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate() {
        LocalRedisTemplate<String, Object> template = new LocalRedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory());
        return template;
    }

    @Bean
    @Primary
    public StringRedisTemplate stringRedisTemplate() {
        LocalStringRedisTemplate template = new LocalStringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory());
        return template;
    }

    /**
     * 本地环境的 RedissonClient Bean
     * 使用内存实现，不依赖外部Redis服务
     * 提供分布式锁、分布式集合等功能的本地化实现
     */
    @Bean
    @Primary
    public RedissonClient redissonClient() {
        return new LocalRedissonClient();
    }
}
