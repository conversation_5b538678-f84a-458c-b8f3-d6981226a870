package com.xyy.saas.inquiry.config.redis.local;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.ScanOptions;

/**
 * 本地内存实现的 HashOperations
 */
public class LocalHashOperations<H, HK, HV> implements HashOperations<H, HK, HV> {
    
    private final LocalRedisTemplate<H, ?> template;
    private final Map<String, Map<String, Object>> hashStore = new ConcurrentHashMap<>();
    
    public LocalHashOperations(LocalRedisTemplate<H, ?> template) {
        this.template = template;
    }
    
    @Override
    public Long delete(H key, Object... hashKeys) {
        Map<String, Object> hash = hashStore.get(key.toString());
        if (hash == null) return 0L;
        
        long count = 0;
        for (Object hashKey : hashKeys) {
            if (hash.remove(hashKey.toString()) != null) {
                count++;
            }
        }
        return count;
    }
    
    @Override
    public Boolean hasKey(H key, Object hashKey) {
        Map<String, Object> hash = hashStore.get(key.toString());
        return hash != null && hash.containsKey(hashKey.toString());
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public HV get(H key, Object hashKey) {
        Map<String, Object> hash = hashStore.get(key.toString());
        return hash != null ? (HV) hash.get(hashKey.toString()) : null;
    }
    
    @Override
    public List<HV> multiGet(H key, Collection<HK> hashKeys) {
        throw new UnsupportedOperationException("multiGet not implemented in local mode");
    }
    
    @Override
    public Long increment(H key, HK hashKey, long delta) {
        Map<String, Object> hash = hashStore.computeIfAbsent(key.toString(), k -> new ConcurrentHashMap<>());
        Object value = hash.get(hashKey.toString());
        long newValue = (value != null ? Long.parseLong(value.toString()) : 0) + delta;
        hash.put(hashKey.toString(), newValue);
        return newValue;
    }
    
    @Override
    public Double increment(H key, HK hashKey, double delta) {
        Map<String, Object> hash = hashStore.computeIfAbsent(key.toString(), k -> new ConcurrentHashMap<>());
        Object value = hash.get(hashKey.toString());
        double newValue = (value != null ? Double.parseDouble(value.toString()) : 0.0) + delta;
        hash.put(hashKey.toString(), newValue);
        return newValue;
    }
    
    @Override
    public HK randomKey(H key) {
        throw new UnsupportedOperationException("randomKey not implemented in local mode");
    }
    
    @Override
    public Map.Entry<HK, HV> randomEntry(H key) {
        throw new UnsupportedOperationException("randomEntry not implemented in local mode");
    }
    
    @Override
    public List<HK> randomKeys(H key, long count) {
        throw new UnsupportedOperationException("randomKeys not implemented in local mode");
    }
    
    @Override
    public Map<HK, HV> randomEntries(H key, long count) {
        throw new UnsupportedOperationException("randomEntries not implemented in local mode");
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public Set<HK> keys(H key) {
        Map<String, Object> hash = hashStore.get(key.toString());
        if (hash == null) return Set.of();
        return (Set<HK>) hash.keySet();
    }
    
    @Override
    public Long lengthOfValue(H key, HK hashKey) {
        HV value = get(key, hashKey);
        return value != null ? (long) value.toString().length() : 0L;
    }
    
    @Override
    public Long size(H key) {
        Map<String, Object> hash = hashStore.get(key.toString());
        return hash != null ? (long) hash.size() : 0L;
    }
    
    @Override
    public void putAll(H key, Map<? extends HK, ? extends HV> m) {
        Map<String, Object> hash = hashStore.computeIfAbsent(key.toString(), k -> new ConcurrentHashMap<>());
        for (Map.Entry<? extends HK, ? extends HV> entry : m.entrySet()) {
            hash.put(entry.getKey().toString(), entry.getValue());
        }
    }
    
    @Override
    public void put(H key, HK hashKey, HV value) {
        Map<String, Object> hash = hashStore.computeIfAbsent(key.toString(), k -> new ConcurrentHashMap<>());
        hash.put(hashKey.toString(), value);
    }
    
    @Override
    public Boolean putIfAbsent(H key, HK hashKey, HV value) {
        Map<String, Object> hash = hashStore.computeIfAbsent(key.toString(), k -> new ConcurrentHashMap<>());
        if (!hash.containsKey(hashKey.toString())) {
            hash.put(hashKey.toString(), value);
            return true;
        }
        return false;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public List<HV> values(H key) {
        Map<String, Object> hash = hashStore.get(key.toString());
        if (hash == null) return List.of();
        return (List<HV>) List.copyOf(hash.values());
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public Map<HK, HV> entries(H key) {
        Map<String, Object> hash = hashStore.get(key.toString());
        if (hash == null) return Map.of();
        Map<HK, HV> result = new ConcurrentHashMap<>();
        for (Map.Entry<String, Object> entry : hash.entrySet()) {
            result.put((HK) entry.getKey(), (HV) entry.getValue());
        }
        return result;
    }
    
    @Override
    public Cursor<Map.Entry<HK, HV>> scan(H key, ScanOptions options) {
        throw new UnsupportedOperationException("scan not implemented in local mode");
    }

    /**
     * @return never {@literal null}.
     */
    @Override
    public RedisOperations<H, ?> getOperations() {
        return template;
    }
}
