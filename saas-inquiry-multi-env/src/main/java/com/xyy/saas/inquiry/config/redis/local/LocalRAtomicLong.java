package com.xyy.saas.inquiry.config.redis.local;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RFuture;
import org.redisson.client.codec.Codec;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 本地内存实现的 RAtomicLong
 */
public class LocalRAtomicLong implements RAtomicLong {
    
    private final String name;
    private final LocalRedissonClient client;
    private final AtomicLong atomicLong;
    
    public LocalRAtomicLong(String name, LocalRedissonClient client) {
        this.name = name;
        this.client = client;
        
        // 从存储中获取初始值
        Object value = client.getValue(name);
        long initialValue = 0L;
        if (value instanceof Number) {
            initialValue = ((Number) value).longValue();
        }
        this.atomicLong = new AtomicLong(initialValue);
    }
    
    private void saveValue(long value) {
        client.putValue(name, value);
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public long addAndGet(long delta) {
        long result = atomicLong.addAndGet(delta);
        saveValue(result);
        return result;
    }
    
    @Override
    public boolean compareAndSet(long expect, long update) {
        boolean result = atomicLong.compareAndSet(expect, update);
        if (result) {
            saveValue(update);
        }
        return result;
    }
    
    @Override
    public long decrementAndGet() {
        long result = atomicLong.decrementAndGet();
        saveValue(result);
        return result;
    }
    
    @Override
    public long get() {
        return atomicLong.get();
    }
    
    @Override
    public long getAndAdd(long delta) {
        long result = atomicLong.getAndAdd(delta);
        saveValue(atomicLong.get());
        return result;
    }
    
    @Override
    public long getAndDecrement() {
        long result = atomicLong.getAndDecrement();
        saveValue(atomicLong.get());
        return result;
    }
    
    @Override
    public long getAndIncrement() {
        long result = atomicLong.getAndIncrement();
        saveValue(atomicLong.get());
        return result;
    }
    
    @Override
    public long getAndSet(long newValue) {
        long result = atomicLong.getAndSet(newValue);
        saveValue(newValue);
        return result;
    }
    
    @Override
    public long incrementAndGet() {
        long result = atomicLong.incrementAndGet();
        saveValue(result);
        return result;
    }
    
    @Override
    public void set(long newValue) {
        atomicLong.set(newValue);
        saveValue(newValue);
    }
    
    @Override
    public boolean expire(long timeToLive, TimeUnit timeUnit) {
        if (client.hasKey(name)) {
            client.putValue(name, atomicLong.get(), timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(long timestamp) {
        if (client.hasKey(name)) {
            long timeToLive = timestamp - System.currentTimeMillis();
            if (timeToLive > 0) {
                client.putValue(name, atomicLong.get(), timeToLive, TimeUnit.MILLISECONDS);
            } else {
                client.deleteKey(name);
            }
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(Instant instant) {
        return expireAt(instant.toEpochMilli());
    }
    
    @Override
    public boolean clearExpire() {
        if (client.hasKey(name)) {
            client.putValue(name, atomicLong.get());
            return true;
        }
        return false;
    }
    
    @Override
    public long remainTimeToLive() {
        return client.hasKey(name) ? -1 : -2;
    }
    
    @Override
    public boolean delete() {
        if (client.hasKey(name)) {
            client.deleteKey(name);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean unlink() {
        return delete();
    }
    
    @Override
    public boolean isExists() {
        return client.hasKey(name);
    }
    
    @Override
    public Codec getCodec() {
        return null;
    }
    
    // 异步方法实现
    @Override
    public RFuture<Long> addAndGetAsync(long delta) {
        return new LocalRFuture<>(() -> addAndGet(delta));
    }
    
    @Override
    public RFuture<Boolean> compareAndSetAsync(long expect, long update) {
        return new LocalRFuture<>(() -> compareAndSet(expect, update));
    }
    
    @Override
    public RFuture<Long> decrementAndGetAsync() {
        return new LocalRFuture<>(() -> decrementAndGet());
    }
    
    @Override
    public RFuture<Long> getAsync() {
        return new LocalRFuture<>(() -> get());
    }
    
    @Override
    public RFuture<Long> getAndAddAsync(long delta) {
        return new LocalRFuture<>(() -> getAndAdd(delta));
    }
    
    @Override
    public RFuture<Long> getAndDecrementAsync() {
        return new LocalRFuture<>(() -> getAndDecrement());
    }
    
    @Override
    public RFuture<Long> getAndIncrementAsync() {
        return new LocalRFuture<>(() -> getAndIncrement());
    }
    
    @Override
    public RFuture<Long> getAndSetAsync(long newValue) {
        return new LocalRFuture<>(() -> getAndSet(newValue));
    }
    
    @Override
    public RFuture<Long> incrementAndGetAsync() {
        return new LocalRFuture<>(() -> incrementAndGet());
    }
    
    @Override
    public RFuture<Void> setAsync(long newValue) {
        return new LocalRFuture<>(() -> {
            set(newValue);
            return null;
        });
    }
    
    @Override
    public RFuture<Boolean> expireAsync(long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> expire(timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(long timestamp) {
        return new LocalRFuture<>(() -> expireAt(timestamp));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(Instant instant) {
        return new LocalRFuture<>(() -> expireAt(instant));
    }
    
    @Override
    public RFuture<Boolean> clearExpireAsync() {
        return new LocalRFuture<>(() -> clearExpire());
    }
    
    @Override
    public RFuture<Long> remainTimeToLiveAsync() {
        return new LocalRFuture<>(() -> remainTimeToLive());
    }
    
    @Override
    public RFuture<Boolean> deleteAsync() {
        return new LocalRFuture<>(() -> delete());
    }
    
    @Override
    public RFuture<Boolean> unlinkAsync() {
        return new LocalRFuture<>(() -> unlink());
    }
    
    @Override
    public RFuture<Boolean> isExistsAsync() {
        return new LocalRFuture<>(() -> isExists());
    }
}
