package com.xyy.saas.inquiry.config.redis.local;

import org.redisson.api.RBucket;
import org.redisson.api.RFuture;
import org.redisson.client.codec.Codec;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

/**
 * 本地内存实现的 RBucket
 */
public class LocalRBucket<V> implements RBucket<V> {
    
    private final String name;
    private final LocalRedissonClient client;
    
    public LocalRBucket(String name, LocalRedissonClient client) {
        this.name = name;
        this.client = client;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public long size() {
        Object value = client.getValue(name);
        return value != null ? 1 : 0;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public V get() {
        return (V) client.getValue(name);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public V getAndDelete() {
        V value = (V) client.getValue(name);
        if (value != null) {
            client.deleteKey(name);
        }
        return value;
    }
    
    @Override
    public boolean trySet(V value) {
        if (!client.hasKey(name)) {
            client.putValue(name, value);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean trySet(V value, long timeToLive, TimeUnit timeUnit) {
        if (!client.hasKey(name)) {
            client.putValue(name, value, timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean compareAndSet(V expect, V update) {
        Object current = client.getValue(name);
        if ((current == null && expect == null) || 
            (current != null && current.equals(expect))) {
            client.putValue(name, update);
            return true;
        }
        return false;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public V getAndSet(V newValue) {
        V oldValue = (V) client.getValue(name);
        client.putValue(name, newValue);
        return oldValue;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public V getAndSet(V value, long timeToLive, TimeUnit timeUnit) {
        V oldValue = (V) client.getValue(name);
        client.putValue(name, value, timeToLive, timeUnit);
        return oldValue;
    }
    
    @Override
    public void set(V value) {
        client.putValue(name, value);
    }
    
    @Override
    public void set(V value, long timeToLive, TimeUnit timeUnit) {
        client.putValue(name, value, timeToLive, timeUnit);
    }
    
    @Override
    public void set(V value, Duration duration) {
        client.putValue(name, value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public boolean setIfExists(V value) {
        if (client.hasKey(name)) {
            client.putValue(name, value);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean setIfExists(V value, long timeToLive, TimeUnit timeUnit) {
        if (client.hasKey(name)) {
            client.putValue(name, value, timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public void setAndKeepTTL(V value) {
        // 在本地实现中，我们简单地设置值，不保持TTL
        client.putValue(name, value);
    }
    
    @Override
    public boolean expire(long timeToLive, TimeUnit timeUnit) {
        if (client.hasKey(name)) {
            Object value = client.getValue(name);
            client.putValue(name, value, timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(long timestamp) {
        if (client.hasKey(name)) {
            Object value = client.getValue(name);
            long timeToLive = timestamp - System.currentTimeMillis();
            if (timeToLive > 0) {
                client.putValue(name, value, timeToLive, TimeUnit.MILLISECONDS);
            } else {
                client.deleteKey(name);
            }
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(Instant instant) {
        return expireAt(instant.toEpochMilli());
    }
    
    @Override
    public boolean clearExpire() {
        if (client.hasKey(name)) {
            Object value = client.getValue(name);
            client.putValue(name, value); // 重新设置，不带过期时间
            return true;
        }
        return false;
    }
    
    @Override
    public long remainTimeToLive() {
        // 在本地实现中，我们不精确跟踪TTL
        return client.hasKey(name) ? -1 : -2;
    }
    
    @Override
    public boolean delete() {
        if (client.hasKey(name)) {
            client.deleteKey(name);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean unlink() {
        return delete();
    }
    
    @Override
    public boolean isExists() {
        return client.hasKey(name);
    }
    
    @Override
    public Codec getCodec() {
        return null; // 在本地实现中不使用编解码器
    }
    
    // 异步方法实现
    @Override
    public RFuture<Long> sizeAsync() {
        return new LocalRFuture<>(() -> size());
    }
    
    @Override
    public RFuture<V> getAsync() {
        return new LocalRFuture<>(() -> get());
    }
    
    @Override
    public RFuture<V> getAndDeleteAsync() {
        return new LocalRFuture<>(() -> getAndDelete());
    }
    
    @Override
    public RFuture<Boolean> trySetAsync(V value) {
        return new LocalRFuture<>(() -> trySet(value));
    }
    
    @Override
    public RFuture<Boolean> trySetAsync(V value, long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> trySet(value, timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Boolean> compareAndSetAsync(V expect, V update) {
        return new LocalRFuture<>(() -> compareAndSet(expect, update));
    }
    
    @Override
    public RFuture<V> getAndSetAsync(V newValue) {
        return new LocalRFuture<>(() -> getAndSet(newValue));
    }
    
    @Override
    public RFuture<V> getAndSetAsync(V value, long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> getAndSet(value, timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Void> setAsync(V value) {
        return new LocalRFuture<>(() -> {
            set(value);
            return null;
        });
    }
    
    @Override
    public RFuture<Void> setAsync(V value, long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> {
            set(value, timeToLive, timeUnit);
            return null;
        });
    }
    
    @Override
    public RFuture<Boolean> setIfExistsAsync(V value) {
        return new LocalRFuture<>(() -> setIfExists(value));
    }
    
    @Override
    public RFuture<Boolean> setIfExistsAsync(V value, long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> setIfExists(value, timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Void> setAndKeepTTLAsync(V value) {
        return new LocalRFuture<>(() -> {
            setAndKeepTTL(value);
            return null;
        });
    }
    
    @Override
    public RFuture<Boolean> expireAsync(long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> expire(timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(long timestamp) {
        return new LocalRFuture<>(() -> expireAt(timestamp));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(Instant instant) {
        return new LocalRFuture<>(() -> expireAt(instant));
    }
    
    @Override
    public RFuture<Boolean> clearExpireAsync() {
        return new LocalRFuture<>(() -> clearExpire());
    }
    
    @Override
    public RFuture<Long> remainTimeToLiveAsync() {
        return new LocalRFuture<>(() -> remainTimeToLive());
    }
    
    @Override
    public RFuture<Boolean> deleteAsync() {
        return new LocalRFuture<>(() -> delete());
    }
    
    @Override
    public RFuture<Boolean> unlinkAsync() {
        return new LocalRFuture<>(() -> unlink());
    }
    
    @Override
    public RFuture<Boolean> isExistsAsync() {
        return new LocalRFuture<>(() -> isExists());
    }
}
