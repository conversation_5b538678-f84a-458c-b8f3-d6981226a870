package com.xyy.saas.inquiry.config.redis.local;

import org.redisson.api.RFuture;
import org.redisson.api.RPromise;

import java.util.concurrent.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 本地实现的 RFuture
 * 在本地模式下，所有操作都是同步的，所以我们简单地包装结果
 */
public class LocalRFuture<V> implements RFuture<V> {
    
    private final CompletableFuture<V> future;
    
    public LocalRFuture(Callable<V> callable) {
        this.future = CompletableFuture.supplyAsync(() -> {
            try {
                return callable.call();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    public LocalRFuture(V value) {
        this.future = CompletableFuture.completedFuture(value);
    }
    
    public LocalRFuture(CompletableFuture<V> future) {
        this.future = future;
    }
    
    @Override
    public boolean isSuccess() {
        return future.isDone() && !future.isCompletedExceptionally();
    }
    
    @Override
    public Throwable cause() {
        if (future.isCompletedExceptionally()) {
            try {
                future.get();
            } catch (ExecutionException e) {
                return e.getCause();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return e;
            }
        }
        return null;
    }
    
    @Override
    public V getNow() {
        if (future.isDone()) {
            try {
                return future.get();
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
    
    @Override
    public V join() {
        return future.join();
    }
    
    @Override
    public RFuture<V> sync() throws InterruptedException {
        try {
            future.get();
        } catch (ExecutionException e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw new RuntimeException(e.getCause());
        }
        return this;
    }
    
    @Override
    public RFuture<V> syncInterruptibly() throws InterruptedException {
        return sync();
    }
    
    @Override
    public RFuture<V> await() throws InterruptedException {
        try {
            future.get();
        } catch (ExecutionException e) {
            // 忽略异常，只等待完成
        }
        return this;
    }
    
    @Override
    public RFuture<V> awaitUninterruptibly() {
        try {
            future.get();
        } catch (Exception e) {
            // 忽略异常，只等待完成
        }
        return this;
    }
    
    @Override
    public boolean await(long timeout, TimeUnit unit) throws InterruptedException {
        try {
            future.get(timeout, unit);
            return true;
        } catch (TimeoutException e) {
            return false;
        } catch (ExecutionException e) {
            return true; // 已完成，即使有异常
        }
    }
    
    @Override
    public boolean await(long timeoutMillis) throws InterruptedException {
        return await(timeoutMillis, TimeUnit.MILLISECONDS);
    }
    
    @Override
    public boolean awaitUninterruptibly(long timeout, TimeUnit unit) {
        try {
            return await(timeout, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    @Override
    public boolean awaitUninterruptibly(long timeoutMillis) {
        return awaitUninterruptibly(timeoutMillis, TimeUnit.MILLISECONDS);
    }
    
    @Override
    public RFuture<V> addListener(GenericFutureListener<? extends Future<? super V>> listener) {
        future.whenComplete((result, throwable) -> {
            try {
                listener.operationComplete(this);
            } catch (Exception e) {
                // 忽略监听器异常
            }
        });
        return this;
    }
    
    @Override
    public RFuture<V> addListeners(GenericFutureListener<? extends Future<? super V>>... listeners) {
        for (GenericFutureListener<? extends Future<? super V>> listener : listeners) {
            addListener(listener);
        }
        return this;
    }
    
    @Override
    public RFuture<V> removeListener(GenericFutureListener<? extends Future<? super V>> listener) {
        // 在简单实现中，我们不支持移除监听器
        return this;
    }
    
    @Override
    public RFuture<V> removeListeners(GenericFutureListener<? extends Future<? super V>>... listeners) {
        // 在简单实现中，我们不支持移除监听器
        return this;
    }
    
    // CompletableFuture 方法委托
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return future.cancel(mayInterruptIfRunning);
    }
    
    @Override
    public boolean isCancelled() {
        return future.isCancelled();
    }
    
    @Override
    public boolean isDone() {
        return future.isDone();
    }
    
    @Override
    public V get() throws InterruptedException, ExecutionException {
        return future.get();
    }
    
    @Override
    public V get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return future.get(timeout, unit);
    }
    
    // CompletionStage 方法委托
    @Override
    public <U> CompletionStage<U> thenApply(Function<? super V, ? extends U> fn) {
        return future.thenApply(fn);
    }
    
    @Override
    public <U> CompletionStage<U> thenApplyAsync(Function<? super V, ? extends U> fn) {
        return future.thenApplyAsync(fn);
    }
    
    @Override
    public <U> CompletionStage<U> thenApplyAsync(Function<? super V, ? extends U> fn, Executor executor) {
        return future.thenApplyAsync(fn, executor);
    }
    
    @Override
    public CompletionStage<Void> thenAccept(Consumer<? super V> action) {
        return future.thenAccept(action);
    }
    
    @Override
    public CompletionStage<Void> thenAcceptAsync(Consumer<? super V> action) {
        return future.thenAcceptAsync(action);
    }
    
    @Override
    public CompletionStage<Void> thenAcceptAsync(Consumer<? super V> action, Executor executor) {
        return future.thenAcceptAsync(action, executor);
    }
    
    @Override
    public CompletionStage<Void> thenRun(Runnable action) {
        return future.thenRun(action);
    }
    
    @Override
    public CompletionStage<Void> thenRunAsync(Runnable action) {
        return future.thenRunAsync(action);
    }
    
    @Override
    public CompletionStage<Void> thenRunAsync(Runnable action, Executor executor) {
        return future.thenRunAsync(action, executor);
    }
    
    @Override
    public <U, V1> CompletionStage<V1> thenCombine(CompletionStage<? extends U> other, BiFunction<? super V, ? super U, ? extends V1> fn) {
        return future.thenCombine(other, fn);
    }
    
    @Override
    public <U, V1> CompletionStage<V1> thenCombineAsync(CompletionStage<? extends U> other, BiFunction<? super V, ? super U, ? extends V1> fn) {
        return future.thenCombineAsync(other, fn);
    }
    
    @Override
    public <U, V1> CompletionStage<V1> thenCombineAsync(CompletionStage<? extends U> other, BiFunction<? super V, ? super U, ? extends V1> fn, Executor executor) {
        return future.thenCombineAsync(other, fn, executor);
    }
    
    @Override
    public <U> CompletionStage<Void> thenAcceptBoth(CompletionStage<? extends U> other, BiConsumer<? super V, ? super U> action) {
        return future.thenAcceptBoth(other, action);
    }
    
    @Override
    public <U> CompletionStage<Void> thenAcceptBothAsync(CompletionStage<? extends U> other, BiConsumer<? super V, ? super U> action) {
        return future.thenAcceptBothAsync(other, action);
    }
    
    @Override
    public <U> CompletionStage<Void> thenAcceptBothAsync(CompletionStage<? extends U> other, BiConsumer<? super V, ? super U> action, Executor executor) {
        return future.thenAcceptBothAsync(other, action, executor);
    }
    
    @Override
    public CompletionStage<Void> runAfterBoth(CompletionStage<?> other, Runnable action) {
        return future.runAfterBoth(other, action);
    }
    
    @Override
    public CompletionStage<Void> runAfterBothAsync(CompletionStage<?> other, Runnable action) {
        return future.runAfterBothAsync(other, action);
    }
    
    @Override
    public CompletionStage<Void> runAfterBothAsync(CompletionStage<?> other, Runnable action, Executor executor) {
        return future.runAfterBothAsync(other, action, executor);
    }
    
    @Override
    public <U> CompletionStage<U> applyToEither(CompletionStage<? extends V> other, Function<? super V, U> fn) {
        return future.applyToEither(other, fn);
    }
    
    @Override
    public <U> CompletionStage<U> applyToEitherAsync(CompletionStage<? extends V> other, Function<? super V, U> fn) {
        return future.applyToEitherAsync(other, fn);
    }
    
    @Override
    public <U> CompletionStage<U> applyToEitherAsync(CompletionStage<? extends V> other, Function<? super V, U> fn, Executor executor) {
        return future.applyToEitherAsync(other, fn, executor);
    }
    
    @Override
    public CompletionStage<Void> acceptEither(CompletionStage<? extends V> other, Consumer<? super V> action) {
        return future.acceptEither(other, action);
    }
    
    @Override
    public CompletionStage<Void> acceptEitherAsync(CompletionStage<? extends V> other, Consumer<? super V> action) {
        return future.acceptEitherAsync(other, action);
    }
    
    @Override
    public CompletionStage<Void> acceptEitherAsync(CompletionStage<? extends V> other, Consumer<? super V> action, Executor executor) {
        return future.acceptEitherAsync(other, action, executor);
    }
    
    @Override
    public CompletionStage<Void> runAfterEither(CompletionStage<?> other, Runnable action) {
        return future.runAfterEither(other, action);
    }
    
    @Override
    public CompletionStage<Void> runAfterEitherAsync(CompletionStage<?> other, Runnable action) {
        return future.runAfterEitherAsync(other, action);
    }
    
    @Override
    public CompletionStage<Void> runAfterEitherAsync(CompletionStage<?> other, Runnable action, Executor executor) {
        return future.runAfterEitherAsync(other, action, executor);
    }
    
    @Override
    public <U> CompletionStage<U> thenCompose(Function<? super V, ? extends CompletionStage<U>> fn) {
        return future.thenCompose(fn);
    }
    
    @Override
    public <U> CompletionStage<U> thenComposeAsync(Function<? super V, ? extends CompletionStage<U>> fn) {
        return future.thenComposeAsync(fn);
    }
    
    @Override
    public <U> CompletionStage<U> thenComposeAsync(Function<? super V, ? extends CompletionStage<U>> fn, Executor executor) {
        return future.thenComposeAsync(fn, executor);
    }
    
    @Override
    public <U> CompletionStage<U> handle(BiFunction<? super V, Throwable, ? extends U> fn) {
        return future.handle(fn);
    }
    
    @Override
    public <U> CompletionStage<U> handleAsync(BiFunction<? super V, Throwable, ? extends U> fn) {
        return future.handleAsync(fn);
    }
    
    @Override
    public <U> CompletionStage<U> handleAsync(BiFunction<? super V, Throwable, ? extends U> fn, Executor executor) {
        return future.handleAsync(fn, executor);
    }
    
    @Override
    public CompletionStage<V> whenComplete(BiConsumer<? super V, ? super Throwable> action) {
        return future.whenComplete(action);
    }
    
    @Override
    public CompletionStage<V> whenCompleteAsync(BiConsumer<? super V, ? super Throwable> action) {
        return future.whenCompleteAsync(action);
    }
    
    @Override
    public CompletionStage<V> whenCompleteAsync(BiConsumer<? super V, ? super Throwable> action, Executor executor) {
        return future.whenCompleteAsync(action, executor);
    }
    
    @Override
    public CompletionStage<V> exceptionally(Function<Throwable, ? extends V> fn) {
        return future.exceptionally(fn);
    }
    
    @Override
    public CompletableFuture<V> toCompletableFuture() {
        return future;
    }
}
