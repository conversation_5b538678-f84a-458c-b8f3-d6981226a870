package com.xyy.saas.inquiry.config.redis.local;

import org.redisson.api.RFuture;
import org.redisson.api.RKeys;
import org.redisson.api.RType;
import org.redisson.client.codec.Codec;

import java.util.Collection;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 本地内存实现的 RKeys
 */
public class LocalRKeys implements RKeys {
    
    private final LocalRedissonClient client;
    
    public LocalRKeys(LocalRedissonClient client) {
        this.client = client;
    }
    
    @Override
    public RType getType(String key) {
        if (!client.hasKey(key)) {
            return RType.OBJECT;
        }
        
        Object value = client.getValue(key);
        if (value == null) {
            return RType.OBJECT;
        }
        
        // 根据值的类型返回对应的RType
        if (value instanceof String || value instanceof Number || value instanceof Boolean) {
            return RType.OBJECT;
        } else if (value instanceof java.util.List) {
            return RType.LIST;
        } else if (value instanceof java.util.Set) {
            return RType.SET;
        } else if (value instanceof java.util.Map) {
            return RType.MAP;
        } else {
            return RType.OBJECT;
        }
    }
    
    @Override
    public int getSlot(String key) {
        // 在本地模式下，所有key都在同一个slot
        return 0;
    }
    
    @Override
    public Iterable<String> getKeysByPattern(String pattern) {
        return client.getMemoryStore().keySet().stream()
            .filter(key -> matchesPattern(key, pattern))
            .collect(Collectors.toList());
    }

    @Override
    public Iterable<String> getKeysByPattern(String pattern, int count) {
        return client.getMemoryStore().keySet().stream()
            .filter(key -> matchesPattern(key, pattern))
            .limit(count)
            .collect(Collectors.toList());
    }

    @Override
    public Iterable<String> getKeys() {
        return client.getMemoryStore().keySet();
    }

    @Override
    public Iterable<String> getKeys(int count) {
        return client.getMemoryStore().keySet().stream()
            .limit(count)
            .collect(Collectors.toList());
    }
    
    @Override
    public long delete(String... keys) {
        long count = 0;
        for (String key : keys) {
            if (client.hasKey(key)) {
                client.deleteKey(key);
                count++;
            }
        }
        return count;
    }
    
    @Override
    public long unlink(String... keys) {
        return delete(keys); // 在本地模式下，unlink和delete行为相同
    }
    
    @Override
    public long delete(String key) {
        if (client.hasKey(key)) {
            client.deleteKey(key);
            return 1;
        }
        return 0;
    }
    
    @Override
    public long unlink(String key) {
        return delete(key);
    }
    
    @Override
    public long count() {
        return client.getMemoryStore().size();
    }

    @Override
    public void flushdb() {
        client.getMemoryStore().clear();
        client.getExpirationMap().clear();
        client.getLockMap().clear();
        if (client.getLocalKvStoreMapper() != null) {
            // 清空数据库中的所有数据
            // 这里需要根据实际的Mapper实现来调用相应的清空方法
        }
    }
    
    @Override
    public void flushall() {
        flushdb(); // 在本地模式下，flushall和flushdb行为相同
    }
    
    @Override
    public long randomKey() {
        if (client.getMemoryStore().isEmpty()) {
            return 0;
        }
        return 1; // 简化实现，返回1表示有key存在
    }

    @Override
    public Collection<String> findKeysByPattern(String pattern) {
        return client.getMemoryStore().keySet().stream()
            .filter(key -> matchesPattern(key, pattern))
            .collect(Collectors.toList());
    }
    
    @Override
    public long deleteByPattern(String pattern) {
        Collection<String> keysToDelete = findKeysByPattern(pattern);
        long count = 0;
        for (String key : keysToDelete) {
            client.deleteKey(key);
            count++;
        }
        return count;
    }
    
    @Override
    public boolean move(String name, int database) {
        // 在本地模式下不支持多数据库
        return false;
    }
    
    @Override
    public void migrate(String name, String host, int port, int database, long timeout) {
        throw new UnsupportedOperationException("Migrate not implemented in local mode");
    }
    
    @Override
    public void copy(String name, String host, int port, int database, long timeout) {
        throw new UnsupportedOperationException("Copy not implemented in local mode");
    }
    
    @Override
    public boolean expire(String name, long timeToLive, TimeUnit timeUnit) {
        if (client.hasKey(name)) {
            Object value = client.getValue(name);
            client.putValue(name, value, timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(String name, long timestamp) {
        if (client.hasKey(name)) {
            Object value = client.getValue(name);
            long timeToLive = timestamp - System.currentTimeMillis();
            if (timeToLive > 0) {
                client.putValue(name, value, timeToLive, TimeUnit.MILLISECONDS);
            } else {
                client.deleteKey(name);
            }
            return true;
        }
        return false;
    }
    
    @Override
    public boolean clearExpire(String name) {
        if (client.hasKey(name)) {
            Object value = client.getValue(name);
            client.putValue(name, value); // 重新设置，不带过期时间
            return true;
        }
        return false;
    }
    
    @Override
    public boolean renamenx(String oldName, String newName) {
        if (client.hasKey(oldName) && !client.hasKey(newName)) {
            Object value = client.getValue(oldName);
            client.deleteKey(oldName);
            client.putValue(newName, value);
            return true;
        }
        return false;
    }
    
    @Override
    public void rename(String currentName, String newName) {
        if (client.hasKey(currentName)) {
            Object value = client.getValue(currentName);
            client.deleteKey(currentName);
            client.putValue(newName, value);
        }
    }
    
    @Override
    public long remainTimeToLive(String name) {
        if (!client.hasKey(name)) {
            return -2; // key不存在
        }
        
        Long expireTime = client.getExpirationMap().get(name);
        if (expireTime == null) {
            return -1; // key存在但没有设置过期时间
        }
        
        long remaining = expireTime - System.currentTimeMillis();
        return remaining > 0 ? remaining : -2; // 如果已过期，返回-2
    }
    
    @Override
    public long touch(String... names) {
        long count = 0;
        for (String name : names) {
            if (client.hasKey(name)) {
                count++;
            }
        }
        return count;
    }
    
    @Override
    public String randomKey(String pattern) {
        Collection<String> matchingKeys = findKeysByPattern(pattern);
        if (matchingKeys.isEmpty()) {
            return null;
        }
        return matchingKeys.iterator().next();
    }
    
    @Override
    public RFuture<String> randomKeyAsync() {
        return new LocalRFuture<>(() -> {
            if (client.getMemoryStore().isEmpty()) {
                return null;
            }
            return client.getMemoryStore().keySet().iterator().next();
        });
    }
    
    @Override
    public RFuture<String> randomKeyAsync(String pattern) {
        return new LocalRFuture<>(() -> randomKey(pattern));
    }
    
    @Override
    public RFuture<Collection<String>> findKeysByPatternAsync(String pattern) {
        return new LocalRFuture<>(() -> findKeysByPattern(pattern));
    }
    
    @Override
    public RFuture<Long> deleteByPatternAsync(String pattern) {
        return new LocalRFuture<>(() -> deleteByPattern(pattern));
    }
    
    @Override
    public RFuture<Long> deleteAsync(String... keys) {
        return new LocalRFuture<>(() -> delete(keys));
    }
    
    @Override
    public RFuture<Long> unlinkAsync(String... keys) {
        return new LocalRFuture<>(() -> unlink(keys));
    }
    
    @Override
    public RFuture<Long> countAsync() {
        return new LocalRFuture<>(() -> count());
    }
    
    @Override
    public RFuture<Void> flushdbAsync() {
        return new LocalRFuture<>(() -> {
            flushdb();
            return null;
        });
    }
    
    @Override
    public RFuture<Void> flushallAsync() {
        return new LocalRFuture<>(() -> {
            flushall();
            return null;
        });
    }
    
    @Override
    public RFuture<RType> getTypeAsync(String key) {
        return new LocalRFuture<>(() -> getType(key));
    }
    
    @Override
    public RFuture<Integer> getSlotAsync(String key) {
        return new LocalRFuture<>(() -> getSlot(key));
    }
    
    @Override
    public RFuture<Boolean> moveAsync(String name, int database) {
        return new LocalRFuture<>(() -> move(name, database));
    }
    
    @Override
    public RFuture<Void> migrateAsync(String name, String host, int port, int database, long timeout) {
        return new LocalRFuture<>(() -> {
            migrate(name, host, port, database, timeout);
            return null;
        });
    }
    
    @Override
    public RFuture<Void> copyAsync(String name, String host, int port, int database, long timeout) {
        return new LocalRFuture<>(() -> {
            copy(name, host, port, database, timeout);
            return null;
        });
    }
    
    @Override
    public RFuture<Boolean> expireAsync(String name, long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> expire(name, timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(String name, long timestamp) {
        return new LocalRFuture<>(() -> expireAt(name, timestamp));
    }
    
    @Override
    public RFuture<Boolean> clearExpireAsync(String name) {
        return new LocalRFuture<>(() -> clearExpire(name));
    }
    
    @Override
    public RFuture<Boolean> renamenxAsync(String oldName, String newName) {
        return new LocalRFuture<>(() -> renamenx(oldName, newName));
    }
    
    @Override
    public RFuture<Void> renameAsync(String currentName, String newName) {
        return new LocalRFuture<>(() -> {
            rename(currentName, newName);
            return null;
        });
    }
    
    @Override
    public RFuture<Long> remainTimeToLiveAsync(String name) {
        return new LocalRFuture<>(() -> remainTimeToLive(name));
    }
    
    @Override
    public RFuture<Long> touchAsync(String... names) {
        return new LocalRFuture<>(() -> touch(names));
    }
    
    /**
     * 简单的模式匹配实现
     * 支持 * 和 ? 通配符
     */
    private boolean matchesPattern(String key, String pattern) {
        if (pattern == null || pattern.isEmpty()) {
            return true;
        }
        
        // 将Redis模式转换为Java正则表达式
        String regex = pattern
            .replace("*", ".*")
            .replace("?", ".");
        
        return key.matches(regex);
    }
}
