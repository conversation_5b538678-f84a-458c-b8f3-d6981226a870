package com.xyy.saas.inquiry.config.redis.local;

import org.redisson.api.RFuture;
import org.redisson.api.RList;
import org.redisson.api.SortOrder;
import org.redisson.client.codec.Codec;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;

/**
 * 本地内存实现的 RList
 */
public class LocalRList<V> implements RList<V> {
    
    private final String name;
    private final LocalRedissonClient client;
    private final List<V> list;
    
    @SuppressWarnings("unchecked")
    public LocalRList(String name, LocalRedissonClient client) {
        this.name = name;
        this.client = client;
        
        // 从存储中获取初始值
        Object value = client.getValue(name);
        if (value instanceof List) {
            this.list = new CopyOnWriteArrayList<>((List<V>) value);
        } else {
            this.list = new CopyOnWriteArrayList<>();
        }
    }
    
    private void saveList() {
        client.putValue(name, new ArrayList<>(list));
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public int size() {
        return list.size();
    }
    
    @Override
    public boolean isEmpty() {
        return list.isEmpty();
    }
    
    @Override
    public boolean contains(Object o) {
        return list.contains(o);
    }
    
    @Override
    public Iterator<V> iterator() {
        return list.iterator();
    }
    
    @Override
    public Object[] toArray() {
        return list.toArray();
    }
    
    @Override
    public <T> T[] toArray(T[] a) {
        return list.toArray(a);
    }
    
    @Override
    public boolean add(V v) {
        boolean result = list.add(v);
        if (result) {
            saveList();
        }
        return result;
    }
    
    @Override
    public boolean remove(Object o) {
        boolean result = list.remove(o);
        if (result) {
            saveList();
        }
        return result;
    }
    
    @Override
    public boolean containsAll(Collection<?> c) {
        return list.containsAll(c);
    }
    
    @Override
    public boolean addAll(Collection<? extends V> c) {
        boolean result = list.addAll(c);
        if (result) {
            saveList();
        }
        return result;
    }
    
    @Override
    public boolean addAll(int index, Collection<? extends V> c) {
        boolean result = list.addAll(index, c);
        if (result) {
            saveList();
        }
        return result;
    }
    
    @Override
    public boolean removeAll(Collection<?> c) {
        boolean result = list.removeAll(c);
        if (result) {
            saveList();
        }
        return result;
    }
    
    @Override
    public boolean retainAll(Collection<?> c) {
        boolean result = list.retainAll(c);
        if (result) {
            saveList();
        }
        return result;
    }
    
    @Override
    public void clear() {
        list.clear();
        saveList();
    }
    
    @Override
    public V get(int index) {
        return list.get(index);
    }
    
    @Override
    public V set(int index, V element) {
        V result = list.set(index, element);
        saveList();
        return result;
    }
    
    @Override
    public void add(int index, V element) {
        list.add(index, element);
        saveList();
    }
    
    @Override
    public V remove(int index) {
        V result = list.remove(index);
        saveList();
        return result;
    }
    
    @Override
    public int indexOf(Object o) {
        return list.indexOf(o);
    }
    
    @Override
    public int lastIndexOf(Object o) {
        return list.lastIndexOf(o);
    }
    
    @Override
    public ListIterator<V> listIterator() {
        return list.listIterator();
    }
    
    @Override
    public ListIterator<V> listIterator(int index) {
        return list.listIterator(index);
    }
    
    @Override
    public List<V> subList(int fromIndex, int toIndex) {
        return list.subList(fromIndex, toIndex);
    }
    
    // RList 特有方法
    @Override
    public List<V> get(int... indexes) {
        List<V> result = new ArrayList<>();
        for (int index : indexes) {
            if (index >= 0 && index < list.size()) {
                result.add(list.get(index));
            }
        }
        return result;
    }
    
    @Override
    public void trim(int fromIndex, int toIndex) {
        if (fromIndex < 0) fromIndex = 0;
        if (toIndex >= list.size()) toIndex = list.size() - 1;
        
        List<V> trimmed = new ArrayList<>();
        for (int i = fromIndex; i <= toIndex && i < list.size(); i++) {
            trimmed.add(list.get(i));
        }
        list.clear();
        list.addAll(trimmed);
        saveList();
    }
    
    @Override
    public void fastSet(int index, V value) {
        set(index, value);
    }
    
    @Override
    public List<V> readAll() {
        return new ArrayList<>(list);
    }
    
    @Override
    public List<V> range(int fromIndex, int toIndex) {
        if (fromIndex < 0) fromIndex = 0;
        if (toIndex >= list.size()) toIndex = list.size() - 1;
        
        List<V> result = new ArrayList<>();
        for (int i = fromIndex; i <= toIndex && i < list.size(); i++) {
            result.add(list.get(i));
        }
        return result;
    }
    
    @Override
    public void fastRemove(int index) {
        remove(index);
    }
    
    @Override
    public boolean expire(long timeToLive, TimeUnit timeUnit) {
        if (client.hasKey(name)) {
            client.putValue(name, new ArrayList<>(list), timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(long timestamp) {
        if (client.hasKey(name)) {
            long timeToLive = timestamp - System.currentTimeMillis();
            if (timeToLive > 0) {
                client.putValue(name, new ArrayList<>(list), timeToLive, TimeUnit.MILLISECONDS);
            } else {
                client.deleteKey(name);
            }
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(Instant instant) {
        return expireAt(instant.toEpochMilli());
    }
    
    @Override
    public boolean clearExpire() {
        if (client.hasKey(name)) {
            client.putValue(name, new ArrayList<>(list));
            return true;
        }
        return false;
    }
    
    @Override
    public long remainTimeToLive() {
        return client.hasKey(name) ? -1 : -2;
    }
    
    @Override
    public boolean delete() {
        if (client.hasKey(name)) {
            client.deleteKey(name);
            list.clear();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean unlink() {
        return delete();
    }
    
    @Override
    public boolean isExists() {
        return client.hasKey(name);
    }
    
    @Override
    public Codec getCodec() {
        return null;
    }
    
    // 不支持的方法
    @Override
    public List<V> sort(SortOrder order) {
        throw new UnsupportedOperationException("Sort not implemented in local mode");
    }
    
    @Override
    public List<V> sort(SortOrder order, int offset, int count) {
        throw new UnsupportedOperationException("Sort not implemented in local mode");
    }
    
    @Override
    public List<V> sort(String byPattern, SortOrder order) {
        throw new UnsupportedOperationException("Sort not implemented in local mode");
    }
    
    @Override
    public List<V> sort(String byPattern, SortOrder order, int offset, int count) {
        throw new UnsupportedOperationException("Sort not implemented in local mode");
    }
    
    @Override
    public <T> Collection<T> sort(String byPattern, List<String> getPatterns, SortOrder order) {
        throw new UnsupportedOperationException("Sort not implemented in local mode");
    }
    
    @Override
    public <T> Collection<T> sort(String byPattern, List<String> getPatterns, SortOrder order, int offset, int count) {
        throw new UnsupportedOperationException("Sort not implemented in local mode");
    }
    
    // Java 8+ 方法
    @Override
    public void replaceAll(UnaryOperator<V> operator) {
        list.replaceAll(operator);
        saveList();
    }
    
    @Override
    public void sort(Comparator<? super V> c) {
        list.sort(c);
        saveList();
    }
    
    @Override
    public Spliterator<V> spliterator() {
        return list.spliterator();
    }
    
    @Override
    public boolean removeIf(Predicate<? super V> filter) {
        boolean result = list.removeIf(filter);
        if (result) {
            saveList();
        }
        return result;
    }
    
    @Override
    public Stream<V> stream() {
        return list.stream();
    }
    
    @Override
    public Stream<V> parallelStream() {
        return list.parallelStream();
    }
    
    @Override
    public void forEach(Consumer<? super V> action) {
        list.forEach(action);
    }
    
    // 异步方法（简化实现）
    @Override
    public RFuture<Boolean> addAsync(V e) {
        return new LocalRFuture<>(() -> add(e));
    }
    
    @Override
    public RFuture<Boolean> removeAsync(Object o) {
        return new LocalRFuture<>(() -> remove(o));
    }
    
    @Override
    public RFuture<V> getAsync(int index) {
        return new LocalRFuture<>(() -> get(index));
    }
    
    @Override
    public RFuture<V> setAsync(int index, V element) {
        return new LocalRFuture<>(() -> set(index, element));
    }
    
    @Override
    public RFuture<Void> addAsync(int index, V element) {
        return new LocalRFuture<>(() -> {
            add(index, element);
            return null;
        });
    }
    
    @Override
    public RFuture<V> removeAsync(int index) {
        return new LocalRFuture<>(() -> remove(index));
    }
    
    @Override
    public RFuture<Boolean> containsAsync(Object o) {
        return new LocalRFuture<>(() -> contains(o));
    }
    
    @Override
    public RFuture<Boolean> containsAllAsync(Collection<?> c) {
        return new LocalRFuture<>(() -> containsAll(c));
    }
    
    @Override
    public RFuture<Boolean> removeAllAsync(Collection<?> c) {
        return new LocalRFuture<>(() -> removeAll(c));
    }
    
    @Override
    public RFuture<Boolean> retainAllAsync(Collection<?> c) {
        return new LocalRFuture<>(() -> retainAll(c));
    }
    
    @Override
    public RFuture<Integer> sizeAsync() {
        return new LocalRFuture<>(() -> size());
    }
    
    @Override
    public RFuture<List<V>> readAllAsync() {
        return new LocalRFuture<>(() -> readAll());
    }
    
    @Override
    public RFuture<Void> trimAsync(int fromIndex, int toIndex) {
        return new LocalRFuture<>(() -> {
            trim(fromIndex, toIndex);
            return null;
        });
    }
    
    @Override
    public RFuture<Void> fastSetAsync(int index, V value) {
        return new LocalRFuture<>(() -> {
            fastSet(index, value);
            return null;
        });
    }
    
    @Override
    public RFuture<Boolean> addAllAsync(Collection<? extends V> c) {
        return new LocalRFuture<>(() -> addAll(c));
    }
    
    @Override
    public RFuture<Boolean> addAllAsync(int index, Collection<? extends V> c) {
        return new LocalRFuture<>(() -> addAll(index, c));
    }
    
    @Override
    public RFuture<List<V>> getAsync(int... indexes) {
        return new LocalRFuture<>(() -> get(indexes));
    }
    
    @Override
    public RFuture<List<V>> rangeAsync(int fromIndex, int toIndex) {
        return new LocalRFuture<>(() -> range(fromIndex, toIndex));
    }
    
    @Override
    public RFuture<Void> fastRemoveAsync(int index) {
        return new LocalRFuture<>(() -> {
            fastRemove(index);
            return null;
        });
    }
    
    @Override
    public RFuture<Integer> indexOfAsync(Object o) {
        return new LocalRFuture<>(() -> indexOf(o));
    }
    
    @Override
    public RFuture<Integer> lastIndexOfAsync(Object o) {
        return new LocalRFuture<>(() -> lastIndexOf(o));
    }
    
    @Override
    public RFuture<Boolean> expireAsync(long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> expire(timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(long timestamp) {
        return new LocalRFuture<>(() -> expireAt(timestamp));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(Instant instant) {
        return new LocalRFuture<>(() -> expireAt(instant));
    }
    
    @Override
    public RFuture<Boolean> clearExpireAsync() {
        return new LocalRFuture<>(() -> clearExpire());
    }
    
    @Override
    public RFuture<Long> remainTimeToLiveAsync() {
        return new LocalRFuture<>(() -> remainTimeToLive());
    }
    
    @Override
    public RFuture<Boolean> deleteAsync() {
        return new LocalRFuture<>(() -> delete());
    }
    
    @Override
    public RFuture<Boolean> unlinkAsync() {
        return new LocalRFuture<>(() -> unlink());
    }
    
    @Override
    public RFuture<Boolean> isExistsAsync() {
        return new LocalRFuture<>(() -> isExists());
    }
}
