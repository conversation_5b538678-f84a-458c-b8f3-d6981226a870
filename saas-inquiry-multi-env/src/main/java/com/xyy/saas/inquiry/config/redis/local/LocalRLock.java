package com.xyy.saas.inquiry.config.redis.local;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RFuture;
import org.redisson.api.RLock;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 本地内存实现的 RLock
 * 使用 ReentrantLock 模拟分布式锁
 */
@Slf4j
public class LocalRLock implements RLock {
    
    private final String name;
    private final LocalRedissonClient client;
    private final ReentrantLock lock;
    
    public LocalRLock(String name, LocalRedissonClient client) {
        this.name = name;
        this.client = client;
        this.lock = client.getInternalLock(name);
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public void lockInterruptibly(long leaseTime, TimeUnit unit) throws InterruptedException {
        lock.lockInterruptibly();
        if (leaseTime > 0) {
            // 在本地实现中，我们简单地记录租约时间，但不强制执行
            log.debug("Lock acquired with lease time: {} {}", leaseTime, unit);
        }
    }
    
    @Override
    public boolean tryLock(long waitTime, long leaseTime, TimeUnit unit) throws InterruptedException {
        boolean acquired = lock.tryLock(waitTime, unit);
        if (acquired && leaseTime > 0) {
            log.debug("Lock acquired with lease time: {} {}", leaseTime, unit);
        }
        return acquired;
    }
    
    @Override
    public void lock(long leaseTime, TimeUnit unit) {
        lock.lock();
        if (leaseTime > 0) {
            log.debug("Lock acquired with lease time: {} {}", leaseTime, unit);
        }
    }
    
    @Override
    public boolean forceUnlock() {
        if (lock.isLocked()) {
            // 强制释放锁，即使不是当前线程持有
            while (lock.isLocked()) {
                try {
                    lock.unlock();
                } catch (IllegalMonitorStateException e) {
                    // 如果当前线程不持有锁，则跳出循环
                    break;
                }
            }
            return true;
        }
        return false;
    }
    
    @Override
    public boolean isLocked() {
        return lock.isLocked();
    }
    
    @Override
    public boolean isHeldByThread(long threadId) {
        return lock.isHeldByCurrentThread() && Thread.currentThread().getId() == threadId;
    }
    
    @Override
    public boolean isHeldByCurrentThread() {
        return lock.isHeldByCurrentThread();
    }
    
    @Override
    public int getHoldCount() {
        return lock.getHoldCount();
    }
    
    @Override
    public long remainTimeToLive() {
        // 在本地实现中，我们不跟踪租约时间，返回-1表示永不过期
        return -1;
    }
    
    // 实现 Lock 接口的方法
    @Override
    public void lock() {
        lock.lock();
    }
    
    @Override
    public void lockInterruptibly() throws InterruptedException {
        lock.lockInterruptibly();
    }
    
    @Override
    public boolean tryLock() {
        return lock.tryLock();
    }
    
    @Override
    public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
        return lock.tryLock(time, unit);
    }
    
    @Override
    public void unlock() {
        lock.unlock();
    }
    
    @Override
    public Condition newCondition() {
        return lock.newCondition();
    }
    
    // 异步方法实现（在本地模式下，我们简单地同步执行）
    @Override
    public RFuture<Void> lockAsync() {
        return new LocalRFuture<>(() -> {
            lock();
            return null;
        });
    }
    
    @Override
    public RFuture<Void> lockAsync(long leaseTime, TimeUnit unit) {
        return new LocalRFuture<>(() -> {
            lock(leaseTime, unit);
            return null;
        });
    }
    
    @Override
    public RFuture<Void> lockAsync(long currentThreadId) {
        return new LocalRFuture<>(() -> {
            lock();
            return null;
        });
    }
    
    @Override
    public RFuture<Void> lockAsync(long leaseTime, TimeUnit unit, long currentThreadId) {
        return new LocalRFuture<>(() -> {
            lock(leaseTime, unit);
            return null;
        });
    }
    
    @Override
    public RFuture<Boolean> tryLockAsync() {
        return new LocalRFuture<>(() -> tryLock());
    }
    
    @Override
    public RFuture<Boolean> tryLockAsync(long threadId) {
        return new LocalRFuture<>(() -> tryLock());
    }
    
    @Override
    public RFuture<Boolean> tryLockAsync(long waitTime, TimeUnit unit) {
        return new LocalRFuture<>(() -> {
            try {
                return tryLock(waitTime, unit);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        });
    }
    
    @Override
    public RFuture<Boolean> tryLockAsync(long waitTime, long leaseTime, TimeUnit unit) {
        return new LocalRFuture<>(() -> {
            try {
                return tryLock(waitTime, leaseTime, unit);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        });
    }
    
    @Override
    public RFuture<Boolean> tryLockAsync(long waitTime, long leaseTime, TimeUnit unit, long currentThreadId) {
        return new LocalRFuture<>(() -> {
            try {
                return tryLock(waitTime, leaseTime, unit);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        });
    }
    
    @Override
    public RFuture<Boolean> unlockAsync() {
        return new LocalRFuture<>(() -> {
            unlock();
            return true;
        });
    }
    
    @Override
    public RFuture<Boolean> unlockAsync(long threadId) {
        return new LocalRFuture<>(() -> {
            unlock();
            return true;
        });
    }
    
    @Override
    public RFuture<Boolean> forceUnlockAsync() {
        return new LocalRFuture<>(() -> forceUnlock());
    }
}
