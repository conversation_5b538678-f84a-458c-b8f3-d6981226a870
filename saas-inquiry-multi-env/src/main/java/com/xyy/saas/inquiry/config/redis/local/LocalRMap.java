package com.xyy.saas.inquiry.config.redis.local;

import org.redisson.api.RFuture;
import org.redisson.api.RMap;
import org.redisson.api.mapreduce.RMapReduce;
import org.redisson.client.codec.Codec;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 本地内存实现的 RMap
 */
public class LocalRMap<K, V> implements RMap<K, V> {
    
    private final String name;
    private final LocalRedissonClient client;
    private final Map<K, V> map;
    
    @SuppressWarnings("unchecked")
    public LocalRMap(String name, LocalRedissonClient client) {
        this.name = name;
        this.client = client;
        
        // 从存储中获取初始值
        Object value = client.getValue(name);
        if (value instanceof Map) {
            this.map = new ConcurrentHashMap<>((Map<K, V>) value);
        } else {
            this.map = new ConcurrentHashMap<>();
        }
    }
    
    private void saveMap() {
        client.putValue(name, new HashMap<>(map));
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public int size() {
        return map.size();
    }
    
    @Override
    public boolean isEmpty() {
        return map.isEmpty();
    }
    
    @Override
    public boolean containsKey(Object key) {
        return map.containsKey(key);
    }
    
    @Override
    public boolean containsValue(Object value) {
        return map.containsValue(value);
    }
    
    @Override
    public V get(Object key) {
        return map.get(key);
    }
    
    @Override
    public V put(K key, V value) {
        V result = map.put(key, value);
        saveMap();
        return result;
    }
    
    @Override
    public V remove(Object key) {
        V result = map.remove(key);
        if (result != null) {
            saveMap();
        }
        return result;
    }
    
    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        map.putAll(m);
        saveMap();
    }
    
    @Override
    public void clear() {
        map.clear();
        saveMap();
    }
    
    @Override
    public Set<K> keySet() {
        return map.keySet();
    }
    
    @Override
    public Collection<V> values() {
        return map.values();
    }
    
    @Override
    public Set<Entry<K, V>> entrySet() {
        return map.entrySet();
    }
    
    // RMap 特有方法
    @Override
    public V putIfAbsent(K key, V value) {
        V result = map.putIfAbsent(key, value);
        if (result == null) {
            saveMap();
        }
        return result;
    }
    
    @Override
    public boolean remove(Object key, Object value) {
        boolean result = map.remove(key, value);
        if (result) {
            saveMap();
        }
        return result;
    }
    
    @Override
    public boolean replace(K key, V oldValue, V newValue) {
        boolean result = map.replace(key, oldValue, newValue);
        if (result) {
            saveMap();
        }
        return result;
    }
    
    @Override
    public V replace(K key, V value) {
        V result = map.replace(key, value);
        if (result != null) {
            saveMap();
        }
        return result;
    }
    
    @Override
    public V getOrDefault(Object key, V defaultValue) {
        return map.getOrDefault(key, defaultValue);
    }
    
    @Override
    public void forEach(BiConsumer<? super K, ? super V> action) {
        map.forEach(action);
    }
    
    @Override
    public void replaceAll(BiFunction<? super K, ? super V, ? extends V> function) {
        map.replaceAll(function);
        saveMap();
    }
    
    @Override
    public V computeIfAbsent(K key, Function<? super K, ? extends V> mappingFunction) {
        V result = map.computeIfAbsent(key, mappingFunction);
        saveMap();
        return result;
    }
    
    @Override
    public V computeIfPresent(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        V result = map.computeIfPresent(key, remappingFunction);
        saveMap();
        return result;
    }
    
    @Override
    public V compute(K key, BiFunction<? super K, ? super V, ? extends V> remappingFunction) {
        V result = map.compute(key, remappingFunction);
        saveMap();
        return result;
    }
    
    @Override
    public V merge(K key, V value, BiFunction<? super V, ? super V, ? extends V> remappingFunction) {
        V result = map.merge(key, value, remappingFunction);
        saveMap();
        return result;
    }
    
    @Override
    public Map<K, V> getAll(Set<K> keys) {
        Map<K, V> result = new HashMap<>();
        for (K key : keys) {
            V value = map.get(key);
            if (value != null) {
                result.put(key, value);
            }
        }
        return result;
    }
    
    @Override
    public long fastRemove(K... keys) {
        long count = 0;
        for (K key : keys) {
            if (map.remove(key) != null) {
                count++;
            }
        }
        if (count > 0) {
            saveMap();
        }
        return count;
    }
    
    @Override
    public boolean fastPut(K key, V value) {
        boolean isNew = !map.containsKey(key);
        map.put(key, value);
        saveMap();
        return isNew;
    }
    
    @Override
    public boolean fastReplace(K key, V value) {
        if (map.containsKey(key)) {
            map.put(key, value);
            saveMap();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean fastPutIfAbsent(K key, V value) {
        if (!map.containsKey(key)) {
            map.put(key, value);
            saveMap();
            return true;
        }
        return false;
    }
    
    @Override
    public Set<K> readAllKeySet() {
        return new HashSet<>(map.keySet());
    }
    
    @Override
    public Collection<V> readAllValues() {
        return new ArrayList<>(map.values());
    }
    
    @Override
    public Set<Entry<K, V>> readAllEntrySet() {
        return new HashSet<>(map.entrySet());
    }
    
    @Override
    public Map<K, V> readAllMap() {
        return new HashMap<>(map);
    }
    
    @Override
    public V addAndGet(K key, Number delta) {
        throw new UnsupportedOperationException("addAndGet not implemented in local mode");
    }
    
    @Override
    public boolean expire(long timeToLive, TimeUnit timeUnit) {
        if (client.hasKey(name)) {
            client.putValue(name, new HashMap<>(map), timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(long timestamp) {
        if (client.hasKey(name)) {
            long timeToLive = timestamp - System.currentTimeMillis();
            if (timeToLive > 0) {
                client.putValue(name, new HashMap<>(map), timeToLive, TimeUnit.MILLISECONDS);
            } else {
                client.deleteKey(name);
            }
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(Instant instant) {
        return expireAt(instant.toEpochMilli());
    }
    
    @Override
    public boolean clearExpire() {
        if (client.hasKey(name)) {
            client.putValue(name, new HashMap<>(map));
            return true;
        }
        return false;
    }
    
    @Override
    public long remainTimeToLive() {
        return client.hasKey(name) ? -1 : -2;
    }
    
    @Override
    public boolean delete() {
        if (client.hasKey(name)) {
            client.deleteKey(name);
            map.clear();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean unlink() {
        return delete();
    }
    
    @Override
    public boolean isExists() {
        return client.hasKey(name);
    }
    
    @Override
    public Codec getCodec() {
        return null;
    }
    
    // 不支持的方法
    @Override
    public RMapReduce<K, V, Object> mapReduce() {
        throw new UnsupportedOperationException("MapReduce not implemented in local mode");
    }
    
    // 异步方法（简化实现）
    @Override
    public RFuture<V> getAsync(K key) {
        return new LocalRFuture<>(() -> get(key));
    }
    
    @Override
    public RFuture<V> putAsync(K key, V value) {
        return new LocalRFuture<>(() -> put(key, value));
    }
    
    @Override
    public RFuture<V> removeAsync(K key) {
        return new LocalRFuture<>(() -> remove(key));
    }
    
    @Override
    public RFuture<Boolean> containsKeyAsync(K key) {
        return new LocalRFuture<>(() -> containsKey(key));
    }
    
    @Override
    public RFuture<Boolean> containsValueAsync(V value) {
        return new LocalRFuture<>(() -> containsValue(value));
    }
    
    @Override
    public RFuture<Map<K, V>> getAllAsync(Set<K> keys) {
        return new LocalRFuture<>(() -> getAll(keys));
    }
    
    @Override
    public RFuture<Void> putAllAsync(Map<? extends K, ? extends V> map) {
        return new LocalRFuture<>(() -> {
            putAll(map);
            return null;
        });
    }
    
    @Override
    public RFuture<V> putIfAbsentAsync(K key, V value) {
        return new LocalRFuture<>(() -> putIfAbsent(key, value));
    }
    
    @Override
    public RFuture<Boolean> removeAsync(Object key, Object value) {
        return new LocalRFuture<>(() -> remove(key, value));
    }
    
    @Override
    public RFuture<Boolean> replaceAsync(K key, V oldValue, V newValue) {
        return new LocalRFuture<>(() -> replace(key, oldValue, newValue));
    }
    
    @Override
    public RFuture<V> replaceAsync(K key, V value) {
        return new LocalRFuture<>(() -> replace(key, value));
    }
    
    @Override
    public RFuture<Long> fastRemoveAsync(K... keys) {
        return new LocalRFuture<>(() -> fastRemove(keys));
    }
    
    @Override
    public RFuture<Boolean> fastPutAsync(K key, V value) {
        return new LocalRFuture<>(() -> fastPut(key, value));
    }
    
    @Override
    public RFuture<Boolean> fastReplaceAsync(K key, V value) {
        return new LocalRFuture<>(() -> fastReplace(key, value));
    }
    
    @Override
    public RFuture<Boolean> fastPutIfAbsentAsync(K key, V value) {
        return new LocalRFuture<>(() -> fastPutIfAbsent(key, value));
    }
    
    @Override
    public RFuture<Set<K>> readAllKeySetAsync() {
        return new LocalRFuture<>(() -> readAllKeySet());
    }
    
    @Override
    public RFuture<Collection<V>> readAllValuesAsync() {
        return new LocalRFuture<>(() -> readAllValues());
    }
    
    @Override
    public RFuture<Set<Entry<K, V>>> readAllEntrySetAsync() {
        return new LocalRFuture<>(() -> readAllEntrySet());
    }
    
    @Override
    public RFuture<Map<K, V>> readAllMapAsync() {
        return new LocalRFuture<>(() -> readAllMap());
    }
    
    @Override
    public RFuture<Integer> sizeAsync() {
        return new LocalRFuture<>(() -> size());
    }
    
    @Override
    public RFuture<Boolean> expireAsync(long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> expire(timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(long timestamp) {
        return new LocalRFuture<>(() -> expireAt(timestamp));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(Instant instant) {
        return new LocalRFuture<>(() -> expireAt(instant));
    }
    
    @Override
    public RFuture<Boolean> clearExpireAsync() {
        return new LocalRFuture<>(() -> clearExpire());
    }
    
    @Override
    public RFuture<Long> remainTimeToLiveAsync() {
        return new LocalRFuture<>(() -> remainTimeToLive());
    }
    
    @Override
    public RFuture<Boolean> deleteAsync() {
        return new LocalRFuture<>(() -> delete());
    }
    
    @Override
    public RFuture<Boolean> unlinkAsync() {
        return new LocalRFuture<>(() -> unlink());
    }
    
    @Override
    public RFuture<Boolean> isExistsAsync() {
        return new LocalRFuture<>(() -> isExists());
    }
}
