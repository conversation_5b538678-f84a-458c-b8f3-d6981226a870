package com.xyy.saas.inquiry.config.redis.local;

import org.redisson.api.RFuture;
import org.redisson.api.RQueue;
import org.redisson.client.codec.Codec;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;

/**
 * 本地内存实现的 RQueue
 */
public class LocalRQueue<V> implements RQueue<V> {
    
    private final String name;
    private final LocalRedissonClient client;
    private final Queue<V> queue;
    
    @SuppressWarnings("unchecked")
    public LocalRQueue(String name, LocalRedissonClient client) {
        this.name = name;
        this.client = client;
        
        // 从存储中获取初始值
        Object value = client.getValue(name);
        if (value instanceof Collection) {
            this.queue = new ConcurrentLinkedQueue<>((Collection<V>) value);
        } else {
            this.queue = new ConcurrentLinkedQueue<>();
        }
    }
    
    private void saveQueue() {
        client.putValue(name, new ArrayList<>(queue));
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public boolean add(V v) {
        boolean result = queue.add(v);
        if (result) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public boolean offer(V v) {
        boolean result = queue.offer(v);
        if (result) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public V remove() {
        V result = queue.remove();
        saveQueue();
        return result;
    }
    
    @Override
    public V poll() {
        V result = queue.poll();
        if (result != null) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public V element() {
        return queue.element();
    }
    
    @Override
    public V peek() {
        return queue.peek();
    }
    
    @Override
    public int size() {
        return queue.size();
    }
    
    @Override
    public boolean isEmpty() {
        return queue.isEmpty();
    }
    
    @Override
    public boolean contains(Object o) {
        return queue.contains(o);
    }
    
    @Override
    public Iterator<V> iterator() {
        return queue.iterator();
    }
    
    @Override
    public Object[] toArray() {
        return queue.toArray();
    }
    
    @Override
    public <T> T[] toArray(T[] a) {
        return queue.toArray(a);
    }
    
    @Override
    public boolean remove(Object o) {
        boolean result = queue.remove(o);
        if (result) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public boolean containsAll(Collection<?> c) {
        return queue.containsAll(c);
    }
    
    @Override
    public boolean addAll(Collection<? extends V> c) {
        boolean result = queue.addAll(c);
        if (result) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public boolean removeAll(Collection<?> c) {
        boolean result = queue.removeAll(c);
        if (result) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public boolean retainAll(Collection<?> c) {
        boolean result = queue.retainAll(c);
        if (result) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public void clear() {
        queue.clear();
        saveQueue();
    }
    
    // RQueue 特有方法
    @Override
    public List<V> readAll() {
        return new ArrayList<>(queue);
    }
    
    @Override
    public V pollLastAndOfferFirstTo(String queueName) {
        throw new UnsupportedOperationException("pollLastAndOfferFirstTo not implemented in local mode");
    }
    
    @Override
    public boolean expire(long timeToLive, TimeUnit timeUnit) {
        if (client.hasKey(name)) {
            client.putValue(name, new ArrayList<>(queue), timeToLive, timeUnit);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(long timestamp) {
        if (client.hasKey(name)) {
            long timeToLive = timestamp - System.currentTimeMillis();
            if (timeToLive > 0) {
                client.putValue(name, new ArrayList<>(queue), timeToLive, TimeUnit.MILLISECONDS);
            } else {
                client.deleteKey(name);
            }
            return true;
        }
        return false;
    }
    
    @Override
    public boolean expireAt(Instant instant) {
        return expireAt(instant.toEpochMilli());
    }
    
    @Override
    public boolean clearExpire() {
        if (client.hasKey(name)) {
            client.putValue(name, new ArrayList<>(queue));
            return true;
        }
        return false;
    }
    
    @Override
    public long remainTimeToLive() {
        return client.hasKey(name) ? -1 : -2;
    }
    
    @Override
    public boolean delete() {
        if (client.hasKey(name)) {
            client.deleteKey(name);
            queue.clear();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean unlink() {
        return delete();
    }
    
    @Override
    public boolean isExists() {
        return client.hasKey(name);
    }
    
    @Override
    public Codec getCodec() {
        return null;
    }
    
    // Java 8+ 方法
    @Override
    public Spliterator<V> spliterator() {
        return queue.spliterator();
    }
    
    @Override
    public boolean removeIf(Predicate<? super V> filter) {
        boolean result = queue.removeIf(filter);
        if (result) {
            saveQueue();
        }
        return result;
    }
    
    @Override
    public Stream<V> stream() {
        return queue.stream();
    }
    
    @Override
    public Stream<V> parallelStream() {
        return queue.parallelStream();
    }
    
    @Override
    public void forEach(Consumer<? super V> action) {
        queue.forEach(action);
    }
    
    // 异步方法（简化实现）
    @Override
    public RFuture<Boolean> addAsync(V e) {
        return new LocalRFuture<>(() -> add(e));
    }
    
    @Override
    public RFuture<Boolean> offerAsync(V e) {
        return new LocalRFuture<>(() -> offer(e));
    }
    
    @Override
    public RFuture<V> removeAsync() {
        return new LocalRFuture<>(() -> remove());
    }
    
    @Override
    public RFuture<V> pollAsync() {
        return new LocalRFuture<>(() -> poll());
    }
    
    @Override
    public RFuture<V> elementAsync() {
        return new LocalRFuture<>(() -> element());
    }
    
    @Override
    public RFuture<V> peekAsync() {
        return new LocalRFuture<>(() -> peek());
    }
    
    @Override
    public RFuture<Boolean> removeAsync(Object o) {
        return new LocalRFuture<>(() -> remove(o));
    }
    
    @Override
    public RFuture<Boolean> containsAsync(Object o) {
        return new LocalRFuture<>(() -> contains(o));
    }
    
    @Override
    public RFuture<Boolean> containsAllAsync(Collection<?> c) {
        return new LocalRFuture<>(() -> containsAll(c));
    }
    
    @Override
    public RFuture<Boolean> removeAllAsync(Collection<?> c) {
        return new LocalRFuture<>(() -> removeAll(c));
    }
    
    @Override
    public RFuture<Boolean> retainAllAsync(Collection<?> c) {
        return new LocalRFuture<>(() -> retainAll(c));
    }
    
    @Override
    public RFuture<Integer> sizeAsync() {
        return new LocalRFuture<>(() -> size());
    }
    
    @Override
    public RFuture<List<V>> readAllAsync() {
        return new LocalRFuture<>(() -> readAll());
    }
    
    @Override
    public RFuture<V> pollLastAndOfferFirstToAsync(String queueName) {
        return new LocalRFuture<>(() -> {
            throw new UnsupportedOperationException("pollLastAndOfferFirstTo not implemented in local mode");
        });
    }
    
    @Override
    public RFuture<Boolean> addAllAsync(Collection<? extends V> c) {
        return new LocalRFuture<>(() -> addAll(c));
    }
    
    @Override
    public RFuture<Boolean> expireAsync(long timeToLive, TimeUnit timeUnit) {
        return new LocalRFuture<>(() -> expire(timeToLive, timeUnit));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(long timestamp) {
        return new LocalRFuture<>(() -> expireAt(timestamp));
    }
    
    @Override
    public RFuture<Boolean> expireAtAsync(Instant instant) {
        return new LocalRFuture<>(() -> expireAt(instant));
    }
    
    @Override
    public RFuture<Boolean> clearExpireAsync() {
        return new LocalRFuture<>(() -> clearExpire());
    }
    
    @Override
    public RFuture<Long> remainTimeToLiveAsync() {
        return new LocalRFuture<>(() -> remainTimeToLive());
    }
    
    @Override
    public RFuture<Boolean> deleteAsync() {
        return new LocalRFuture<>(() -> delete());
    }
    
    @Override
    public RFuture<Boolean> unlinkAsync() {
        return new LocalRFuture<>(() -> unlink());
    }
    
    @Override
    public RFuture<Boolean> isExistsAsync() {
        return new LocalRFuture<>(() -> isExists());
    }
}
