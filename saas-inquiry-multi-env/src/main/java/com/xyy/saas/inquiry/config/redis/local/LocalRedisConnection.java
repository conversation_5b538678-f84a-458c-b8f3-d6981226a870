

package com.xyy.saas.inquiry.config.redis.local;

import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisCommands;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.connection.RedisHashCommands;
import org.springframework.data.redis.connection.RedisHyperLogLogCommands;
import org.springframework.data.redis.connection.RedisKeyCommands;
import org.springframework.data.redis.connection.RedisListCommands;
import org.springframework.data.redis.connection.RedisScriptingCommands;
import org.springframework.data.redis.connection.RedisSentinelConnection;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.connection.RedisSetCommands;
import org.springframework.data.redis.connection.RedisStreamCommands;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.connection.RedisZSetCommands;
import org.springframework.data.redis.connection.Subscription;

/**
 * 本地内存实现的 RedisConnection
 * 用于本地开发环境，避免依赖真实的 Redis 服务
 */
public class LocalRedisConnection implements RedisConnection {

    private final Map<byte[], byte[]> dataStore = new ConcurrentHashMap<>();
    private final Map<byte[], Long> expirationMap = new ConcurrentHashMap<>();
    private boolean connected = true;
    private boolean inTransaction = false;
    private boolean pipelined = false;

    @Override
    public void close() {
        connected = false;
    }

    @Override
    public boolean isClosed() {
        return !connected;
    }

    @Override
    public Object getNativeConnection() {
        return this;
    }

    /**
     * @return the {@link RedisSentinelConnection} when using Redis Sentinel.
     * @since 1.4
     */
    @Override
    public RedisSentinelConnection getSentinelConnection() {
        return null;
    }

    @Override
    public boolean isQueueing() {
        return inTransaction;
    }

    @Override
    public boolean isPipelined() {
        return pipelined;
    }

    @Override
    public void openPipeline() {
        pipelined = true;
    }

    @Override
    public List<Object> closePipeline() {
        pipelined = false;
        return List.of();
    }

    @Override
    public void multi() {
        inTransaction = true;
    }

    @Override
    public List<Object> exec() {
        inTransaction = false;
        return List.of();
    }

    @Override
    public void discard() {
        inTransaction = false;
    }

    @Override
    public void watch(byte[]... keys) {
        // 本地模式不需要实际操作
    }

    @Override
    public void unwatch() {
        // 本地模式不需要实际操作
    }

    @Override
    public boolean isSubscribed() {
        return false;
    }

    @Override
    public Subscription getSubscription() {
        return null;
    }

    @Override
    public Long publish(byte[] channel, byte[] message) {
        return 0L;
    }

    @Override
    public void subscribe(MessageListener listener, byte[]... channels) {
        // 本地模式不需要实际操作
    }

    @Override
    public void pSubscribe(MessageListener listener, byte[]... patterns) {
        // 本地模式不需要实际操作
    }

    @Override
    public void select(int dbIndex) {
        if (dbIndex != 0) {
            throw new UnsupportedOperationException("Multiple databases not supported in local mode");
        }
    }

    @Override
    public byte[] echo(byte[] message) {
        return message;
    }

    @Override
    public String ping() {
        return "PONG";
    }

    @Override
    public void flushDb() {
        dataStore.clear();
        expirationMap.clear();
    }

    @Override
    public void flushAll() {
        flushDb();
    }

    @Override
    public void bgSave() {
        // 本地模式不需要实际操作
    }

    @Override
    public void bgReWriteAof() {
        // 本地模式不需要实际操作
    }

    @Override
    public void save() {
        // 本地模式不需要实际操作
    }

    @Override
    public Long lastSave() {
        return System.currentTimeMillis();
    }

    @Override
    public void shutdown() {
        close();
    }

    @Override
    public Properties info() {
        Properties props = new Properties();
        props.setProperty("redis_version", "local-mock");
        props.setProperty("connected_clients", "1");
        return props;
    }

    @Override
    public Properties info(String section) {
        return info();
    }

    @Override
    public Long dbSize() {
        return (long) dataStore.size();
    }

    @Override
    public void resetConfigStats() {
        // 本地模式不需要实际操作
    }



    @Override
    public void setClientName(byte[] name) {
        // 本地模式不需要实际操作
    }

    @Override
    public String getClientName() {
        return "local-redis-client";
    }

    @Override
    public void killClient(String host, int port) {
        // 本地模式不需要实际操作
    }


    /**
     * {@literal Native} or {@literal raw} execution of the given Redis command along with the given arguments.
     * <p>
     * The command is executed as is, with as little interpretation as possible - it is up to the caller to take care
     * of any processing of arguments or the result.
     *
     * @param command Redis {@link String command} to execute; must not be {@literal null}.
     * @param args    optional array of command arguments; may be empty;
     * @return the execution result; may be {@literal null}.
     */
    @Override
    public Object execute(String command, byte[]... args) {
        return null;
    }

    /**
     * Get {@link RedisCommands}.
     *
     * @return never {@literal null}.
     * @since 3.0
     */
    @Override
    public RedisCommands commands() {
        return null;
    }

    /**
     * Get {@link RedisGeoCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisGeoCommands geoCommands() {
        return null;
    }

    /**
     * Get {@link RedisHashCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisHashCommands hashCommands() {
        return null;
    }

    /**
     * Get {@link RedisHyperLogLogCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisHyperLogLogCommands hyperLogLogCommands() {
        return null;
    }

    /**
     * Get {@link RedisKeyCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisKeyCommands keyCommands() {
        return null;
    }

    /**
     * Get {@link RedisListCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisListCommands listCommands() {
        return null;
    }

    /**
     * Get {@link RedisSetCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisSetCommands setCommands() {
        return null;
    }

    /**
     * Get {@link RedisScriptingCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisScriptingCommands scriptingCommands() {
        return null;
    }

    /**
     * Get {@link RedisServerCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisServerCommands serverCommands() {
        return null;
    }

    /**
     * Get {@link RedisStreamCommands}.
     *
     * @return never {@literal null}.
     * @since 2.2
     */
    @Override
    public RedisStreamCommands streamCommands() {
        return null;
    }

    /**
     * Get {@link RedisStringCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisStringCommands stringCommands() {
        return null;
    }

    /**
     * Get {@link RedisZSetCommands}.
     *
     * @return never {@literal null}.
     * @since 2.0
     */
    @Override
    public RedisZSetCommands zSetCommands() {
        return null;
    }
}
