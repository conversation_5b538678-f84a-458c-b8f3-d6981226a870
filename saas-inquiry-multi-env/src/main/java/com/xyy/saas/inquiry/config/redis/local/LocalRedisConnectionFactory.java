package com.xyy.saas.inquiry.config.redis.local;

import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConnection;
import org.springframework.data.redis.connection.RedisClusterConnection;

/**
 * 本地内存实现的 RedisConnectionFactory
 * 用于本地开发环境，避免依赖真实的 Redis 服务
 */
public class LocalRedisConnectionFactory implements RedisConnectionFactory {

    private final LocalRedisConnection connection = new LocalRedisConnection();

    @Override
    public RedisConnection getConnection() {
        return connection;
    }

    @Override
    public RedisClusterConnection getClusterConnection() {
        throw new UnsupportedOperationException("Cluster connections not supported in local mode");
    }

    @Override
    public boolean getConvertPipelineAndTxResults() {
        return true;
    }

    @Override
    public RedisSentinelConnection getSentinelConnection() {
        throw new UnsupportedOperationException("Sentinel connections not supported in local mode");
    }

    @Override
    public DataAccessException translateExceptionIfPossible(RuntimeException ex) {
        return null;
    }
}
