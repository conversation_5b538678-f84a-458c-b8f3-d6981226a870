package com.xyy.saas.inquiry.config.redis.local;

import com.xyy.saas.inquiry.localmock.redis.LocalKvStoreMapper;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStorePO;
import org.springframework.beans.factory.annotation.Autowired;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * 本地内存实现的 RedisTemplate
 * 使用 ConcurrentHashMap 模拟 Redis 操作
 */
@Slf4j
public class LocalRedisTemplate<K, V> extends RedisTemplate<K, V> {

    @Autowired
    private LocalKvStoreMapper localKvStoreMapper;
    
    private final Map<String, Object> memoryStore = new ConcurrentHashMap<>();
    private final Map<String, Long> expirationMap = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public LocalRedisTemplate() {
        setKeySerializer(new StringRedisSerializer());
        setValueSerializer(RedisSerializer.json());
        setHashKeySerializer(new StringRedisSerializer());
        setHashValueSerializer(RedisSerializer.json());
    }
    
    public void putValue(String key, Object value) {
        memoryStore.put(key, value);
        try {
            LocalKvStorePO po = new LocalKvStorePO();
            po.setKey(key);
            po.setValue(objectMapper.writeValueAsString(value));
            localKvStoreMapper.insert(po);
        } catch (JsonProcessingException e) {
            log.error("Error serializing value for key {}: {}", key, e.getMessage());
        }
    }
    
    public void putValue(String key, Object value, long timeout, TimeUnit unit) {
        memoryStore.put(key, value);
        long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
        expirationMap.put(key, expireTime);
        try {
            LocalKvStorePO po = new LocalKvStorePO();
            po.setKey(key);
            po.setValue(objectMapper.writeValueAsString(value));
            po.setExpireTime(expireTime);
            localKvStoreMapper.insert(po);
        } catch (JsonProcessingException e) {
            log.error("Error serializing value for key {}: {}", key, e.getMessage());
        }
    }
    
    public Object getValue(String key) {
        Object value = memoryStore.get(key);
        if (value == null) {
            LocalKvStorePO po = localKvStoreMapper.selectById(key);
            if (po != null) {
                if (po.getExpireTime() != null && System.currentTimeMillis() > po.getExpireTime()) {
                    localKvStoreMapper.deleteById(key); // Delete expired from DB
                    return null;
                }
                try {
                    value = objectMapper.readValue(po.getValue(), Object.class);
                    memoryStore.put(key, value);
                    if (po.getExpireTime() != null) {
                        expirationMap.put(key, po.getExpireTime());
                    }
                } catch (JsonProcessingException e) {
                    log.error("Error deserializing value for key {}: {}", key, e.getMessage());
                    return null;
                }
            }
        }

        if (value != null && isExpired(key)) { // Check expiration after potentially loading from DB
            memoryStore.remove(key);
            expirationMap.remove(key);
            localKvStoreMapper.deleteById(key); // Delete from DB if expired
            return null;
        }
        return value;
    }
    
    public Boolean hasKeyInternal(String key) {
        if (memoryStore.containsKey(key)) {
            return !isExpired(key);
        }
        LocalKvStorePO po = localKvStoreMapper.selectById(key);
        if (po != null) {
            if (po.getExpireTime() != null && System.currentTimeMillis() > po.getExpireTime()) {
                localKvStoreMapper.deleteById(key);
                return false;
            }
            // Load into memory for future access
            try {
                memoryStore.put(key, objectMapper.readValue(po.getValue(), Object.class));
                if (po.getExpireTime() != null) {
                    expirationMap.put(key, po.getExpireTime());
                }
            } catch (JsonProcessingException e) {
                log.error("Error deserializing value for key {}: {}", key, e.getMessage());
                return false;
            }
            return true;
        }
        return false;
    }
    
    public void deleteKey(String key) {
        memoryStore.remove(key);
        expirationMap.remove(key);
        localKvStoreMapper.deleteById(key);
    }
    
    public void deleteKeys(Collection<String> keys) {
        for (String key : keys) {
            deleteKey(key);
            localKvStoreMapper.deleteById(key);
        }
    }
    
    private boolean isExpired(String key) {
        Long expireTime = expirationMap.get(key);
        return expireTime != null && System.currentTimeMillis() > expireTime;
    }
    
    @Override
    public ValueOperations<K, V> opsForValue() {
        return new LocalValueOperations<>(this);
    }
    
    @Override
    public HashOperations<K, Object, Object> opsForHash() {
        return new LocalHashOperations<>(this);
    }
    
    @Override
    public Boolean hasKey(K key) {
        return hasKeyInternal(key.toString());
    }
    
    @Override
    public Boolean delete(K key) {
        deleteKey(key.toString());
        return true;
    }
    
    @Override
    public Long delete(Collection<K> keys) {
        long count = 0;
        for (K key : keys) {
            if (hasKey(key)) {
                delete(key);
                count++;
            }
        }
        return count;
    }

    /**
     * Set time to live for given {@code key}.
     *
     * @param key     must not be {@literal null}.
     * @param timeout must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @throws IllegalArgumentException if the timeout is {@literal null}.
     * @since 2.3
     */
    @Override
    public Boolean expire(K key, Duration timeout) {
        String keyStr = key.toString();
        if (!hasKeyInternal(keyStr)) {
            return false;
        }
        long expireTime = System.currentTimeMillis() + timeout.toMillis();
        expirationMap.put(keyStr, expireTime);

        LocalKvStorePO po = localKvStoreMapper.selectById(keyStr);
        if (po != null) {
            po.setExpireTime(expireTime);
            localKvStoreMapper.updateById(po);
        }
        return true;
    }

    /**
     * Set the expiration for given {@code key} as a {@literal date} timestamp.
     *
     * @param key      must not be {@literal null}.
     * @param expireAt must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @throws IllegalArgumentException if the instant is {@literal null} or too large to represent as a {@code Date}.
     * @since 2.3
     */
    @Override
    public Boolean expireAt(K key, Instant expireAt) {
        String keyStr = key.toString();
        if (!hasKeyInternal(keyStr)) {
            return false;
        }
        long expireTime = expireAt.toEpochMilli();
        expirationMap.put(keyStr, expireTime);

        LocalKvStorePO po = localKvStoreMapper.selectById(keyStr);
        if (po != null) {
            po.setExpireTime(expireTime);
            localKvStoreMapper.updateById(po);
        }
        return true;
    }

    /**
     * Create {@code key} using the {@code serializedValue}, previously obtained using {@link #dump(Object)}.
     *
     * @param key        must not be {@literal null}.
     * @param value      must not be {@literal null}.
     * @param timeToLive
     * @param unit       must not be {@literal null}.
     * @see <a href="https://redis.io/commands/restore">Redis Documentation: RESTORE</a>
     */
    @Override
    public void restore(K key, byte[] value, long timeToLive, TimeUnit unit) {
        throw new UnsupportedOperationException("restore not implemented in local mode");
    }
}
