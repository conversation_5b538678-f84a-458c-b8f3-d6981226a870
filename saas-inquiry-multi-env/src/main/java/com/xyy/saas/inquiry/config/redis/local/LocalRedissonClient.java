package com.xyy.saas.inquiry.config.redis.local;

import com.xyy.saas.inquiry.localmock.redis.LocalKvStoreMapper;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStorePO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.api.listener.MessageListener;
import org.redisson.api.listener.PatternMessageListener;
import org.redisson.api.listener.StatusListener;
import org.redisson.client.codec.Codec;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.Map;
import java.util.UUID;

/**
 * 本地内存实现的 RedissonClient
 * 使用 ConcurrentHashMap 模拟 Redisson 分布式操作
 * 主要用于本地开发环境，避免依赖真实的 Redis 服务
 */
@Slf4j
public class LocalRedissonClient implements RedissonClient {

    @Autowired
    private LocalKvStoreMapper localKvStoreMapper;
    
    private final Map<String, Object> memoryStore = new ConcurrentHashMap<>();
    private final Map<String, Long> expirationMap = new ConcurrentHashMap<>();
    private final Map<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 清理过期数据的方法
    private void cleanExpiredKeys() {
        long currentTime = System.currentTimeMillis();
        expirationMap.entrySet().removeIf(entry -> {
            if (currentTime > entry.getValue()) {
                String key = entry.getKey();
                memoryStore.remove(key);
                lockMap.remove(key);
                if (localKvStoreMapper != null) {
                    localKvStoreMapper.deleteById(key);
                }
                return true;
            }
            return false;
        });
    }
    
    private boolean isExpired(String key) {
        Long expireTime = expirationMap.get(key);
        if (expireTime != null && System.currentTimeMillis() > expireTime) {
            memoryStore.remove(key);
            expirationMap.remove(key);
            lockMap.remove(key);
            if (localKvStoreMapper != null) {
                localKvStoreMapper.deleteById(key);
            }
            return true;
        }
        return false;
    }
    
    @Override
    public RLock getLock(String name) {
        return new LocalRLock(name, this);
    }
    
    @Override
    public RLock getFairLock(String name) {
        return new LocalRLock(name, this);
    }
    
    @Override
    public RReadWriteLock getReadWriteLock(String name) {
        throw new UnsupportedOperationException("ReadWriteLock not implemented in local mode");
    }
    
    @Override
    public <V> RBucket<V> getBucket(String name) {
        return new LocalRBucket<>(name, this);
    }
    
    @Override
    public <V> RBucket<V> getBucket(String name, Codec codec) {
        return new LocalRBucket<>(name, this);
    }
    
    @Override
    public RBuckets getBuckets() {
        throw new UnsupportedOperationException("Buckets not implemented in local mode");
    }
    
    @Override
    public RBuckets getBuckets(Codec codec) {
        throw new UnsupportedOperationException("Buckets not implemented in local mode");
    }
    
    @Override
    public <V> RList<V> getList(String name) {
        return new LocalRList<>(name, this);
    }
    
    @Override
    public <V> RList<V> getList(String name, Codec codec) {
        return new LocalRList<>(name, this);
    }
    
    @Override
    public <K, V> RMap<K, V> getMap(String name) {
        return new LocalRMap<>(name, this);
    }
    
    @Override
    public <K, V> RMap<K, V> getMap(String name, Codec codec) {
        return new LocalRMap<>(name, this);
    }
    
    @Override
    public <V> RSet<V> getSet(String name) {
        return new LocalRSet<>(name, this);
    }
    
    @Override
    public <V> RSet<V> getSet(String name, Codec codec) {
        return new LocalRSet<>(name, this);
    }
    
    @Override
    public RAtomicLong getAtomicLong(String name) {
        return new LocalRAtomicLong(name, this);
    }
    
    @Override
    public RAtomicDouble getAtomicDouble(String name) {
        throw new UnsupportedOperationException("AtomicDouble not implemented in local mode");
    }
    
    @Override
    public RCountDownLatch getCountDownLatch(String name) {
        throw new UnsupportedOperationException("CountDownLatch not implemented in local mode");
    }
    
    @Override
    public RSemaphore getSemaphore(String name) {
        throw new UnsupportedOperationException("Semaphore not implemented in local mode");
    }
    
    @Override
    public RPermitExpirableSemaphore getPermitExpirableSemaphore(String name) {
        throw new UnsupportedOperationException("PermitExpirableSemaphore not implemented in local mode");
    }
    
    @Override
    public RTopic getTopic(String name) {
        throw new UnsupportedOperationException("Topic not implemented in local mode");
    }
    
    @Override
    public RTopic getTopic(String name, Codec codec) {
        throw new UnsupportedOperationException("Topic not implemented in local mode");
    }
    
    @Override
    public RPatternTopic getPatternTopic(String pattern) {
        throw new UnsupportedOperationException("PatternTopic not implemented in local mode");
    }
    
    @Override
    public RPatternTopic getPatternTopic(String pattern, Codec codec) {
        throw new UnsupportedOperationException("PatternTopic not implemented in local mode");
    }
    
    @Override
    public <V> RQueue<V> getQueue(String name) {
        return new LocalRQueue<>(name, this);
    }
    
    @Override
    public <V> RQueue<V> getQueue(String name, Codec codec) {
        return new LocalRQueue<>(name, this);
    }
    
    @Override
    public <V> RBlockingQueue<V> getBlockingQueue(String name) {
        throw new UnsupportedOperationException("BlockingQueue not implemented in local mode");
    }
    
    @Override
    public <V> RBlockingQueue<V> getBlockingQueue(String name, Codec codec) {
        throw new UnsupportedOperationException("BlockingQueue not implemented in local mode");
    }
    
    @Override
    public <V> RDeque<V> getDeque(String name) {
        throw new UnsupportedOperationException("Deque not implemented in local mode");
    }
    
    @Override
    public <V> RDeque<V> getDeque(String name, Codec codec) {
        throw new UnsupportedOperationException("Deque not implemented in local mode");
    }
    
    @Override
    public <V> RBlockingDeque<V> getBlockingDeque(String name) {
        throw new UnsupportedOperationException("BlockingDeque not implemented in local mode");
    }
    
    @Override
    public <V> RBlockingDeque<V> getBlockingDeque(String name, Codec codec) {
        throw new UnsupportedOperationException("BlockingDeque not implemented in local mode");
    }
    
    @Override
    public RScript getScript() {
        throw new UnsupportedOperationException("Script not implemented in local mode");
    }
    
    @Override
    public RScript getScript(Codec codec) {
        throw new UnsupportedOperationException("Script not implemented in local mode");
    }
    
    @Override
    public RScheduledExecutorService getExecutorService(String name) {
        throw new UnsupportedOperationException("ExecutorService not implemented in local mode");
    }
    
    @Override
    public RScheduledExecutorService getExecutorService(String name, Codec codec) {
        throw new UnsupportedOperationException("ExecutorService not implemented in local mode");
    }
    
    @Override
    public RRemoteService getRemoteService() {
        throw new UnsupportedOperationException("RemoteService not implemented in local mode");
    }
    
    @Override
    public RRemoteService getRemoteService(Codec codec) {
        throw new UnsupportedOperationException("RemoteService not implemented in local mode");
    }
    
    @Override
    public RRemoteService getRemoteService(String name) {
        throw new UnsupportedOperationException("RemoteService not implemented in local mode");
    }
    
    @Override
    public RRemoteService getRemoteService(String name, Codec codec) {
        throw new UnsupportedOperationException("RemoteService not implemented in local mode");
    }
    
    @Override
    public RTransaction createTransaction(TransactionOptions options) {
        throw new UnsupportedOperationException("Transaction not implemented in local mode");
    }
    
    @Override
    public RBatch createBatch(BatchOptions options) {
        throw new UnsupportedOperationException("Batch not implemented in local mode");
    }
    
    @Override
    public RBatch createBatch() {
        throw new UnsupportedOperationException("Batch not implemented in local mode");
    }
    
    @Override
    public RKeys getKeys() {
        return new LocalRKeys(this);
    }
    
    @Override
    public Config getConfig() {
        return new Config();
    }
    
    @Override
    public NodesGroup<Node> getNodesGroup() {
        throw new UnsupportedOperationException("NodesGroup not implemented in local mode");
    }
    
    @Override
    public ClusterNodesGroup getClusterNodesGroup() {
        throw new UnsupportedOperationException("ClusterNodesGroup not implemented in local mode");
    }
    
    @Override
    public boolean isShutdown() {
        return false;
    }
    
    @Override
    public boolean isShuttingDown() {
        return false;
    }
    
    @Override
    public String getId() {
        return "local-redisson-client-" + UUID.randomUUID().toString();
    }
    
    @Override
    public void shutdown() {
        log.info("LocalRedissonClient shutdown");
        memoryStore.clear();
        expirationMap.clear();
        lockMap.clear();
    }
    
    @Override
    public void shutdown(long quietPeriod, long timeout, TimeUnit unit) {
        shutdown();
    }
    
    // 内部方法，供本地实现类使用
    public void putValue(String key, Object value) {
        memoryStore.put(key, value);
        if (localKvStoreMapper != null) {
            try {
                LocalKvStorePO po = new LocalKvStorePO();
                po.setKey(key);
                po.setValue(objectMapper.writeValueAsString(value));
                localKvStoreMapper.insertOrUpdate(po);
            } catch (JsonProcessingException e) {
                log.error("Error serializing value for key {}: {}", key, e.getMessage());
            }
        }
    }
    
    public void putValue(String key, Object value, long timeout, TimeUnit unit) {
        memoryStore.put(key, value);
        long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
        expirationMap.put(key, expireTime);
        if (localKvStoreMapper != null) {
            try {
                LocalKvStorePO po = new LocalKvStorePO();
                po.setKey(key);
                po.setValue(objectMapper.writeValueAsString(value));
                po.setExpireTime(expireTime);
                localKvStoreMapper.insertOrUpdate(po);
            } catch (JsonProcessingException e) {
                log.error("Error serializing value for key {}: {}", key, e.getMessage());
            }
        }
    }
    
    public Object getValue(String key) {
        if (isExpired(key)) {
            return null;
        }
        
        Object value = memoryStore.get(key);
        if (value == null && localKvStoreMapper != null) {
            LocalKvStorePO po = localKvStoreMapper.selectById(key);
            if (po != null) {
                if (po.getExpireTime() != null && System.currentTimeMillis() > po.getExpireTime()) {
                    localKvStoreMapper.deleteById(key);
                    return null;
                }
                try {
                    value = objectMapper.readValue(po.getValue(), Object.class);
                    memoryStore.put(key, value);
                    if (po.getExpireTime() != null) {
                        expirationMap.put(key, po.getExpireTime());
                    }
                } catch (JsonProcessingException e) {
                    log.error("Error deserializing value for key {}: {}", key, e.getMessage());
                    return null;
                }
            }
        }
        return value;
    }
    
    public boolean hasKey(String key) {
        if (isExpired(key)) {
            return false;
        }
        
        if (memoryStore.containsKey(key)) {
            return true;
        }
        
        if (localKvStoreMapper != null) {
            LocalKvStorePO po = localKvStoreMapper.selectById(key);
            if (po != null) {
                if (po.getExpireTime() != null && System.currentTimeMillis() > po.getExpireTime()) {
                    localKvStoreMapper.deleteById(key);
                    return false;
                }
                return true;
            }
        }
        return false;
    }
    
    public void deleteKey(String key) {
        memoryStore.remove(key);
        expirationMap.remove(key);
        lockMap.remove(key);
        if (localKvStoreMapper != null) {
            localKvStoreMapper.deleteById(key);
        }
    }
    
    public ReentrantLock getInternalLock(String key) {
        return lockMap.computeIfAbsent(key, k -> new ReentrantLock());
    }

    // Getter方法供LocalRKeys使用
    public Map<String, Object> getMemoryStore() {
        return memoryStore;
    }

    public Map<String, Long> getExpirationMap() {
        return expirationMap;
    }

    public Map<String, ReentrantLock> getLockMap() {
        return lockMap;
    }

    public LocalKvStoreMapper getLocalKvStoreMapper() {
        return localKvStoreMapper;
    }
}
