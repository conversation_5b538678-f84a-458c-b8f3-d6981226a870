package com.xyy.saas.inquiry.config.redis.local;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * LocalRedissonClient 使用演示
 * 在本地环境下演示各种RedissonClient功能的使用
 */
@Slf4j
@Component
@Profile("local")
public class LocalRedissonClientDemo implements CommandLineRunner {
    
    @Autowired
    private RedissonClient redissonClient;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("=== LocalRedissonClient 功能演示开始 ===");
        
        // 演示分布式锁
        demonstrateLock();
        
        // 演示分布式对象存储
        demonstrateBucket();
        
        // 演示分布式Map
        demonstrateMap();
        
        // 演示分布式List
        demonstrateList();
        
        // 演示分布式Set
        demonstrateSet();
        
        // 演示分布式Queue
        demonstrateQueue();
        
        // 演示原子操作
        demonstrateAtomicLong();
        
        // 演示键管理
        demonstrateKeys();
        
        log.info("=== LocalRedissonClient 功能演示结束 ===");
    }
    
    private void demonstrateLock() {
        log.info("--- 分布式锁演示 ---");
        
        RLock lock = redissonClient.getLock("demo:lock");
        
        try {
            // 尝试获取锁，等待时间10秒，锁定时间30秒
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                log.info("成功获取分布式锁");
                log.info("锁是否被当前线程持有: {}", lock.isHeldByCurrentThread());
                log.info("锁的持有次数: {}", lock.getHoldCount());
                
                // 模拟业务处理
                Thread.sleep(1000);
                
                log.info("释放分布式锁");
                lock.unlock();
            } else {
                log.warn("获取分布式锁失败");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取锁时被中断", e);
        }
    }
    
    private void demonstrateBucket() {
        log.info("--- 分布式对象存储演示 ---");
        
        RBucket<String> bucket = redissonClient.getBucket("demo:bucket");
        
        // 设置值
        bucket.set("Hello LocalRedissonClient!");
        log.info("设置值: {}", bucket.get());
        
        // 设置带过期时间的值
        bucket.set("这个值会在5秒后过期", 5, TimeUnit.SECONDS);
        log.info("设置带过期时间的值: {}", bucket.get());
        
        // 原子操作
        String oldValue = bucket.getAndSet("新的值");
        log.info("原子替换，旧值: {}, 新值: {}", oldValue, bucket.get());
        
        // 条件设置
        RBucket<String> newBucket = redissonClient.getBucket("demo:new-bucket");
        boolean success = newBucket.trySet("只有不存在时才设置");
        log.info("条件设置成功: {}, 值: {}", success, newBucket.get());
    }
    
    private void demonstrateMap() {
        log.info("--- 分布式Map演示 ---");
        
        RMap<String, Object> map = redissonClient.getMap("demo:map");
        
        // 基本操作
        map.put("name", "张三");
        map.put("age", 25);
        map.put("city", "北京");
        
        log.info("Map大小: {}", map.size());
        log.info("姓名: {}", map.get("name"));
        log.info("年龄: {}", map.get("age"));
        
        // 批量操作
        log.info("所有键: {}", map.keySet());
        log.info("所有值: {}", map.values());
        
        // 原子操作
        Object oldAge = map.putIfAbsent("age", 30);
        log.info("putIfAbsent结果 - 旧值: {}, 当前值: {}", oldAge, map.get("age"));
        
        // 快速操作
        boolean fastPutResult = map.fastPut("email", "<EMAIL>");
        log.info("fastPut结果: {}, 邮箱: {}", fastPutResult, map.get("email"));
    }
    
    private void demonstrateList() {
        log.info("--- 分布式List演示 ---");
        
        RList<String> list = redissonClient.getList("demo:list");
        
        // 添加元素
        list.add("第一个元素");
        list.add("第二个元素");
        list.add("第三个元素");
        list.add(1, "插入的元素"); // 在索引1处插入
        
        log.info("List大小: {}", list.size());
        log.info("所有元素: {}", list.readAll());
        
        // 获取指定范围的元素
        log.info("前两个元素: {}", list.range(0, 1));
        
        // 删除元素
        String removed = list.remove(1);
        log.info("删除的元素: {}, 剩余元素: {}", removed, list.readAll());
    }
    
    private void demonstrateSet() {
        log.info("--- 分布式Set演示 ---");
        
        RSet<String> set = redissonClient.getSet("demo:set");
        
        // 添加元素
        set.add("苹果");
        set.add("香蕉");
        set.add("橙子");
        set.add("苹果"); // 重复元素不会被添加
        
        log.info("Set大小: {}", set.size());
        log.info("所有元素: {}", set.readAll());
        
        // 检查元素存在
        log.info("是否包含苹果: {}", set.contains("苹果"));
        log.info("是否包含葡萄: {}", set.contains("葡萄"));
        
        // 随机操作
        String randomElement = set.random();
        log.info("随机元素: {}", randomElement);
        
        String removedElement = set.removeRandom();
        log.info("随机删除的元素: {}, 剩余元素: {}", removedElement, set.readAll());
    }
    
    private void demonstrateQueue() {
        log.info("--- 分布式Queue演示 ---");
        
        RQueue<String> queue = redissonClient.getQueue("demo:queue");
        
        // 入队
        queue.offer("任务1");
        queue.offer("任务2");
        queue.offer("任务3");
        
        log.info("队列大小: {}", queue.size());
        log.info("所有任务: {}", queue.readAll());
        
        // 查看队首元素（不删除）
        String peek = queue.peek();
        log.info("队首元素: {}", peek);
        log.info("队列大小（peek后）: {}", queue.size());
        
        // 出队
        String task1 = queue.poll();
        String task2 = queue.poll();
        log.info("处理任务: {}, {}", task1, task2);
        log.info("剩余任务: {}", queue.readAll());
    }
    
    private void demonstrateAtomicLong() {
        log.info("--- 原子操作演示 ---");
        
        RAtomicLong counter = redissonClient.getAtomicLong("demo:counter");
        
        // 设置初始值
        counter.set(100);
        log.info("初始值: {}", counter.get());
        
        // 原子递增
        long incremented = counter.incrementAndGet();
        log.info("递增后: {}", incremented);
        
        // 原子加法
        long added = counter.addAndGet(10);
        log.info("加10后: {}", added);
        
        // 比较并设置
        boolean casResult = counter.compareAndSet(111, 200);
        log.info("CAS操作结果: {}, 当前值: {}", casResult, counter.get());
        
        // 获取并设置
        long oldValue = counter.getAndSet(0);
        log.info("getAndSet - 旧值: {}, 新值: {}", oldValue, counter.get());
    }
    
    private void demonstrateKeys() {
        log.info("--- 键管理演示 ---");
        
        RKeys keys = redissonClient.getKeys();
        
        // 创建一些测试数据
        redissonClient.getBucket("user:1001").set("张三");
        redissonClient.getBucket("user:1002").set("李四");
        redissonClient.getBucket("product:2001").set("iPhone");
        redissonClient.getBucket("product:2002").set("iPad");
        
        // 获取所有键的数量
        log.info("总键数: {}", keys.count());
        
        // 模式匹配查找
        log.info("用户相关的键: {}", keys.findKeysByPattern("user:*"));
        log.info("产品相关的键: {}", keys.findKeysByPattern("product:*"));
        
        // 设置过期时间
        boolean expireResult = keys.expire("user:1001", 10, TimeUnit.SECONDS);
        log.info("设置过期时间结果: {}", expireResult);
        
        // 获取剩余生存时间
        long ttl = keys.remainTimeToLive("user:1001");
        log.info("user:1001 剩余生存时间: {} 毫秒", ttl);
        
        // 批量删除
        long deletedCount = keys.deleteByPattern("demo:*");
        log.info("删除demo:*模式的键数量: {}", deletedCount);
    }
}
