package com.xyy.saas.inquiry.config.redis.local;

import com.xyy.saas.inquiry.localmock.redis.LocalKvStoreMapper;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStorePO;
import org.springframework.beans.factory.annotation.Autowired;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * 本地内存实现的 StringRedisTemplate
 * 使用 ConcurrentHashMap 模拟 Redis 字符串操作
 */
@Slf4j
public class LocalStringRedisTemplate extends StringRedisTemplate {

    @Autowired
    private LocalKvStoreMapper localKvStoreMapper;
    
    private final Map<String, String> memoryStore = new ConcurrentHashMap<>();
    private final Map<String, Long> expirationMap = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public LocalStringRedisTemplate() {
        setKeySerializer(new StringRedisSerializer());
        setValueSerializer(new StringRedisSerializer());
        setHashKeySerializer(new StringRedisSerializer());
        setHashValueSerializer(new StringRedisSerializer());
    }
    
    @Override
    public ValueOperations<String, String> opsForValue() {
        return new LocalStringValueOperations(this);
    }
    
    @Override
    public Boolean hasKey(String key) {
        if (memoryStore.containsKey(key)) {
            return !isExpired(key);
        }
        LocalKvStorePO po = localKvStoreMapper.selectById(key);
        if (po != null) {
            if (po.getExpireTime() != null && System.currentTimeMillis() > po.getExpireTime()) {
                localKvStoreMapper.deleteById(key);
                return false;
            }
            // Load into memory for future access
            memoryStore.put(key, po.getValue());
            if (po.getExpireTime() != null) {
                expirationMap.put(key, po.getExpireTime());
            }
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean delete(String key) {
        memoryStore.remove(key);
        expirationMap.remove(key);
        localKvStoreMapper.deleteById(key);
        return true;
    }
    
    @Override
    public Long delete(Collection<String> keys) {
        long count = 0;
        for (String key : keys) {
            if (hasKey(key)) {
                delete(key);
                count++;
            }
        }
        return count;
    }
    
    public void putValue(String key, String value) {
        memoryStore.put(key, value);
        LocalKvStorePO po = new LocalKvStorePO();
        po.setKey(key);
        po.setValue(value);
        localKvStoreMapper.insertOrUpdate(po);
    }
    
    public void putValue(String key, String value, long timeout, TimeUnit unit) {
        memoryStore.put(key, value);
        long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
        expirationMap.put(key, expireTime);
        LocalKvStorePO po = new LocalKvStorePO();
        po.setKey(key);
        po.setValue(value);
        po.setExpireTime(expireTime);
        localKvStoreMapper.insertOrUpdate(po);
    }
    
    public String getValue(String key) {
        String value = memoryStore.get(key);
        if (value == null) {
            LocalKvStorePO po = localKvStoreMapper.selectById(key);
            if (po != null) {
                if (po.getExpireTime() != null && System.currentTimeMillis() > po.getExpireTime()) {
                    localKvStoreMapper.deleteById(key);
                    return null;
                }
                value = po.getValue();
                memoryStore.put(key, value);
                if (po.getExpireTime() != null) {
                    expirationMap.put(key, po.getExpireTime());
                }
            }
        }

        if (value != null && isExpired(key)) {
            memoryStore.remove(key);
            expirationMap.remove(key);
            localKvStoreMapper.deleteById(key);
            return null;
        }
        return value;
    }
    
    public Long increment(String key, long delta) {
        String value = getValue(key);
        long newValue = (value != null ? Long.parseLong(value) : 0) + delta;
        putValue(key, String.valueOf(newValue));
        return newValue;
    }
    
    private boolean isExpired(String key) {
        Long expireTime = expirationMap.get(key);
        return expireTime != null && System.currentTimeMillis() > expireTime;
    }

    /**
     * Set time to live for given {@code key}.
     *
     * @param key     must not be {@literal null}.
     * @param timeout must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @throws IllegalArgumentException if the timeout is {@literal null}.
     * @since 2.3
     */
    @Override
    public Boolean expire(String key, Duration timeout) {
        if (!hasKey(key)) {
            return false;
        }
        long expireTime = System.currentTimeMillis() + timeout.toMillis();
        expirationMap.put(key, expireTime);

        LocalKvStorePO po = localKvStoreMapper.selectById(key);
        if (po != null) {
            po.setExpireTime(expireTime);
            localKvStoreMapper.updateById(po);
        }
        return true;
    }

    /**
     * Set the expiration for given {@code key} as a {@literal date} timestamp.
     *
     * @param key      must not be {@literal null}.
     * @param expireAt must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @throws IllegalArgumentException if the instant is {@literal null} or too large to represent as a {@code Date}.
     * @since 2.3
     */
    @Override
    public Boolean expireAt(String key, Instant expireAt) {
        if (!hasKey(key)) {
            return false;
        }
        long expireTime = expireAt.toEpochMilli();
        expirationMap.put(key, expireTime);

        LocalKvStorePO po = localKvStoreMapper.selectById(key);
        if (po != null) {
            po.setExpireTime(expireTime);
            localKvStoreMapper.updateById(po);
        }
        return true;
    }

    /**
     * Create {@code key} using the {@code serializedValue}, previously obtained using {@link #dump(Object)}.
     *
     * @param key        must not be {@literal null}.
     * @param value      must not be {@literal null}.
     * @param timeToLive
     * @param unit       must not be {@literal null}.
     * @see <a href="https://redis.io/commands/restore">Redis Documentation: RESTORE</a>
     */
    @Override
    public void restore(String key, byte[] value, long timeToLive, TimeUnit unit) {
        throw new UnsupportedOperationException("restore not implemented in local mode");
    }
}
