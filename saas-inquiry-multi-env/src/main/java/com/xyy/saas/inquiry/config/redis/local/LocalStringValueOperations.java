package com.xyy.saas.inquiry.config.redis.local;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.ValueOperations;

/**
 * 本地内存实现的 String ValueOperations
 */
public class LocalStringValueOperations implements ValueOperations<String, String> {
    
    private final LocalStringRedisTemplate template;
    
    public LocalStringValueOperations(LocalStringRedisTemplate template) {
        this.template = template;
    }
    
    @Override
    public void set(String key, String value) {
        template.putValue(key, value);
    }
    
    @Override
    public void set(String key, String value, long timeout, TimeUnit unit) {
        template.putValue(key, value, timeout, unit);
    }
    
    @Override
    public void set(String key, String value, Duration timeout) {
        template.putValue(key, value, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public Boolean setIfAbsent(String key, String value) {
        if (!template.hasKey(key)) {
            template.putValue(key, value);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfAbsent(String key, String value, long timeout, TimeUnit unit) {
        if (!template.hasKey(key)) {
            template.putValue(key, value, timeout, unit);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfAbsent(String key, String value, Duration timeout) {
        return setIfAbsent(key, value, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public Boolean setIfPresent(String key, String value) {
        if (template.hasKey(key)) {
            template.putValue(key, value);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfPresent(String key, String value, long timeout, TimeUnit unit) {
        if (template.hasKey(key)) {
            template.putValue(key, value, timeout, unit);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfPresent(String key, String value, Duration timeout) {
        return setIfPresent(key, value, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public String get(Object key) {
        return template.getValue(key.toString());
    }
    
    @Override
    public String getAndDelete(String key) {
        String value = get(key);
        template.delete(key);
        return value;
    }
    
    @Override
    public String getAndExpire(String key, long timeout, TimeUnit unit) {
        String value = get(key);
        if (value != null) {
            template.putValue(key, value, timeout, unit);
        }
        return value;
    }
    
    @Override
    public String getAndExpire(String key, Duration timeout) {
        return getAndExpire(key, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public String getAndPersist(String key) {
        return get(key);
    }
    
    @Override
    public String getAndSet(String key, String value) {
        String oldValue = get(key);
        set(key, value);
        return oldValue;
    }
    
    @Override
    public List<String> multiGet(Collection<String> keys) {
        throw new UnsupportedOperationException("multiGet not implemented in local mode");
    }
    
    @Override
    public void multiSet(Map<? extends String, ? extends String> map) {
        for (Map.Entry<? extends String, ? extends String> entry : map.entrySet()) {
            set(entry.getKey(), entry.getValue());
        }
    }
    
    @Override
    public Boolean multiSetIfAbsent(Map<? extends String, ? extends String> map) {
        for (String key : map.keySet()) {
            if (template.hasKey(key)) {
                return false;
            }
        }
        multiSet(map);
        return true;
    }
    
    @Override
    public Long increment(String key) {
        return template.increment(key, 1L);
    }
    
    @Override
    public Long increment(String key, long delta) {
        return template.increment(key, delta);
    }
    
    @Override
    public Double increment(String key, double delta) {
        String value = get(key);
        double newValue = (value != null ? Double.parseDouble(value) : 0.0) + delta;
        set(key, String.valueOf(newValue));
        return newValue;
    }
    
    @Override
    public Long decrement(String key) {
        return template.increment(key, -1L);
    }
    
    @Override
    public Long decrement(String key, long delta) {
        return template.increment(key, -delta);
    }
    
    @Override
    public Integer append(String key, String value) {
        String existing = get(key);
        String newValue = (existing != null ? existing : "") + value;
        set(key, newValue);
        return newValue.length();
    }
    
    @Override
    public String get(String key, long start, long end) {
        String value = get(key);
        if (value == null) return null;
        int len = value.length();
        int startIdx = (int) (start < 0 ? len + start : start);
        int endIdx = (int) (end < 0 ? len + end : end);
        startIdx = Math.max(0, Math.min(startIdx, len));
        endIdx = Math.max(0, Math.min(endIdx + 1, len));
        return startIdx < endIdx ? value.substring(startIdx, endIdx) : "";
    }
    
    @Override
    public void set(String key, String value, long offset) {
        throw new UnsupportedOperationException("set with offset not implemented in local mode");
    }
    
    @Override
    public Long size(String key) {
        String value = get(key);
        return value != null ? (long) value.length() : 0L;
    }
    
    @Override
    public Boolean setBit(String key, long offset, boolean value) {
        throw new UnsupportedOperationException("setBit not implemented in local mode");
    }
    
    @Override
    public Boolean getBit(String key, long offset) {
        throw new UnsupportedOperationException("getBit not implemented in local mode");
    }




    /**
     * Get / Manipulate specific integer fields of varying bit widths and arbitrary non (necessary) aligned offset stored
     * at a given {@code key}.
     *
     * @param key         must not be {@literal null}.
     * @param subCommands must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @see <a href="https://redis.io/commands/bitfield">Redis Documentation: BITFIELD</a>
     * @since 2.1
     */
    @Override
    public List<Long> bitField(String key, BitFieldSubCommands subCommands) {
        throw new UnsupportedOperationException("bitField not implemented in local mode");
    }

    @Override
    public RedisOperations<String, String> getOperations() {
        return template;
    }
}
