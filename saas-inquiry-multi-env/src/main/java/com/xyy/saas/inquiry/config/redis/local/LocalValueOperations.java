package com.xyy.saas.inquiry.config.redis.local;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.ValueOperations;

/**
 * 本地内存实现的 ValueOperations
 */
public class LocalValueOperations<K, V> implements ValueOperations<K, V> {
    
    private final LocalRedisTemplate<K, V> template;
    
    public LocalValueOperations(LocalRedisTemplate<K, V> template) {
        this.template = template;
    }
    
    @Override
    public void set(K key, V value) {
        template.putValue(key.toString(), value);
    }
    
    @Override
    public void set(K key, V value, long timeout, TimeUnit unit) {
        template.putValue(key.toString(), value, timeout, unit);
    }
    
    @Override
    public void set(K key, V value, Duration timeout) {
        template.putValue(key.toString(), value, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public Boolean setIfAbsent(K key, V value) {
        if (!template.hasKeyInternal(key.toString())) {
            template.putValue(key.toString(), value);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfAbsent(K key, V value, long timeout, TimeUnit unit) {
        if (!template.hasKeyInternal(key.toString())) {
            template.putValue(key.toString(), value, timeout, unit);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfAbsent(K key, V value, Duration timeout) {
        return setIfAbsent(key, value, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public Boolean setIfPresent(K key, V value) {
        if (template.hasKeyInternal(key.toString())) {
            template.putValue(key.toString(), value);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfPresent(K key, V value, long timeout, TimeUnit unit) {
        if (template.hasKeyInternal(key.toString())) {
            template.putValue(key.toString(), value, timeout, unit);
            return true;
        }
        return false;
    }
    
    @Override
    public Boolean setIfPresent(K key, V value, Duration timeout) {
        return setIfPresent(key, value, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public V get(Object key) {
        return (V) template.getValue(key.toString());
    }
    
    @Override
    public V getAndDelete(K key) {
        V value = get(key);
        template.deleteKey(key.toString());
        return value;
    }
    
    @Override
    public V getAndExpire(K key, long timeout, TimeUnit unit) {
        V value = get(key);
        if (value != null) {
            template.putValue(key.toString(), value, timeout, unit);
        }
        return value;
    }
    
    @Override
    public V getAndExpire(K key, Duration timeout) {
        return getAndExpire(key, timeout.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public V getAndPersist(K key) {
        return get(key);
    }
    
    @Override
    public V getAndSet(K key, V value) {
        V oldValue = get(key);
        set(key, value);
        return oldValue;
    }
    
    @Override
    public List<V> multiGet(Collection<K> keys) {
        throw new UnsupportedOperationException("multiGet not implemented in local mode");
    }
    
    @Override
    public void multiSet(Map<? extends K, ? extends V> map) {
        for (Map.Entry<? extends K, ? extends V> entry : map.entrySet()) {
            set(entry.getKey(), entry.getValue());
        }
    }
    
    @Override
    public Boolean multiSetIfAbsent(Map<? extends K, ? extends V> map) {
        for (K key : map.keySet()) {
            if (template.hasKeyInternal(key.toString())) {
                return false;
            }
        }
        multiSet(map);
        return true;
    }
    
    @Override
    public Long increment(K key) {
        throw new UnsupportedOperationException("increment not supported for generic ValueOperations");
    }
    
    @Override
    public Long increment(K key, long delta) {
        throw new UnsupportedOperationException("increment not supported for generic ValueOperations");
    }
    
    @Override
    public Double increment(K key, double delta) {
        throw new UnsupportedOperationException("increment not supported for generic ValueOperations");
    }
    
    @Override
    public Long decrement(K key) {
        throw new UnsupportedOperationException("decrement not supported for generic ValueOperations");
    }
    
    @Override
    public Long decrement(K key, long delta) {
        throw new UnsupportedOperationException("decrement not supported for generic ValueOperations");
    }
    
    @Override
    public Integer append(K key, String value) {
        throw new UnsupportedOperationException("append not implemented in local mode");
    }
    
    @Override
    public String get(K key, long start, long end) {
        throw new UnsupportedOperationException("get with range not implemented in local mode");
    }
    
    @Override
    public void set(K key, V value, long offset) {
        throw new UnsupportedOperationException("set with offset not implemented in local mode");
    }
    
    @Override
    public Long size(K key) {
        V value = get(key);
        return value != null ? (long) value.toString().length() : 0L;
    }
    
    @Override
    public Boolean setBit(K key, long offset, boolean value) {
        throw new UnsupportedOperationException("setBit not implemented in local mode");
    }
    
    @Override
    public Boolean getBit(K key, long offset) {
        throw new UnsupportedOperationException("getBit not implemented in local mode");
    }

    /**
     * Get / Manipulate specific integer fields of varying bit widths and arbitrary non (necessary) aligned offset stored
     * at a given {@code key}.
     *
     * @param key         must not be {@literal null}.
     * @param subCommands must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @see <a href="https://redis.io/commands/bitfield">Redis Documentation: BITFIELD</a>
     * @since 2.1
     */
    @Override
    public List<Long> bitField(K key, BitFieldSubCommands subCommands) {
        throw new UnsupportedOperationException("bitField not implemented in local mode");
    }

    @Override
    public RedisOperations<K, V> getOperations() {
        return template;
    }
}
