package com.xyy.saas.inquiry.config.servlet;


import com.xyy.common.config.TomcatServerConfig;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
// @Profile("!local")
@ConditionalOnClass(name = "com.xyy.common.config.TomcatServerConfig")
public class TomcatServerConfiguration {

    /**
     *
     * @return
     */
    @Bean(initMethod = "")
    public TomcatServerConfig tomcatServerConfig() {
        TomcatServerConfig tomcatServerConfig = new TomcatServerConfig();
        String configUrl = "";
        try {
            configUrl = System.getProperty("user.dir") + "/tomcat_server.conf";
            if (configUrl.startsWith("file:")) {
                configUrl = configUrl.replace("file:", "");
            }
            File file = new File(configUrl);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();
            log.info("文件创建成功:{}", configUrl);
        } catch (FileNotFoundException var2) {
            FileNotFoundException e = var2;
            log.error("获取路径异常：", e);
        } catch (IOException var3) {
            IOException e = var3;
            log.error("创建文件异常：", e);
        }
        TomcatServerConfig.configUrl = configUrl;
        return tomcatServerConfig;
    }
}
