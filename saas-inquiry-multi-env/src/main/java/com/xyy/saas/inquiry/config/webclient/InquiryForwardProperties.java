package com.xyy.saas.inquiry.config.webclient;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/11/26 9:32
 */
@Data
@ConfigurationProperties(prefix = "inquiry.forward")
public class InquiryForwardProperties {

    /**
     * 转发服务sign 统一key
     */
    private String sign;
    /**
     * 转发服务domain
     */
    private String domain;

    /**
     * 超时时间 单位 s
     */
    private Integer httpTimeOut;

    /**
     * 内存限制 单位 MB
     */
    private Integer maxInMemorySize = 32;

    /**
     * 超时时间 单位 s
     */
    private InquiryDubboForwardProperties dubbo;

    @Data
    public static class InquiryDubboForwardProperties extends InquiryForwardProperties {

    }

}
