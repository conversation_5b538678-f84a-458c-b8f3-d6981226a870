package com.xyy.saas.inquiry.localmock;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.inquiry.localmock.mq.LocalMessage;
import com.xyy.saas.inquiry.localmock.mq.LocalMessageRepository;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStore;
import com.xyy.saas.inquiry.localmock.redis.LocalKvStoreRepository;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Configuration
@Profile("local")
public class LocalMockConfiguration {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(LocalKvStoreRepository repository) {
        RedisTemplate<String, Object> mockTemplate = Mockito.mock(RedisTemplate.class);
        ValueOperations<String, Object> mockOps = Mockito.mock(ValueOperations.class);

        Mockito.when(mockTemplate.opsForValue()).thenReturn(mockOps);

        // Mock set(key, value, timeout, unit)
        Mockito.doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = invocation.getArgument(1);
            long timeout = invocation.getArgument(2);
            TimeUnit unit = invocation.getArgument(3);

            LocalKvStore entry = repository.findByKeyName(key).orElse(new LocalKvStore());
            entry.setKeyName(key);
            entry.setKeyValue(objectMapper.writeValueAsString(value));
            entry.setExpireAt(Instant.now().plus(timeout, unit.toChronoUnit()));
            repository.save(entry);
            return null;
        }).when(mockOps).set(Mockito.anyString(), Mockito.any(), Mockito.anyLong(), Mockito.any(TimeUnit.class));

        // Mock get(key)
        Mockito.when(mockOps.get(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            return repository.findByKeyName(key)
                .filter(e -> e.getExpireAt() == null || e.getExpireAt().isAfter(Instant.now()))
                .map(LocalKvStore::getKeyValue)
                .map(v -> {
                    try {
                        return objectMapper.readValue(v, Object.class);
                    } catch (Exception e) {
                        return v; // return raw string if deserialization fails
                    }
                })
                .orElse(null);
        });

        // Mock delete(key)
        Mockito.when(mockTemplate.delete(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            repository.deleteByKeyName(key);
            return true;
        });

        System.out.println("[LOCAL_MOCK] Mock RedisTemplate has been activated, using database backend.");
        return mockTemplate;
    }

    @Bean
    @Primary
    public RocketMQTemplate rocketMQTemplate(LocalMessageRepository messageRepo) {
        RocketMQTemplate mockTemplate = Mockito.mock(RocketMQTemplate.class);

        Mockito.doAnswer(invocation -> {
            String topic = invocation.getArgument(0);
            Object payload = invocation.getArgument(1);

            LocalMessage msg = new LocalMessage();
            msg.setTopic(topic);
            msg.setMessageBody(objectMapper.writeValueAsString(payload));
            msg.setStatus("UNPROCESSED");
            messageRepo.save(msg);

            System.out.println("[LOCAL_MOCK] Message saved to DB queue for topic '" + topic + "'");
            return null;
        }).when(mockTemplate).syncSend(Mockito.anyString(), Mockito.any());

        System.out.println("[LOCAL_MOCK] Mock RocketMQTemplate has been activated, using database backend.");
        return mockTemplate;
    }
}
