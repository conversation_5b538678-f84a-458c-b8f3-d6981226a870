package com.xyy.saas.inquiry.localmock.mq;

import jakarta.persistence.*;
import lombok.Data;
import java.time.Instant;

@Entity
@Table(name = "local_mock_message_queue")
@Data
public class LocalMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String topic;

    private String tag;

    @Lob
    @Column(nullable = false)
    private String messageBody;

    @Column(nullable = false)
    private String status = "UNPROCESSED";

    private String consumerGroup;

    private Instant consumedAt;
}
