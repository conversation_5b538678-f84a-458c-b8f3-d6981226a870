package com.xyy.saas.inquiry.localmock.mq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface LocalMessageMapper extends BaseMapper<LocalMessagePO> {

    // Using a raw SQL with 'FOR UPDATE' to achieve pessimistic locking, as this is database-specific
    // This syntax works for MySQL/MariaDB.
    @Select("SELECT * FROM local_mock_message_queue WHERE status = #{status} ORDER BY id ASC LIMIT 10 FOR UPDATE")
    List<LocalMessagePO> findTop10ByStatusForUpdate(@Param("status") String status);
}
