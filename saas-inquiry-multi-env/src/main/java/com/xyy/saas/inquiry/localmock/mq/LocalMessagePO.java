package com.xyy.saas.inquiry.localmock.mq;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.Instant;

@Data
@TableName("local_mock_message_queue")
public class LocalMessagePO {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String topic;
    private String tag;
    private String messageBody;
    private String status;
    private String consumerGroup;
    private Instant consumedAt;
}
