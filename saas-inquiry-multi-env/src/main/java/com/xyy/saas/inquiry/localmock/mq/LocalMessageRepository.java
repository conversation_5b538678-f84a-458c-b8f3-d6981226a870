package com.xyy.saas.inquiry.localmock.mq;

import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LocalMessageRepository extends JpaRepository<LocalMessage, Long> {

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT m FROM LocalMessage m WHERE m.status = :status ORDER BY m.id ASC")
    List<LocalMessage> findTop10ByStatusForUpdate(@Param("status") String status);

}
