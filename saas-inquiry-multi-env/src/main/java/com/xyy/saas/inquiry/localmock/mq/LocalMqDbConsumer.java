package com.xyy.saas.inquiry.localmock.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Profile("local")
@EnableScheduling
public class LocalMqDbConsumer {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private LocalMessageMapper messageMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final Map<String, RocketMQListener> listenersCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(RocketMQMessageListener.class);
        for (Object bean : beans.values()) {
            if (bean instanceof RocketMQListener) {
                RocketMQMessageListener annotation = bean.getClass().getAnnotation(RocketMQMessageListener.class);
                listenersCache.put(annotation.topic(), (RocketMQListener) bean);
            }
        }
        System.out.println("[LOCAL_MOCK] Found and cached " + listenersCache.size() + " RocketMQ listeners.");
    }

    @Scheduled(fixedDelay = 3000) // Poll every 3 seconds
    @Transactional
    public void pollAndConsume() {
        List<LocalMessagePO> messages = messageMapper.findTop10ByStatusForUpdate("UNPROCESSED");
        if (messages.isEmpty()) {
            return;
        }

        for (LocalMessagePO msg : messages) {
            msg.setStatus("PROCESSING");
            messageMapper.updateById(msg);

            RocketMQListener listener = listenersCache.get(msg.getTopic());
            if (listener != null) {
                try {
                    Object payload = objectMapper.readValue(msg.getMessageBody(), Object.class);
                    listener.onMessage(payload);
                    msg.setStatus("PROCESSED");
                } catch (Exception e) {
                    System.err.println("[LOCAL_MOCK] Error consuming message " + msg.getId() + ": " + e.getMessage());
                    msg.setStatus("FAILED");
                }
            } else {
                System.err.println("[LOCAL_MOCK] No listener found for topic: " + msg.getTopic());
                msg.setStatus("FAILED");
            }
            msg.setConsumedAt(Instant.now());
            messageMapper.updateById(msg);
        }
    }
}