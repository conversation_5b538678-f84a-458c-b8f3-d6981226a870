package com.xyy.saas.inquiry.localmock.redis;

import jakarta.persistence.*;
import lombok.Data;
import java.time.Instant;

@Entity
@Table(name = "local_mock_kv_store")
@Data
public class LocalKvStore {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String keyName;

    @Lob
    private String keyValue;

    private Instant expireAt;

}
