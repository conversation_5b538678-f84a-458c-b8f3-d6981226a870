package com.xyy.saas.inquiry.localmock.redis;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("local_mock_kv_store")
public class LocalKvStorePO {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String key;
    private String value;
    private Long expireTime;
}
