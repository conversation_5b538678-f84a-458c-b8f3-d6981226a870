package com.xyy.saas.inquiry.localmock.redis;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;

@Data
@TableName("local_mock_kv_store")
public class LocalKvStorePO {

    @TableId(type = IdType.INPUT)
    private String id;
    @TableField("`key`")
    private String key;
    @TableField("`value`")
    private String value;

    @TableField(exist = false)
    private Long expireTime;

    private LocalDateTime expireAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
        this.expireAt = LocalDateTime.ofInstant(Instant.ofEpochMilli(expireTime), ZoneId.systemDefault());
    }

    public void setExpireAt(LocalDateTime expireAt) {
        this.expireAt = expireAt;
        this.expireTime = expireAt.toInstant(ZoneOffset.UTC).toEpochMilli();
    }

}
