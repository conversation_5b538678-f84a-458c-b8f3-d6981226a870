# Dubbo Bootstrap 配置示例
dubbo:
  bootstrap:
    # 启用 Dubbo Bootstrap 自动配置
    enabled: true
    # 基础包路径，只有在这些包路径下的类才会被暴露为 Dubbo 服务
    # 支持 Ant 风格的路径匹配模式：
    # - ** 匹配任意层级的包或类
    # - * 匹配单层包或类名
    # - ? 匹配单个字符
    base-package:
      # 精确匹配指定包
      - com.xyy.saas.inquiry.hospital.service
      # 匹配所有子包（推荐使用）
      - com.xyy.saas.inquiry.patient.**
      # 匹配单层子包下的service包
      - com.xyy.saas.inquiry.*.service
      # 匹配指定包下的所有类
      - com.xyy.saas.inquiry.pharmacist.service.*
    # 排除包路径，在这些包路径下的类不会被暴露为 Dubbo 服务
    # 同样支持 Ant 风格的路径匹配模式
    exclude-package:
      # 排除内部实现包
      - com.xyy.saas.inquiry.**.internal.**
      # 排除测试相关包
      - com.xyy.saas.inquiry.**.test.**
      # 排除特定的类
      - com.xyy.saas.inquiry.hospital.service.PrivateApiImpl
