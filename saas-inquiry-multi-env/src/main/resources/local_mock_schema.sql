-- SQL for local mock environment, using H2 or MySQL syntax

-- Table for simulating Redis Key-Value store
CREATE TABLE IF NOT EXISTS local_mock_kv_store (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(255) NOT NULL,
    key_value LONGTEXT,
    expire_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE (key_name)
);

-- Table for simulating RocketMQ persistent message queue
CREATE TABLE IF NOT EXISTS local_mock_message_queue (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    topic VARCHAR(255) NOT NULL,
    tag VARCHAR(255),
    message_body LONGTEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'UNPROCESSED', -- UNPROCESSED, PROCESSING, PROCESSED, FAILED
    consumer_group VARCHAR(255),
    consumed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_topic_status (topic, status)
);
