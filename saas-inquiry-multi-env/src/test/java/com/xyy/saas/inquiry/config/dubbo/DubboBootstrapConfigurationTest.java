package com.xyy.saas.inquiry.config.dubbo;

import org.junit.jupiter.api.Test;
import org.springframework.util.AntPathMatcher;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DubboBootstrapConfiguration 测试类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
class DubboBootstrapConfigurationTest {

    @Test
    void testAntPathMatcher() {
        AntPathMatcher matcher = new AntPathMatcher(".");
        
        // 测试 ** 通配符
        assertTrue(matcher.match("com.xyy.saas.inquiry.**", "com.xyy.saas.inquiry.hospital.service.HospitalApiImpl"));
        assertTrue(matcher.match("com.xyy.saas.inquiry.**", "com.xyy.saas.inquiry.patient.service.PatientApiImpl"));
        assertFalse(matcher.match("com.xyy.saas.inquiry.**", "com.other.package.SomeClass"));
        
        // 测试 * 通配符
        assertTrue(matcher.match("com.xyy.saas.inquiry.*.service.*", "com.xyy.saas.inquiry.hospital.service.HospitalApiImpl"));
        assertTrue(matcher.match("com.xyy.saas.inquiry.*.service.*", "com.xyy.saas.inquiry.patient.service.PatientApiImpl"));
        assertFalse(matcher.match("com.xyy.saas.inquiry.*.service.*", "com.xyy.saas.inquiry.hospital.controller.HospitalController"));
        
        // 测试精确匹配
        assertTrue(matcher.match("com.xyy.saas.inquiry.hospital.service.HospitalApiImpl", "com.xyy.saas.inquiry.hospital.service.HospitalApiImpl"));
        assertFalse(matcher.match("com.xyy.saas.inquiry.hospital.service.HospitalApiImpl", "com.xyy.saas.inquiry.patient.service.PatientApiImpl"));
        
        // 测试排除模式
        assertTrue(matcher.match("com.xyy.saas.inquiry.**.internal.**", "com.xyy.saas.inquiry.hospital.internal.service.InternalApiImpl"));
        assertTrue(matcher.match("com.xyy.saas.inquiry.**.test.**", "com.xyy.saas.inquiry.patient.test.service.TestApiImpl"));
    }

    @Test
    void testPackageMatching() {
        DubboBootstrapProperties properties = new DubboBootstrapProperties();
        
        // 测试基础包路径匹配
        properties.setBasePackage(Arrays.asList(
            "com.xyy.saas.inquiry.hospital.**",
            "com.xyy.saas.inquiry.patient.service.*"
        ));
        
        // 应该匹配的类
        String matchingClass1 = "com.xyy.saas.inquiry.hospital.service.HospitalApiImpl";
        String matchingClass2 = "com.xyy.saas.inquiry.patient.service.PatientApiImpl";
        
        // 不应该匹配的类
        String nonMatchingClass1 = "com.xyy.saas.inquiry.pharmacist.service.PharmacistApiImpl";
        String nonMatchingClass2 = "com.other.package.SomeApiImpl";
        
        // 验证匹配结果
        assertTrue(isPackageMatched(matchingClass1, properties));
        assertTrue(isPackageMatched(matchingClass2, properties));
        assertFalse(isPackageMatched(nonMatchingClass1, properties));
        assertFalse(isPackageMatched(nonMatchingClass2, properties));
    }

    @Test
    void testExcludePackageMatching() {
        DubboBootstrapProperties properties = new DubboBootstrapProperties();
        
        // 设置基础包路径
        properties.setBasePackage(Arrays.asList("com.xyy.saas.inquiry.**"));
        
        // 设置排除包路径
        properties.setExcludePackage(Arrays.asList(
            "com.xyy.saas.inquiry.**.internal.**",
            "com.xyy.saas.inquiry.**.test.**"
        ));
        
        // 应该匹配的类（在基础包中且不在排除包中）
        String matchingClass = "com.xyy.saas.inquiry.hospital.service.HospitalApiImpl";
        
        // 不应该匹配的类（在排除包中）
        String excludedClass1 = "com.xyy.saas.inquiry.hospital.internal.service.InternalApiImpl";
        String excludedClass2 = "com.xyy.saas.inquiry.patient.test.service.TestApiImpl";
        
        // 验证匹配结果
        assertTrue(isPackageMatched(matchingClass, properties));
        assertFalse(isPackageMatched(excludedClass1, properties));
        assertFalse(isPackageMatched(excludedClass2, properties));
    }

    /**
     * 模拟 DubboBootstrapConfiguration.ServiceClassChecker.isPackageMatched 方法
     */
    private boolean isPackageMatched(String className, DubboBootstrapProperties properties) {
        AntPathMatcher pathMatcher = new AntPathMatcher(".");
        
        // 如果没有配置 basePackage，则不进行包路径过滤
        if (properties.getBasePackage() == null || properties.getBasePackage().isEmpty()) {
            return true;
        }

        // 检查是否在基础包路径中（使用 Ant 风格匹配）
        boolean inBasePackage = properties.getBasePackage().stream()
            .anyMatch(basePackage -> matchesPattern(className, basePackage, pathMatcher));

        if (!inBasePackage) {
            return false;
        }

        // 检查是否在排除包路径中（使用 Ant 风格匹配）
        if (properties.getExcludePackage() != null && !properties.getExcludePackage().isEmpty()) {
            boolean inExcludePackage = properties.getExcludePackage().stream()
                .anyMatch(excludePackage -> matchesPattern(className, excludePackage, pathMatcher));

            if (inExcludePackage) {
                return false;
            }
        }

        return true;
    }

    private boolean matchesPattern(String className, String pattern, AntPathMatcher pathMatcher) {
        // 如果模式不包含通配符，则进行精确匹配或前缀匹配
        if (!pattern.contains("*") && !pattern.contains("?")) {
            return className.equals(pattern) || className.startsWith(pattern + ".");
        }
        
        // 使用 AntPathMatcher 进行模式匹配
        return pathMatcher.match(pattern, className);
    }
}
