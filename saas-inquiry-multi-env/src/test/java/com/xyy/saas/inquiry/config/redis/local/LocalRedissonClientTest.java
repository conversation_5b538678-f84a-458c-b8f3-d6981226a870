package com.xyy.saas.inquiry.config.redis.local;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LocalRedissonClient 测试类
 * 验证本地RedissonClient实现的功能
 */
@SpringBootTest
@ActiveProfiles("local")
public class LocalRedissonClientTest {
    
    private LocalRedissonClient redissonClient;
    
    @BeforeEach
    void setUp() {
        redissonClient = new LocalRedissonClient();
    }
    
    @Test
    void testRBucket() {
        // 测试RBucket基本功能
        RBucket<String> bucket = redissonClient.getBucket("test:bucket");
        
        // 测试设置和获取
        bucket.set("hello world");
        assertEquals("hello world", bucket.get());
        
        // 测试过期时间
        bucket.set("expire test", 1, TimeUnit.SECONDS);
        assertEquals("expire test", bucket.get());
        
        // 测试删除
        assertTrue(bucket.delete());
        assertNull(bucket.get());
    }
    
    @Test
    void testRLock() throws InterruptedException {
        // 测试分布式锁
        RLock lock = redissonClient.getLock("test:lock");
        
        // 测试加锁和解锁
        lock.lock();
        assertTrue(lock.isLocked());
        assertTrue(lock.isHeldByCurrentThread());
        lock.unlock();
        assertFalse(lock.isLocked());
        
        // 测试tryLock
        assertTrue(lock.tryLock());
        assertTrue(lock.isLocked());
        lock.unlock();
        
        // 测试带超时的tryLock
        assertTrue(lock.tryLock(1, TimeUnit.SECONDS));
        assertTrue(lock.isLocked());
        lock.unlock();
    }
    
    @Test
    void testRAtomicLong() {
        // 测试原子长整型
        RAtomicLong atomicLong = redissonClient.getAtomicLong("test:atomic");
        
        // 测试基本操作
        atomicLong.set(10);
        assertEquals(10, atomicLong.get());
        
        assertEquals(11, atomicLong.incrementAndGet());
        assertEquals(10, atomicLong.decrementAndGet());
        
        assertEquals(15, atomicLong.addAndGet(5));
        assertEquals(15, atomicLong.getAndSet(20));
        assertEquals(20, atomicLong.get());
    }
    
    @Test
    void testRList() {
        // 测试分布式列表
        RList<String> list = redissonClient.getList("test:list");
        
        // 测试添加元素
        assertTrue(list.add("item1"));
        assertTrue(list.add("item2"));
        assertTrue(list.add("item3"));
        
        assertEquals(3, list.size());
        assertEquals("item1", list.get(0));
        assertEquals("item2", list.get(1));
        assertEquals("item3", list.get(2));
        
        // 测试删除元素
        assertEquals("item2", list.remove(1));
        assertEquals(2, list.size());
        
        // 测试清空
        list.clear();
        assertTrue(list.isEmpty());
    }
    
    @Test
    void testRMap() {
        // 测试分布式Map
        RMap<String, String> map = redissonClient.getMap("test:map");
        
        // 测试基本操作
        assertNull(map.put("key1", "value1"));
        assertEquals("value1", map.put("key1", "value1_updated"));
        assertEquals("value1_updated", map.get("key1"));
        
        map.put("key2", "value2");
        assertEquals(2, map.size());
        
        assertTrue(map.containsKey("key1"));
        assertTrue(map.containsValue("value2"));
        
        // 测试删除
        assertEquals("value1_updated", map.remove("key1"));
        assertEquals(1, map.size());
        
        // 测试清空
        map.clear();
        assertTrue(map.isEmpty());
    }
    
    @Test
    void testRSet() {
        // 测试分布式Set
        RSet<String> set = redissonClient.getSet("test:set");
        
        // 测试添加元素
        assertTrue(set.add("item1"));
        assertTrue(set.add("item2"));
        assertFalse(set.add("item1")); // 重复添加应该返回false
        
        assertEquals(2, set.size());
        assertTrue(set.contains("item1"));
        assertTrue(set.contains("item2"));
        
        // 测试删除
        assertTrue(set.remove("item1"));
        assertEquals(1, set.size());
        
        // 测试清空
        set.clear();
        assertTrue(set.isEmpty());
    }
    
    @Test
    void testRQueue() {
        // 测试分布式队列
        RQueue<String> queue = redissonClient.getQueue("test:queue");
        
        // 测试入队
        assertTrue(queue.offer("first"));
        assertTrue(queue.offer("second"));
        assertTrue(queue.offer("third"));
        
        assertEquals(3, queue.size());
        
        // 测试出队
        assertEquals("first", queue.poll());
        assertEquals("second", queue.poll());
        assertEquals(1, queue.size());
        
        // 测试peek
        assertEquals("third", queue.peek());
        assertEquals(1, queue.size()); // peek不会移除元素
        
        // 测试清空
        queue.clear();
        assertTrue(queue.isEmpty());
    }
    
    @Test
    void testRKeys() {
        // 测试Keys操作
        RKeys keys = redissonClient.getKeys();
        
        // 添加一些测试数据
        redissonClient.getBucket("test:key1").set("value1");
        redissonClient.getBucket("test:key2").set("value2");
        redissonClient.getBucket("other:key").set("value3");
        
        // 测试获取所有keys
        assertTrue(keys.count() >= 3);
        
        // 测试模式匹配
        assertTrue(keys.findKeysByPattern("test:*").size() >= 2);
        assertTrue(keys.findKeysByPattern("other:*").size() >= 1);
        
        // 测试删除
        assertEquals(2, keys.delete("test:key1", "test:key2"));
        assertEquals(1, keys.delete("other:key"));
    }
    
    @Test
    void testExpiration() throws InterruptedException {
        // 测试过期功能
        RBucket<String> bucket = redissonClient.getBucket("test:expire");
        bucket.set("will expire", 100, TimeUnit.MILLISECONDS);
        
        assertTrue(bucket.isExists());
        assertEquals("will expire", bucket.get());
        
        // 等待过期
        Thread.sleep(150);
        
        // 验证已过期
        assertFalse(bucket.isExists());
        assertNull(bucket.get());
    }
    
    @Test
    void testClientOperations() {
        // 测试客户端基本操作
        assertNotNull(redissonClient.getId());
        assertFalse(redissonClient.isShutdown());
        assertFalse(redissonClient.isShuttingDown());
        
        // 测试配置
        assertNotNull(redissonClient.getConfig());
        
        // 测试关闭
        redissonClient.shutdown();
        // 注意：在实际测试中，shutdown后客户端状态可能不会立即改变
    }
}
