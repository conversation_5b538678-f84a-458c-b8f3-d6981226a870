<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-patient</artifactId>
    <version>${revision}</version>
  </parent>
  <artifactId>saas-inquiry-patient-api</artifactId>
  <name>${project.artifactId}</name>

  <properties>
    <java.version>21</java.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <artifactId>lombok</artifactId>
      <groupId>org.projectlombok</groupId>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pojo</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-common</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
