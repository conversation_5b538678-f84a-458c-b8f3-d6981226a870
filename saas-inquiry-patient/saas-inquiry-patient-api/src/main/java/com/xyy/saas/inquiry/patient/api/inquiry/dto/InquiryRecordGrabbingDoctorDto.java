package com.xyy.saas.inquiry.patient.api.inquiry.dto;

import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: xucao
 * @Date: 2024/12/09 19:45
 * @Description: 问诊单Dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class InquiryRecordGrabbingDoctorDto implements Serializable {

    /**
     * 主键ID
     */
    @NotNull(message = "问诊id不可为空")
    private Long id;

    /**
     * 医生Pref
     */
    private String doctorPref;

    /**
     * 自动抢派单状态 0- 常规派单   1-自动抢单
     */
    private Integer autoGrabStatus;


    /**
     * 医生姓名
     */
    private String doctorName;


    /**
     * 科室编码
     */
    private String deptPref;

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消 {@link InquiryStatusEnum }
     */
    private Integer inquiryStatus;

    /**
     * 原始接诊状态
     */
    private Integer originalInquiryStatus;

    /**
     * 医生接诊时间
     */
    private LocalDateTime startTime;
    /**
     * 问诊结束时间
     */
    private LocalDateTime endTime;
}
