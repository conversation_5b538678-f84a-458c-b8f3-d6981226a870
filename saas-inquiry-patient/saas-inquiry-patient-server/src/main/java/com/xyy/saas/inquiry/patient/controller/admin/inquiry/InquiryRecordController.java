package com.xyy.saas.inquiry.patient.controller.admin.inquiry;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordSaveReqVO;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 问诊记录")
@RestController
@RequestMapping("/patient/inquiry-record")
@Validated
public class InquiryRecordController {

    @Resource
    private InquiryService inquiryRecordService;


    @PutMapping("/update")
    @Operation(summary = "更新问诊记录")
    @PreAuthorize("@ss.hasPermission('patient:inquiry-record:update')")
    public CommonResult<Boolean> updateInquiryRecord(@Valid @RequestBody InquiryRecordSaveReqVO updateReqVO) {
        inquiryRecordService.updateInquiryRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除问诊记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('patient:inquiry-record:delete')")
    public CommonResult<Boolean> deleteInquiryRecord(@RequestParam("id") Long id) {
        inquiryRecordService.deleteInquiryRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得问诊记录")
    @Parameter(name = "pref", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('patient:inquiry-record:query')")
    public CommonResult<InquiryRecordDetailRespVO> getInquiryRecord(@RequestParam("id") Long id) {
        return success(inquiryRecordService.getInquiryRecord(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得问诊记录分页")
    @PreAuthorize("@ss.hasPermission('patient:inquiry-record:query')")
    public CommonResult<PageResult<InquiryRecordRespVO>> getInquiryRecordPage(@Valid InquiryRecordPageReqVO pageReqVO) {
        // 设置web端查询标识
        pageReqVO.isWebQuery();
        return success(inquiryRecordService.getInquiryRecordPage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出问诊记录 Excel")
    @PreAuthorize("@ss.hasPermission('patient:inquiry-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryRecordExcel(@Valid InquiryRecordPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 设置web端查询标识
        pageReqVO.isWebQuery();
        List<InquiryRecordRespVO> list = inquiryRecordService.getInquiryRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "问诊记录.xls", "数据", InquiryRecordRespVO.class,
            BeanUtils.toBean(list, InquiryRecordRespVO.class));
    }

}