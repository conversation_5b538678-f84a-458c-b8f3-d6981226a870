package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "查询三方预问诊记录")
public class ThirdPartyPreInquirySearchReqVO extends PageParam {

    @Schema(description = "租户id", example = "1")
    private Long tenantId;

    @Schema(description = "用户姓名", example = "张三")
    private String userName;

    @Schema(description = "用户手机号", example = "13800000000")
    private String mobile;

    @Schema(description = "三方预问诊id", example = "1")
    private Long thirdPartyPreInquiryId;

    @Schema(description = "预问诊编码", example = "pref1")
    private String pref;

    @Schema(description = "三方系统渠道来源", example = "1")
    private Integer transmissionOrganId;

    @Schema(description = "预问诊编码集合", example = "pref1")
    private List<String> prefs;

    @Schema(description = "审核状态 0-待审核   1-审核通过   2-审核驳回", example = "0")
    private Integer auditStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "问诊方式 1-图文 2-视频 3-电话", example = "1")
    private Integer inquiryWayType;

}
