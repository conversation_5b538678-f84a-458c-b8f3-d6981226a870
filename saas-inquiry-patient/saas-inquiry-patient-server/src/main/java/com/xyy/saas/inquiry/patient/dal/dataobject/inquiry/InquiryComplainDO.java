package com.xyy.saas.inquiry.patient.dal.dataobject.inquiry;

import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 问诊投诉记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_complain")
@KeySequence("saas_inquiry_complain_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryComplainDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 问诊单pref
     */
    private String inquiryPref;
    /**
     * 投诉人id
     */
    private Long complainUser;
    /**
     * 被投诉人id
     */
    private Long beComplainUser;
    /**
     * 举报类型
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> complainItem;
    /**
     * 举报描述
     */
    private String complainContent;
    /**
     * 证明材料图片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> complainImage;

}