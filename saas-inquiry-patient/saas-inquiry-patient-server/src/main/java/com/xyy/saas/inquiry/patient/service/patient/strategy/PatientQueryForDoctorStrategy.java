package com.xyy.saas.inquiry.patient.service.patient.strategy;

import static cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils.getLoginUserId;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.util.BusinessUtil;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import org.springframework.stereotype.Component;

/**
 * 类名：PatientQueryForDoctorStrategy 功能描述：医生查询患者信息 作者：xucao 创建时间：2025/3/4 11:00 版本：v1.0
 */
@Component
public class PatientQueryForDoctorStrategy extends PatientQueryStrategy {

    @Resource
    private InquiryDoctorApi inquiryDoctorApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;



    @Override
    public PageResult<InquiryPatientInfoRespVO> query(InquiryPatientInfoQueryReqVO pageReqVO) {
        // 查询当前医生信息
        InquiryDoctorDto doctorDto = inquiryDoctorApi.getInquiryDoctorByUserId(getLoginUserId());
        if (doctorDto == null) {
            return PageResult.empty();
        }
        // 查询医生接诊患者记录（默认查近180天的患者）
        LocalDateTime[]  createTime = BusinessUtil.getBeforeDays(MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.TENANT_QUERY_INQUIRY_RANGE_DAY), 180));
        pageReqVO.setStartTime(createTime[0]);
        pageReqVO.setEndTime(createTime[1]);
        pageReqVO.setDoctorPref(doctorDto.getPref());
        InquiryPatientInfoPageReqVO req = PatientInfoConvert.INSTANCE.convertQueryVO(pageReqVO);
        IPage<PatientSimpleDTO> pageResult = inquiryRecordMapper.selectDoctorReceptionPatientPage(MyBatisUtils.buildPage(pageReqVO), req);
        return super.handleConvert(pageResult);
    }

    @Override
    public PatientQuerySenceEnum getQueryScene() {
        return PatientQuerySenceEnum.DOCTOR_QUERY;
    }
}