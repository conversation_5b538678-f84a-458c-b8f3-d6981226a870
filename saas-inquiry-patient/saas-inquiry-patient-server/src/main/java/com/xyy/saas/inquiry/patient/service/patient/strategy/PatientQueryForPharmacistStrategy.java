package com.xyy.saas.inquiry.patient.service.patient.strategy;

import static cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils.getLoginUserId;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.util.BusinessUtil;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @ClassName：PatientQueryForPharmacistStrategy
 * @Author: xucao
 * @Date: 2025/3/4 13:49
 * @Description: 药师查询患者场景
 */
@Component
public class PatientQueryForPharmacistStrategy extends PatientQueryStrategy {


    @Resource
    private InquiryPharmacistApi inquiryPharmacistApi;

    @Resource
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @Resource
    private ConfigApi configApi;

    @Override
    public PageResult<InquiryPatientInfoRespVO> query(InquiryPatientInfoQueryReqVO pageReqVO) {
        // 查询当前药师信息
        InquiryPharmacistDto pharmacistDto = null;
        if (StringUtils.isBlank(pageReqVO.getPharmacistPref())) {
            pharmacistDto = inquiryPharmacistApi.getRequiredApprovedPharmacistByUserId(getLoginUserId());
        } else {
            pharmacistDto = inquiryPharmacistApi.getPharmacistByPref(pageReqVO.getPharmacistPref());
        }
        if (pharmacistDto == null) {
            return PageResult.empty();
        }
        LocalDateTime[] createTime = BusinessUtil.getBeforeDays(MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.TENANT_QUERY_INQUIRY_RANGE_DAY), 180));
        // 查询药师审方患者
        IPage<PatientSimpleDTO> pageResult =
            inquiryPrescriptionApi.getPrescriptionPatientList(
                InquiryPrescriptionQueryDTO.builder().pharmacistPref(pharmacistDto.getPref()).tenantId(pageReqVO.getTenantId()).patientName(pageReqVO.getName()).pageNum(pageReqVO.getPageNo()).pageSize(pageReqVO.getPageSize())
                    .auditPrescriptionTime(createTime).build());
        return super.handleConvert(pageResult);
    }

    @Override
    public PatientQuerySenceEnum getQueryScene() {
        return PatientQuerySenceEnum.PHARMACIST_QUERY;
    }
}