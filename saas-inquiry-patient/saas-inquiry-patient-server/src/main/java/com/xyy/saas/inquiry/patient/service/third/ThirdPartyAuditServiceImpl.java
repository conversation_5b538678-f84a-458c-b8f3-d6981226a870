package com.xyy.saas.inquiry.patient.service.third;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PreInquiryCancelResultTypeEnum;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryCancelRespVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryMapper;
import com.xyy.saas.inquiry.patient.service.third.handle.PreInquiryAuditHandle;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @DateTime: 2025/4/29 11:29
 * @Description: 预问诊审核服务实现类
 **/
@Service
public class ThirdPartyAuditServiceImpl implements ThirdPartyAuditService{


    @Resource
    private PreInquiryAuditHandle preInquiryAuditHandle;


    /**
     * 三方预问诊单审核
     *
     * @param thirdPartyPreInquiryAuditReqVO
     * @return
     */
    @Override
    public CommonResult<ThirdPartyPreInquiryAuditRespVO> preInquiryAudit(ThirdPartyPreInquiryAuditReqVO thirdPartyPreInquiryAuditReqVO) {
        return CommonResult.success(preInquiryAuditHandle.audit(thirdPartyPreInquiryAuditReqVO));
    }

    /**
     * @param pref
     * @return
     */
    @Override
    public ThirdPartyPreInquiryCancelRespVO cancelPreInquiry(String pref) {
        //默认取消成功
        ThirdPartyPreInquiryCancelRespVO defaultResult = new ThirdPartyPreInquiryCancelRespVO(PreInquiryCancelResultTypeEnum.CANCEL_SUCCESS);
        // 查询预问诊记录
        ThirdPartyPreInquiryDO preInquiryDO = preInquiryAuditHandle.selectByPref(pref);
        // 审核驳回返回取消成功
        if(preInquiryDO == null){
            return defaultResult;
        }
        // 待审核情况下删除预问诊单并返回取消成功
        if(ObjectUtil.equals(preInquiryDO.getAuditStatus(), AuditStatusEnum.PENDING.getCode())){
            preInquiryAuditHandle.delPreInquiry(preInquiryDO);
            return defaultResult;
        }
        // 问诊单号为空
        if(StringUtils.isBlank(preInquiryDO.getInquiryPref())){
            return defaultResult;
        }
        // 审核通过处理
        return preInquiryAuditHandle.preInquiryCancelButApproved(preInquiryDO);
    }
}
