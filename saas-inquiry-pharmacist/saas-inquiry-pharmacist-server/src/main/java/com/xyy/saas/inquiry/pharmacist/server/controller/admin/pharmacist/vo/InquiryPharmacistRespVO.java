package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.pojo.parmacist.PharmacistExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 药师信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryPharmacistRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "23483")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "药师编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("药师编码")
    private String pref;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19857")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "性别 1男 2女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("性别 1男 2女")
    private Integer sex;

    @Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("身份证号码")
    private String idCard;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String mobile;

    @Schema(description = "审核状态 0、待审核  1、审核通过  2、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("审核状态 0、待审核  1、审核通过  2、审核驳回")
    private Integer auditStatus;

    @Schema(description = "在线状态 0离线 1在线", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("在线状态")
    private Integer onlineStatus;

    @Schema(description = "证件照地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证件照地址")
    private String photo;

    @Schema(description = "个人简介", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人简介")
    private String biography;

    @Schema(description = "药师执业资格,中药或西药", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("药师执业资格,中药或西药")
    private Integer qualification;

    @Schema(description = "药师类型 平台药师 / 门店药师 / 医院药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("药师类型 平台药师 / 门店药师 / 医院药师")
    private Integer pharmacistType;

    @Schema(description = "药师性质", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("药师性质")
    private Integer pharmacistNature;

    @Schema(description = "毕业学校", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("毕业学校")
    private String school;

    /**
     * 平台药师才有
     */
    @Schema(description = "执业省份")
    @ExcelProperty("执业省份")
    private String provinceCode;

    /**
     * 药师工作类型 1全职/ 2兼职
     * <p>
     * {@link DoctorJobTypeEnum}
     */
    @ExcelProperty("药师工作类型")
    private Integer jobType;

    @Schema(description = "民族", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer nationCode;

    @Schema(description = "学历", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer formalLevel;

    @Schema(description = "降级绘制标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer drawnSign;

    @Schema(description = "通信地址")
    private String address;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "资质信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<InquiryProfessionIdentificationDto> professionIdentifications;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private String envTag;

    /**
     * PC是否需要指纹审核 默认是 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer fingerPrintAudit;
    /**
     * 扩展信息
     */
    private PharmacistExtDto ext;

    // relation ------
    /**
     * {@link UserStatusEnum}
     */
    @Schema(description = "用户 0启用 1禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "门店药师用户关联id")
    private Long tenantUserRelationId;

    // 签章平台状态 ---------

    @Schema(description = "实名认证状态 0: 待认证，1: 认证完成，2: 认证失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("实名认证状态 0: 待认证，1: 认证完成，2: 认证失败")
    private Integer certifyStatus;

    @Schema(description = "签名状态 0: 未签名，1: 已签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("签名状态 0: 未签名，1: 已签名")
    private Integer signatureStatus;

    @Schema(description = "签名图片Url")
    private String signatureUrl;

    @Schema(description = "免签授权状态 0: 未授权，1: 已授权，", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("免签授权状态 0: 未授权，1: 已授权，")
    private Integer authorizeFreeSignStatus;

    @Schema(description = "免签授权截止时间")
    @ExcelProperty("免签授权截止时间")
    private LocalDateTime authorizeFreeSignDdl;

    @Schema(description = "用户账号状态 0-正常 1-禁用 2-注销")
    @ExcelProperty("用户账号状态")
    private Integer userAccountStatus;

    @Schema(description = "是否拥有指纹")
    private boolean hasFingerPrint;
}