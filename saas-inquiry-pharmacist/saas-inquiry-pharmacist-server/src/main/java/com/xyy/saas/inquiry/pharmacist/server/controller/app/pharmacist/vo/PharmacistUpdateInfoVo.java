package com.xyy.saas.inquiry.pharmacist.server.controller.app.pharmacist.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2025/05/14 11:27
 */
@Data
@Accessors(chain = true)
public class PharmacistUpdateInfoVo implements Serializable {

    @Schema(description = "userId")
    private Long userId;


    @Schema(description = "执业省份")
    private String provinceCode;

    /**
     * 是否需要指纹审核 默认是 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否需要指纹审核 0是 1否")
    private Integer fingerPrintAudit;

}
