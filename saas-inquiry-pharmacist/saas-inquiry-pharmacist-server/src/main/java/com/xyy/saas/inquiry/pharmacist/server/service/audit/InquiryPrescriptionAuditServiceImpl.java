package com.xyy.saas.inquiry.pharmacist.server.service.audit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_REMOTE_COST_DATA_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_REMOTE_PRESCRIPTION_DATA_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.PRESCRIPTION_AUDIT_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.baomidou.lock.annotation.Lock4j;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionDto;
import com.xyy.saas.inquiry.pharmacist.server.constant.PrescriptionAuditConstant;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.audit.InquiryPharmacistAuditConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.prescription.InquiryPharmacistPrescriptionConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.audit.InquiryPrescriptionAuditMapper;
import com.xyy.saas.inquiry.pharmacist.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.pharmacist.server.dal.redis.audit.PharmacistAuditRedisService;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionAuditOutTimeMessageDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.PrescriptionAuditStrategy;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 处方审核记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InquiryPrescriptionAuditServiceImpl implements InquiryPrescriptionAuditService {

    @Resource
    protected InquiryPrescriptionApi inquiryPrescriptionApi;

    @Resource
    private InquiryPrescriptionAuditMapper prescriptionAuditMapper;

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @Resource
    private TenantPackageCostApi tenantPackageCostApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    protected PharmacistAuditRedisService pharmacistAuditRedisService;

    @Resource
    protected TenantApi tenantApi;


    private final Map<PharmacistTypeEnum, PrescriptionAuditStrategy> prescriptionAuditStrategyMap = new HashMap<>();

    @Autowired
    public void initHandler(List<PrescriptionAuditStrategy> strategies) {
        strategies.stream().filter(s -> s.getPharmacistType() != null).forEach(strategy -> prescriptionAuditStrategyMap.put(strategy.getPharmacistType(), strategy));
    }


    @Override
    public CommonResult<Long> waitReceiveCount() {
        // 获取当前登录用户药师信息 根据类型走不同的策略
        InquiryPharmacistDO pharmacist = inquiryPharmacistService.getRequiredOnLinePharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        return prescriptionAuditStrategyMap.get(PharmacistTypeEnum.fromCode(pharmacist.getPharmacistType())).waitReceiveCount(pharmacist);
    }

    @Override
    public CommonResult<InquiryPrescriptionReceiveVO> receivePrescription() {
        // 获取当前登录用户药师信息 根据类型走不同的策略
        InquiryPharmacistDO pharmacist = inquiryPharmacistService.getRequiredOnLinePharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        return prescriptionAuditStrategyMap.get(PharmacistTypeEnum.fromCode(pharmacist.getPharmacistType())).receivePrescription(pharmacist);
    }

    @Override
    public CommonResult<Integer> getAuditTimeout() {
        return CommonResult.success(NumberUtils.toInt(configApi.getConfigValueByKey(PrescriptionAuditConstant.PHARMACIST_AUDIT_TIMEOUT), 60));
    }

    /**
     * 锁处方单号 防止多个药师同时审核一张处方
     *
     * @param auditVO 审核参数
     * @return
     */
    @Override
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_AUDIT_LOCK + "'.concat(#auditVO.pref)")
    public CommonResult<Boolean> auditPass(InquiryPrescriptionAuditVO auditVO) {
        // 获取当前登录用户药师信息 根据类型走不同的策略
        InquiryPharmacistDO pharmacist = inquiryPharmacistService.getRequiredOnLinePharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        return prescriptionAuditStrategyMap.get(PharmacistTypeEnum.fromCode(pharmacist.getPharmacistType())).auditPass(pharmacist, auditVO);
    }

    /**
     * 锁处方单号 防止多个药师同时审核一张处方
     *
     * @param auditVO 审核参数
     * @return
     */
    @Override
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_AUDIT_LOCK + "'.concat(#auditVO.pref)")
    public CommonResult<Boolean> auditReject(InquiryPrescriptionAuditVO auditVO) {
        // 获取当前登录用户药师信息 根据类型走不同的策略
        InquiryPharmacistDO pharmacist = inquiryPharmacistService.getRequiredOnLinePharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        return prescriptionAuditStrategyMap.get(PharmacistTypeEnum.fromCode(pharmacist.getPharmacistType())).auditReject(pharmacist, auditVO);
    }

    /**
     * 锁处方单号 防止处方操作时释放
     *
     * @param msg 审核消息
     * @return
     */
    @Override
    @Lock4j(keys = "'" + RedisKeyConstants.PRESCRIPTION_AUDIT_LOCK + "'.concat(#msg.pref)")
    public CommonResult<Boolean> auditOutTime(PrescriptionAuditOutTimeMessageDTO msg) {
        // 获取药师信息 + 获取处方信息
        InquiryPharmacistDO pharmacist = inquiryPharmacistService.getInquiryPharmacistById(msg.getPharmacistId());
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().pref(msg.getPref()).build());
        if (prescription == null || !PrescriptionStatusEnum.isCanAuditStatus(prescription.getStatus())) {
            return CommonResult.success(true);
        }
        // 走不同审方池重放策略
        Optional.ofNullable(PharmacistTypeEnum.convertAuditorType(prescription.getAuditorType()))
            .ifPresent(pt -> prescriptionAuditStrategyMap.get(pt).auditOutTime(pharmacist, InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription).setAuditRecordId(msg.getAuditRecordId())));
        return CommonResult.success(true);
    }


    /**
     * 操作远程处方审方池
     *
     * @param auditDto 远程审方处方
     */
    @Override
    public void remotePrescriptionOperateAuditPool(RemotePrescriptionAuditDto auditDto) {
        log.info("远程审方操作处方审方池,处方单号:{}", auditDto);
        //  校验远程处方
        InquiryPharmacistPrescriptionDTO remotePrescription = validateRemotePrescription(auditDto);

        //  获取门店套餐 选择推送审方池
        TenantPackageCostDto tenantPackageCostDto = getAndValidatePackageCost(auditDto);

        // 连锁审方->推门店(总部)审方池 / 平台审方->推平台审方池
        PharmacistTypeEnum pharmacistTypeEnum = Objects.equals(tenantPackageCostDto.getInquiryAuditType(), InquiryAuditTypeEnum.PLATFORM.getCode())
            ? PharmacistTypeEnum.PLATFORM : PharmacistTypeEnum.DRUGSTORE;

        if (auditDto.isAudit()) {
            prescriptionAuditStrategyMap.get(pharmacistTypeEnum).pushPrescriptionAuditPool(remotePrescription);
        } else {
            prescriptionAuditStrategyMap.get(pharmacistTypeEnum).removePrescriptionAuditPool(remotePrescription);
        }
    }

    @Override
    public void remotePrescriptionBatchRemoveAuditPool(RemotePrescriptionAuditDto auditDto) {
        log.info("远程审方操作批量移除处方审方池,处方数量:{}", CollUtil.size(auditDto.getRemotePrescriptionDtos()));
        if (CollUtil.isEmpty(auditDto.getRemotePrescriptionDtos())) {
            return;
        }

        Map<String, TenantPackageCostDto> costDtoMap = tenantPackageCostApi.getTenantPackageCostByLogBizIds(auditDto.getRemotePrescriptionDtos().stream().map(RemotePrescriptionDto::getInquiryPref).toList(),
            CostRecordTypeEnum.INQUIRY);

        TenantDto tenantDto = tenantApi.getTenant(auditDto.getRemotePrescriptionDtos().getFirst().getTenantId());

        // 移除连锁总部审方池
        auditDto.getRemotePrescriptionDtos().stream()
            .filter(p -> costDtoMap.containsKey(p.getInquiryPref())
                && Objects.equals(costDtoMap.get(p.getInquiryPref()).getInquiryAuditType(), InquiryAuditTypeEnum.CHAIN.getCode()))
            .collect(Collectors.groupingBy(RemotePrescriptionDto::getMedicineType)).forEach((medicineType, ppDtos) -> {
                ppDtos.stream().collect(Collectors.groupingBy(g -> g.getExt().getHeadTenantId())).forEach((headTenantId, ppDto) -> {
                    // 门店审方池
                    String[] list = ppDto.stream().map(RemotePrescriptionDto::getPref).toArray(String[]::new);
                    log.info("远程审方操作批量移除处方审方池,处方Prefs:{}", list);
                    pharmacistAuditRedisService.prescriptionWaitingReviewChainPoolRemove(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(medicineType), headTenantId, list);
                });
            });

        // 移除平台药师审方池
        // auditDto.getRemotePrescriptionDtos().stream()
        //     .filter(p -> costDtoMap.containsKey(p.getInquiryPref())
        //         && Objects.equals(costDtoMap.get(p.getInquiryPref()).getInquiryAuditType(), InquiryAuditTypeEnum.PLATFORM.getCode()))
        //     .collect(Collectors.groupingBy(RemotePrescriptionDto::getMedicineType)).forEach((medicineType, ppDtos) -> {
        //
        //
        //     });

    }

    //  校验远程处方
    private InquiryPharmacistPrescriptionDTO validateRemotePrescription(RemotePrescriptionAuditDto auditDto) {

        InquiryPharmacistPrescriptionDTO pharmacistPrescriptionDTO = auditDto.isAudit()
            ? InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(
            inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().pref(auditDto.getPrescriptionPref()).build()))
            : InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(auditDto.getRemotePrescriptionDto());

        if (pharmacistPrescriptionDTO == null
            || pharmacistPrescriptionDTO.getExt() == null
            || pharmacistPrescriptionDTO.getExt().getHeadTenantId() == null
            || !PrescriptionStatusEnum.isCanAuditStatus(pharmacistPrescriptionDTO.getStatus())
            || !Objects.equals(pharmacistPrescriptionDTO.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            log.warn("远程审方处方不存在或状态异常,处方单号:{}", auditDto.getPrescriptionPref());
            throw exception(INQUIRY_PHARMACIST_REMOTE_PRESCRIPTION_DATA_ERROR);
        }

        return pharmacistPrescriptionDTO;
    }

    //  获取门店套餐 选择推送审方池
    private TenantPackageCostDto getAndValidatePackageCost(RemotePrescriptionAuditDto auditDto) {
        TenantPackageCostDto packageCostDto = auditDto.isAudit()
            ? tenantPackageCostApi.getTenantPackageCostById(auditDto.getCostId())
            : tenantPackageCostApi.getTenantPackageCostByLogBizId(auditDto.getRemotePrescriptionDto().getInquiryPref(), CostRecordTypeEnum.INQUIRY);

        if (packageCostDto == null || !Objects.equals(packageCostDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            log.warn("远程审方处方额度不存在或类型异常,处方单号:{}", auditDto.getPrescriptionPref());
            throw exception(INQUIRY_PHARMACIST_REMOTE_COST_DATA_ERROR);
        }
        return packageCostDto;
    }


    @Override
    public Long createPrescriptionAudit(InquiryPrescriptionAuditSaveReqVO createReqVO) {
        // 插入
        InquiryPrescriptionAuditDO prescriptionAudit = InquiryPharmacistAuditConvert.INSTANCE.convertDo(createReqVO);
        prescriptionAudit.setTenantId(createReqVO.getTenantId() == null ? TenantContextHolder.getTenantId() : createReqVO.getTenantId());
        prescriptionAuditMapper.insert(prescriptionAudit);
        // 返回
        return prescriptionAudit.getId();
    }

    @Override
    public void updatePrescriptionAudit(InquiryPrescriptionAuditSaveReqVO updateReqVO) {
        // 校验存在
        validatePrescriptionAuditExists(updateReqVO.getId());
        // 更新
        InquiryPrescriptionAuditDO updateObj = InquiryPharmacistAuditConvert.INSTANCE.convertDo(updateReqVO);
        prescriptionAuditMapper.updateById(updateObj);
    }

    @Override
    public void deletePrescriptionAudit(Long id) {
        // 校验存在
        validatePrescriptionAuditExists(id);
        // 删除
        prescriptionAuditMapper.deleteById(id);
    }

    private void validatePrescriptionAuditExists(Long id) {
        if (prescriptionAuditMapper.selectById(id) == null) {
            throw exception(PRESCRIPTION_AUDIT_NOT_EXISTS);
        }
    }

    @Override
    public InquiryPrescriptionAuditDO getPrescriptionAudit(Long id) {
        return prescriptionAuditMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryPrescriptionAuditDO> getPrescriptionAuditPage(InquiryPrescriptionAuditPageReqVO pageReqVO) {
        return prescriptionAuditMapper.selectPage(pageReqVO);
    }

}