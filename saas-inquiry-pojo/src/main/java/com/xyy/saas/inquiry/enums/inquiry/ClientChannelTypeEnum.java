package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

@Getter
public enum ClientChannelTypeEnum {

    /**
     * APP 客户端
     */
    APP(0, "app","APP"),

    /**
     * PC 客户端
     */
    PC(1,"pc", "PC端"),
    /**
     * 小程序客户端
     */
    MINI_PROGRAM(2,"wechat", "小程序"),

    ;

    private final int code; // 整数字段
    private final String suffix;
    private final String description; // 字符串字段

    // 构造函数，用于初始化枚举项的字段
    ClientChannelTypeEnum(int code,String suffix, String description) {
        this.code = code;
        this.suffix = suffix;
        this.description = description;
    }

    /**
     * 根据整数代码获取对应的枚举类型
     *
     * @param code 整数代码
     * @return 对应的枚举类型，如果不存在则返回 null
     */
    public static ClientChannelTypeEnum fromCode(int code) {
        for (ClientChannelTypeEnum channel : values()) {
            if (channel.getCode() == code) {
                return channel;
            }
        }
        return null;
    }

    /**
     * 根据userId 和 客户端类型获取im账号
     * @param userId 用户ID
     * @param clientChannelTypeEnum 客户端类型
     * @return IM账号
     */
    public String getImAccount(Long userId, ClientChannelTypeEnum clientChannelTypeEnum) {
        return userId + "_"+clientChannelTypeEnum.suffix;
    }
}
