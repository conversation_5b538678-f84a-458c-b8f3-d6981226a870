package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * @Author: xucao
 * @Date: 2025/01/22 12:00
 * @Description: 问诊单抢派单状态枚举 0 常规  1抢单
 */
@Getter
public enum InquiryGrabStatusEnum {
    NORMAL(0,"常规派单"),
    GRAB(1,"自动抢单");

    private final int code;
    private final String desc;

    InquiryGrabStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
