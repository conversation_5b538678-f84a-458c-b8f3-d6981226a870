package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Desc 开方状态
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午2:34
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionStatusEnum implements IntArrayValuable {

    WAITING(0, "待开方"),

    CANCELED(1, "已取消"),

    WAIT_APPROVAL(2, "待审核"),

    APPROVAL_ING(3, "审核中"),

    APPROVAL(4, "已审核完结(通过)-(药师)"),

    APPROVAL_REJECTED(5, "审核驳回"),

    UNKNOWN(-1, "未知状态"),

    ;

    private final Integer statusCode;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionStatusEnum::getStatusCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static PrescriptionStatusEnum fromStatusCode(Integer statusCode) {
        return Arrays.stream(values())
            .filter(value -> Objects.equals(value.getStatusCode(), statusCode))
            .findFirst()
            .orElse(null);
    }

    /**
     * 是否是最终状态
     *
     * @param statusCode
     * @return
     */
    public static boolean isEndStatus(Integer statusCode) {
        return Stream.of(APPROVAL.statusCode, APPROVAL_REJECTED.statusCode).collect(Collectors.toSet()).contains(statusCode);
    }

    /**
     * 是否可审核状态
     *
     * @param statusCode
     * @return
     */
    public static boolean isCanAuditStatus(int statusCode) {
        return Stream.of(WAIT_APPROVAL.statusCode, APPROVAL_ING.statusCode).collect(Collectors.toSet()).contains(statusCode);
    }

}
