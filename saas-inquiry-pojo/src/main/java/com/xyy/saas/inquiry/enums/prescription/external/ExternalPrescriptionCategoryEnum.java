package com.xyy.saas.inquiry.enums.prescription.external;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 外配处方类别
 */
@Getter
@RequiredArgsConstructor
public enum ExternalPrescriptionCategoryEnum implements IntArrayValuable {

    MZ_W(1, "门诊西药中成药"),
    MZ_TCM(2, "门诊中药饮片"),
    JZ_W(2, "急诊西药中成药"),
    JZ_TCM(2, "急诊中药饮片"),
    CHILD_W(2, "儿科西药中成药"),
    CHILD_TCM(2, "儿科中药饮片"),
    MJ(2, "麻、精一"),
    J_TWO(2, "精二"),
    TCM(2, "中药饮片"),
    ZCY(2, "中成药"),
    ZY_TCM(2, "住院西药中成药"),
    OTHER(2, "其它"),
    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ExternalPrescriptionCategoryEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static ExternalPrescriptionCategoryEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(MZ_W);
    }
}
