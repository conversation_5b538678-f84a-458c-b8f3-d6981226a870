package com.xyy.saas.inquiry.enums.system;

import lombok.Getter;

/**
 * @Author: xucao
 * @Date: 2025/01/20 19:22
 * @Description: app版本详情业务类型枚举 0-灰度租户  1-用户忽略版本
 */
@Getter
public enum AppVersionDetailBussnissTypeEnum {
    GRAY_TENANT(0,"灰度租户"),
    USER_IGNORE_VERSION(1,"用户忽略版本");

    private int code;
    private String desc;

    AppVersionDetailBussnissTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
