package com.xyy.saas.inquiry.enums.system;

import lombok.Getter;

/**
 * @Author: xucao
 * @Date: 2025/01/20 19:07
 * @Description: app 版本升级类型枚举 0-强制更新  1-提示可选更新  2-不提示可选更新
 */
@Getter
public enum AppVersionUpgradeTypeEnum {
    FORCE_UPDATE(0, "强制更新"),
    OPTIONAL_UPDATE(1, "提示可选更新"),
    NO_UPDATE(2, "不提示可选更新");

    private Integer code;
    private String desc;

    AppVersionUpgradeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
