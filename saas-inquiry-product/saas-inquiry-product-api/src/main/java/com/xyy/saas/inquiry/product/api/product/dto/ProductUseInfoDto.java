package com.xyy.saas.inquiry.product.api.product.dto;

import com.xyy.saas.inquiry.annotation.FieldCompare;
import com.xyy.saas.inquiry.constant.BasicConstant.FieldCompareGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 商品使用信息")
@Data
public class ProductUseInfoDto {

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productPref;

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18520")
    private Long tenantId;

    @Schema(description = "租户编号（总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "11537")
    private Long headTenantId;

    @Schema(description = "零售价", example = "19488")
    @FieldCompare(description = "零售价", group = FieldCompareGroup.PRICE)
    private BigDecimal retailPrice;

    @Schema(description = "会员价", example = "16302")
    @FieldCompare(description = "会员价", group = FieldCompareGroup.PRICE)
    private BigDecimal memberPrice;

    @Schema(description = "医保项目编码")
    @FieldCompare(description = "医保项目编码")
    private String medicareProjectCode;

    @Schema(description = "医保项目名称", example = "赵六")
    @FieldCompare(description = "医保项目名称")
    private String medicareProjectName;

    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @FieldCompare(description = "医保项目等级")
    private Integer medicareProjectLevel;

    @Schema(description = "医保匹配状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer medicareMatchStatus;

    @Schema(description = "匹配关系上传状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer medicareUploadStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}