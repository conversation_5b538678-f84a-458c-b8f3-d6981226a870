package com.xyy.saas.inquiry.product.api.product.dto;

import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Optional;


@Schema(description = "管理后台 - 标准库商品信息 分页查询请求 VO")
@Data
public class StdlibProductSearchDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品大类")
    private String spuCategory;

    @Schema(description = "商品信息（模糊匹配）")
    private String mixedQuery;
    @Schema(description = "商品信息（自然搜索）")
    private String natureQuery;

    @Schema(description = "商品名称（模糊匹配）")
    private String mixedNameQuery;
    @Schema(description = "商品名称（自然搜索）")
    private String natureNameQuery;

    @Schema(description = "规格型号")
    private String spec;
    @Schema(description = "规格型号（模糊匹配）")
    private String specLike;
    @Schema(description = "处方分类")
    private String presCategory;

    @Schema(description = "条形码")
    private String barcode;
    @Schema(description = "条形码集合")
    private List<String> barcodeList;
    @Schema(description = "生产厂家")
    private String manufacturer;
    @Schema(description = "批准文号")
    private String approvalNumber;
    @Schema(description = "批准文号集合")
    private List<String> approvalNumberList;
    @Schema(description = "中台标准库ID")
    private List<Long> midStdlibIdList;
    @Schema(description = "自建标准库ID")
    private List<Long> idList;
    @Schema(description = "商品通用名集合")
    private List<String> commonNameList;

    @Schema(description = "一级分类")
    private String firstCategory;
    @Schema(description = "二级分类")
    private String secondCategory;
    @Schema(description = "三级分类")
    private String thirdCategory;
    @Schema(description = "四级分类")
    private String fourthCategory;
    @Schema(description = "五级分类")
    private String fiveCategory;
    @Schema(description = "六级分类")
    private String sixCategory;

    @Schema(description = "属性标志")
    private ProductFlag multiFlag;
    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "停用状态")
    private Boolean disable;

    @Schema(description = "是否关联中台标准库")
    private Boolean isMidStdlib;


    @Schema(description = "互联网监管目录")
    private Long internetRegulatoryCatalogId;

    /**
     * 组装默认属性
     */
    public void assembleAttrDefault() {
        // 1. 设置默认状态为使用中
        if (this.getStatus() == null) {
            this.setStatus(ProductStdlibStatusEnum.USING.code);
        }

        // 2. 设置过滤条件 - 中台未停用商品
        ProductFlag multiFlag = Optional.ofNullable(this.getMultiFlag()).orElseGet(ProductFlag::new);
        if (multiFlag.getMidDeactivated() == null) {
            this.setMultiFlag(multiFlag.setMidDeactivated(false));
        }
    }
}
