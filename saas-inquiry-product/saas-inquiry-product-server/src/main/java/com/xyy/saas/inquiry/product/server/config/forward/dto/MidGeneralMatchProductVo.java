package com.xyy.saas.inquiry.product.server.config.forward.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.service.product.mid.MeProductTransformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralMatchProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4170878237647676475L;


    /**
     * 入参五要素集合
     */
    private List<MidGeneralMatchProduct> productList;

    /**
     * 调用链标识
     */
    private String traceId;

    /**
     * 来源（1:荷叶健康，2:智慧脸商城，3POP，5:友商库，15:EC）
     */
    private Integer source;



    /**
     * 组装中台标准库匹配请求参数
     * @param dtoList
     * @return
     */
    public static MidGeneralMatchProductVo of(List<ProductInfoDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }

        List<MidGeneralMatchProduct> productList = dtoList.stream().map(dto -> {
            MidGeneralMatchProduct centerProduct = new MidGeneralMatchProduct();
            centerProduct.setBusinessCode(StringUtils.defaultIfEmpty(dto.getPref(), "-"));
            centerProduct.setSpec(StringUtils.defaultIfEmpty(dto.getSpec(), "-"));
            centerProduct.setApprovalNo(StringUtils.defaultIfEmpty(dto.getApprovalNumber(), "-"));
            centerProduct.setManufacturerName(StringUtils.defaultIfEmpty(dto.getManufacturer(), "-"));
            centerProduct.setGeneralName(StringUtils.defaultIfEmpty(dto.getCommonName(), "-"));
            centerProduct.setSmallPackageCode(StringUtils.defaultIfEmpty(dto.getBarcode(), "-"));
            return centerProduct;
        }).toList();

        MidGeneralMatchProductVo vo = new MidGeneralMatchProductVo();
        vo.setTraceId(MeProductTransformUtil.callTraceId());
        vo.setSource((int) MeProductTransformUtil.getSource());
        vo.setProductList(productList);
        return vo;
    }
}
