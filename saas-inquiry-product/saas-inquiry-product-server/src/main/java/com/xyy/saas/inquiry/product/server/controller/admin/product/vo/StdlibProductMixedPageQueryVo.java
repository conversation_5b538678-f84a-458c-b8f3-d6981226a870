package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.io.Serial;


@Schema(description = "管理后台 - 混合标准库商品信息（中台+自建） 分页查询请求 VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StdlibProductMixedPageQueryVo extends PageParam {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "条形码")
    private String barcode;
    @Schema(description = "商品名称")
    private String productName;
    @Schema(description = "规格型号")
    private String spec;
    @Schema(description = "生产厂家")
    private String manufacturer;
    @Schema(description = "批准文号")
    private String approvalNumber;
    @Schema(description = "中台标准库ID")
    private String midStdlibId;

}
