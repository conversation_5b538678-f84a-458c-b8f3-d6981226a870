package com.xyy.saas.inquiry.product.server.controller.app.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "APP后台 - 商品提报保存 Request VO")
@Data
public class ProductPresentSaveReqVO {

    @Schema(description = "提报记录id", example = "1024")
    private Long id;

    @Schema(description = "通用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芬必得")
    @NotBlank(message = "通用名称不能为空")
    private String commonName;

    @Schema(description = "品牌名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芬必得")
    // @NotBlank(message = "品牌名称不能为空")
    private String brandName;

    @Schema(description = "规格", requiredMode = Schema.RequiredMode.REQUIRED, example = "100mg*10片")
    @NotBlank(message = "规格不能为空")
    private String spec;

    @Schema(description = "条形码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1234567890123")
    @NotBlank(message = "条形码不能为空")
    private String barcode;

    @Schema(description = "商品封面图", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品封面图不能为空")
    private List<String> coverImages;
    @Schema(description = "商品外包装图", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotNull(message = "商品外包装图不能为空")
    private List<String> outerPackageImages;
    @Schema(description = "商品说明书图", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotNull(message = "商品说明书图不能为空")
    private List<String> instructionImages;


    @Schema(description = "包装单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "盒")
    @NotBlank(message = "包装单位不能为空")
    private String unit;

    @Schema(description = "生产厂家", requiredMode = Schema.RequiredMode.REQUIRED, example = "国药准字Z20000001")
    @NotBlank(message = "生产厂家不能为空")
    private String manufacturer;

    @Schema(description = "批准文号", requiredMode = Schema.RequiredMode.REQUIRED, example = "国药准字Z20000001")
    @NotBlank(message = "批准文号不能为空")
    private String approvalNumber;
} 