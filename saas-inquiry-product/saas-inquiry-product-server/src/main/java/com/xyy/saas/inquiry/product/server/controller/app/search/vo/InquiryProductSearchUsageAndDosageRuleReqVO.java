package com.xyy.saas.inquiry.product.server.controller.app.search.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import java.util.List;

/**
 * @Author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:2025/02/19 16:41
 */
@Schema(description = "App - 问诊商品用法用量规则搜索 Request VO")
@Data
@Builder
@ToString(callSuper = true)
public class InquiryProductSearchUsageAndDosageRuleReqVO {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "药品和规格集合")
    private List<ProductUsageAndDosageRuleQueryDto> productUsageAndDosageRuleQueryDtoList;

    @Schema(description = "App - 问诊商品用法用量名称和规格搜索 Request VO")
    @Data
    @Builder
    @ToString(callSuper = true)
    public static class ProductUsageAndDosageRuleQueryDto {

        @Schema(description = "通用名")
        private String commonName;

        @Schema(description = "规格")
        private String attributeSpecification;
    }


}
