package com.xyy.saas.inquiry.product.server.controller.app.search.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用法用量药品规则
 *
 * <AUTHOR>
 * @Date 9/9/24 4:44 PM
 */
@Schema(description = "App - 问诊商品用法用量 Response VO")
@Data
@Builder
@ToString(callSuper = true)
public class InquiryProductUsageAndDosageRuleRespVO {

    @Schema(description = "问诊商品用法用量规则")
    private List<ProductUsageAndDosageRuleDetailDto> productUsageAndDosageRuleDetailDtoList;

    @Schema(description = "App - 问诊商品用法用量规则 Response VO")
    @Data
    @Builder
    @ToString(callSuper = true)
    public static class ProductUsageAndDosageRuleDetailDto {

        @Schema(description = "商品通用名")
        private String commonName;

        @Schema(description = "商品规格")
        private String attributeSpecification;

        @Schema(description = "单次剂量最低值")
        private BigDecimal minSingleDose;

        @Schema(description = "单次剂量最高值")
        private BigDecimal maxSingleDose;

        @Schema(description = "用药频次集合json")
        private List<String> useFrequencyList;

        @Schema(description = "给药单位")
        private String singleUnit;

        @Schema(description = "给药途径")
        private String directions;

    }

}
