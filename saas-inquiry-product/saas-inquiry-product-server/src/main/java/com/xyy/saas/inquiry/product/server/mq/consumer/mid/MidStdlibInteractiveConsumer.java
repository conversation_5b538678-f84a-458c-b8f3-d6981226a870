package com.xyy.saas.inquiry.product.server.mq.consumer.mid;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferTypeEnum;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductPresentDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse2;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.mq.message.mid.MidStdlibInteractiveEvent;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidStdlibInteractiveMessage;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidStdlibInteractiveMessage.Type;
import com.xyy.saas.inquiry.product.server.mq.producer.mid.MidStdlibInteractiveProducer;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import com.xyy.saas.inquiry.product.server.service.product.mid.ProductMidStdlibService;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: xucao
 * @Date: 2024/12/26 11:03
 * @Description: 中台标准库数据交互事件（新品提报，匹配）处理
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_product_server_mq_consumer_mid_MidStdlibInteractiveConsumer",
    topic = MidStdlibInteractiveEvent.TOPIC)
public class MidStdlibInteractiveConsumer {

    @Resource
    private ProductMidStdlibService midStdlibService;

    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private ProductTransferRecordService productTransferRecordService;

    @Resource
    private MidStdlibInteractiveProducer midStdlibInteractiveProducer;

    @EventBusListener
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(MidStdlibInteractiveEvent event) {
        MidStdlibInteractiveMessage msg = event.getMsg();
        List<String> productPrefList = msg.getProductPrefList();
        if (CollectionUtils.isEmpty(productPrefList)) {
            return;
        }
        Type type = Type.getByCode(msg.getType());
        if (type == null) {
            log.error("中台交互消息, unsupported type: {}", msg.getType());
            return;
        }
        log.info("中台交互【{}】消息: {}", type.desc, JSON.toJSONString(msg));

        // 1. 查询商品信息
        List<ProductInfoDto> dtoList = productInfoService.listProductInfoByPref(productPrefList);
        if (CollectionUtils.isEmpty(dtoList)) {
            log.error("中台交互【{}】消息, 查询不到商品数据, 跳过！ productPrefList: {}", type.desc, productPrefList);
            return;
        }

        // 2. 根据消息类型处理
        switch (type) {
            case Type.MATCH:
                handleMatchProduct(dtoList);
                break;
            case Type.REPORT:
                handleReportProduct(dtoList);
                break;
        }
    }

    /**
     * 处理商品匹配
     */
    private void handleMatchProduct(List<ProductInfoDto> dtoList) {
        dtoList.stream().collect(Collectors.groupingBy(ProductInfoDto::getTenantId))
            .forEach((tenantId, list) -> {
                List<ProductInfoDto> productInfoDtos = stdlibService.matchProduct2MidStdlib(tenantId, list);
                if (CollectionUtils.isNotEmpty(productInfoDtos)) {
                    productInfoDtos.forEach(p -> {
                        log.info("中台交互【{}】消息, 匹配到自建标准库数据, pref: {}, stdlibId: {}, midStdlibId: {}",
                            Type.MATCH.desc, p.getPref(), p.getStdlibId(), p.getMidStdlibId());
                        productInfoService.saveOrUpdateProduct(p, null);
                    });
                }
            });
    }

    /**
     * 处理商品提报
     */
    private void handleReportProduct(List<ProductInfoDto> dtoList) {
        dtoList.stream().collect(Collectors.groupingBy(ProductInfoDto::getTenantId))
            .forEach((tenantId, list) -> {
                // 提报中台
                stdlibService.reportProduct2MidStdlib(tenantId, list);
            });
    }

}
