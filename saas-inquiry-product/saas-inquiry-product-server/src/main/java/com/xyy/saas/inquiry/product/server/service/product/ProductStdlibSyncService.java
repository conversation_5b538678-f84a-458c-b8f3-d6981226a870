package com.xyy.saas.inquiry.product.server.service.product;

import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductSyncReqVo;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibSyncDto;
import jakarta.annotation.Nonnull;

import java.util.List;

public interface ProductStdlibSyncService {

    /**
     * 开始单个同步
     */
    void startSingleSync(StdlibProductSyncReqVo reqVo);

    /**
     * 开始全量同步
     */
    ProductStdlibSyncDto startFullSync();

    /**
     * 全量同步执行（带重试）
     * @param guid
     * @param retryTimes 重试次数，如果传2，表示失败后，会重试 2 次（总共会尝试3次）
     */
    void syncWithRetry(String guid, int retryTimes);

    /**
     * 全量同步执行（完整同步）
     * @param guid
     * @return
     */
    boolean syncWithCompleteProgress(String guid);

    /**
     * 全量同步执行（分批独立事务，防止出现长事务）
     * @param progress
     * @param startId
     * @return
     */
    ProductStdlibSyncDO syncWithStandaloneTransaction(@Nonnull ProductStdlibSyncDO progress, long startId);

    /**
     * 保存或更新标准库（开启新事务）
     * @param midStdlibIdList
     * @param imageSync
     * @return
     */
    List<ProductStdlibDO> saveOrUpdateStdlibFromMidWithNewTx(List<Long> midStdlibIdList, boolean imageSync);

    /**
     * 取消同步任务
     */
    boolean cancelSync(String taskId);

    /**
     * 获取同步进度
     */
    ProductStdlibSyncDto getProgress(String taskId);

    /**
     * 获取同步进度历史
     */
    List<ProductStdlibSyncDto> getProgressList();

    /**
     * 更新同步进度
     */
    void saveOrUpdateProgress(ProductStdlibSyncDto progress);

    /**
     * 执行所有未开始的任务
     */
    List<ProductStdlibSyncDO> runNotStartFullSyncTasks();
} 