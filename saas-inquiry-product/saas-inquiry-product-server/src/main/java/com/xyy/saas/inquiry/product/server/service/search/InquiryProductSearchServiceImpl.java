package com.xyy.saas.inquiry.product.server.service.search;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.config.forward.InquiryProductForwardClient;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductAddReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchUsageAndDosageRuleReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductUsageAndDosageRuleRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductUsageAndDosageRuleRespVO.ProductUsageAndDosageRuleDetailDto;
import com.xyy.saas.inquiry.product.server.convert.forward.ProductForwardConvert;
import com.xyy.saas.inquiry.product.server.convert.product.ProductConvert;
import com.xyy.saas.inquiry.product.server.service.forward.dto.DiagnosticsForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.ProductForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.ProductStandardForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.UsageAndDosageDrugRuleForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.UsageAndDosageRuleDrugQueryForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.UsageAndDosageRuleDrugQueryForwardDto.UsageAndDosageRuleDrugDetailQueryDto;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 问诊商品搜索服务
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/25 20:33
 */
@Service
@RefreshScope
public class InquiryProductSearchServiceImpl implements InquiryProductSearchService {

    // TODO 后续建设商品服务后,替换掉转发服务
    @Resource
    private InquiryProductForwardClient inquiryProductForwardClient;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private TenantServicePackRelationApi tenantServicePackRelationApi;

    @Resource
    private ProductStdlibService stdlibService;
    @Resource
    private ProductInfoService productInfoService;


    @Value("${inquiry.product.search.switch.self:false}")
    private boolean selfSearchSwitch;


    @Override
    public CommonResult<List<InquiryProductSearchRespVO>> recommendCommonProducts(InquiryProductSearchReqVO reqVO) {
        return success(new ArrayList<>());
    }

    @Override
    public CommonResult<List<String>> suggestNamesByProductsName(InquiryProductSearchReqVO reqVO) {
        if (selfSearchSwitch) {
            Long catalogId = getInternetSuperVisionCatalogId();
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto()
                .setSpuCategory(MedicineTypeEnum.getSpuCategory(reqVO.getMedicineType()))
                .setMixedNameQuery(reqVO.getProductName())
                .setInternetRegulatoryCatalogId(catalogId)
                .setIsMidStdlib(true);
            return success(stdlibService.listDistinctCommonName(searchDto, 150));
        }

        ProductForwardDto productForwardDto = ProductForwardConvert.INSTANCE.convertProductDto(reqVO);
        ForwardResult<List<String>> forwardResult = inquiryProductForwardClient.getSuggestNamesByProductsNameV2(productForwardDto);
        return convertForwardResult(forwardResult);
    }

    @Override
    public CommonResult<PageResult<InquiryProductSearchRespVO>> productsByNameSpec(InquiryProductSearchReqVO reqVO) {
        if (selfSearchSwitch) {
            Long catalogId = getInternetSuperVisionCatalogId();
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto()
                .setSpuCategory(MedicineTypeEnum.getSpuCategory(reqVO.getMedicineType()))
                .setMixedNameQuery(reqVO.getProductName())
                .setSpec(reqVO.getAttributeSpecification())
                .setInternetRegulatoryCatalogId(catalogId)
                .setIsMidStdlib(true);

            // productName 可以提供 名称+空格+规格 查询，需要切割, 考虑中文全角空格和英文空格
            String productName = reqVO.getProductName();
            if (StringUtils.isNotBlank(productName) && (productName.contains("　") || productName.contains(" "))) {
                // 中文全角空格
                String[] split = productName.trim().split("[ 　]");
                searchDto.setMixedNameQuery(split.length > 0 ? split[0] : null);
                searchDto.setSpecLike(split.length > 1 ? split[1] : null);
            }

            List<ProductStdlibDto> voList = stdlibService.searchStdlibProductListGroupByNameSpec(searchDto, 150);
            List<InquiryProductSearchRespVO> pageList = voList.stream()
                .skip(reqVO.getPageNo() - 1).limit(reqVO.getPageSize())
                .map(ProductConvert.INSTANCE::ProductStdlibDto2InquiryProductSearchRespVO).toList();

            return success(new PageResult<>(pageList, (long) voList.size()));
        }

        ProductForwardDto productForwardDto = ProductForwardConvert.INSTANCE.convertProductDto(reqVO);
        ForwardResult<List<ProductStandardForwardDto>> forwardResult = inquiryProductForwardClient.getProductsByNameAndSpecV2(productForwardDto);
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        List<InquiryProductSearchRespVO> searchRespVOS = ProductForwardConvert.INSTANCE.convertSearchRespDtos(forwardResult.getResult());
        // 中台固定返回150条,手动分页给前端
        PageResult<InquiryProductSearchRespVO> pageResult = new PageResult<>(searchRespVOS.stream().skip(reqVO.getPageNo() - 1).limit(reqVO.getPageSize()).collect(Collectors.toList()), (long) searchRespVOS.size());
        return success(pageResult);
    }

    @Override
    public CommonResult<List<InquiryProductSearchRespVO>> productByBarcode(InquiryProductSearchReqVO reqVO) {
        if (selfSearchSwitch) {
            Long catalogId = getInternetSuperVisionCatalogId();
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto()
                .setSpuCategory(MedicineTypeEnum.getSpuCategory(reqVO.getMedicineType()))
                .setBarcode(reqVO.getBarCode())
                .setInternetRegulatoryCatalogId(catalogId)
                .setIsMidStdlib(true);
            List<InquiryProductSearchRespVO> respVOS = stdlibService.searchStdlibProductList(searchDto, 150)
                .stream().map(ProductConvert.INSTANCE::ProductStdlibDto2InquiryProductSearchRespVO).toList();
            return success(respVOS);
        }

        ProductForwardDto productForwardDto = ProductForwardConvert.INSTANCE.convertProductDto(reqVO);
        ForwardResult<List<ProductStandardForwardDto>> forwardResult = inquiryProductForwardClient.getMedicinesByBarcode(productForwardDto);
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        return success(ProductForwardConvert.INSTANCE.convertSearchRespDtos(forwardResult.getResult()));
    }

    @Override
    public CommonResult<List<InquiryDiagnosticsSearchRespDto>> productDiagnostics(InquiryDiagnosticsSearchReqDto reqDto) {
        if (CollUtil.isEmpty(reqDto.getProductSearchList())) {
            return success(new ArrayList<>());
        }
        DiagnosticsForwardDto diagnosticsForwardDto = ProductForwardConvert.INSTANCE.convertDiagnosticsDto(reqDto);

        if (selfSearchSwitch) {
            // 前端传入自建商品库id,转换中台标准库id查询
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto().setIdList(reqDto.getProductSearchList().stream().map(p -> NumberUtil.parseLong(p.getPref(), 0L)).toList());
            Map<Long, Long> productMap = stdlibService.searchStdlibProductList(searchDto, searchDto.getIdList().size()).stream().collect(Collectors.toMap(ProductStdlibDto::getId, ProductStdlibDto::getMidStdlibId, (a, b) -> b));
            for (ProductForwardDto dto : diagnosticsForwardDto.getDruginfoList()) {
                if (productMap.get(NumberUtil.parseLong(dto.getPref(), 0L)) != null) {
                    dto.setPref(productMap.get(NumberUtil.parseLong(dto.getPref())).toString());
                }
            }
        }
        ForwardResult<List<InquiryDiagnosticsSearchRespDto>> forwardResult = inquiryProductForwardClient.getDiagnostics(diagnosticsForwardDto);

        return convertForwardResult(forwardResult);
    }

    @Override
    public CommonResult<String> manualAddProduct(InquiryProductAddReqVO reqVO) {
        if (selfSearchSwitch) {
            ProductInfoDto productInfoDto = ProductConvert.INSTANCE.InquiryProductAddReqVO2ProductInfoDto(reqVO);
            // 问诊临时建品
            Long id = productInfoService.saveOrUpdateProduct(productInfoDto, ProductBizTypeEnum.INQUIRY_ADD_TEMPORARY);
            return success(String.valueOf(id));
        }

        ForwardResult<String> forwardResult = inquiryProductForwardClient.addProductForDrugstore(reqVO);
        return convertForwardResult(forwardResult);
    }


    @Override
    public CommonResult<List<InquiryProductDetailDto>> queryProductStandardListByStandardIds(List<String> standardIds) {
        if (CollUtil.isEmpty(standardIds)) {
            return success(new ArrayList<>());
        }

        if (selfSearchSwitch) {
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto();
            searchDto.setMidStdlibIdList(standardIds.stream().map(p -> NumberUtil.parseLong(p, -1L)).toList());
            List<ProductStdlibDto> productStdlibDtos = stdlibService.searchStdlibProductList(searchDto, searchDto.getMidStdlibIdList().size());
            return success(ProductForwardConvert.INSTANCE.convertProductDetailStdDto(productStdlibDtos));
        }

        ForwardResult<List<ProductStandardForwardDto>> forwardResult = inquiryProductForwardClient.queryProductStandardListByStandardIds(standardIds);
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        return success(ProductForwardConvert.INSTANCE.convertProductDetailDto(forwardResult.getResult()));
    }

    private static <T> CommonResult<T> convertForwardResult(ForwardResult<T> forwardResult) {
        if (forwardResult.isSuccess()) {
            return success(forwardResult.getResult());
        }
        return CommonResult.error(forwardResult.getMsg());
    }

    @Override
    public CommonResult<InquiryProductUsageAndDosageRuleRespVO> searchProductUsageAndDosageRule(InquiryProductSearchUsageAndDosageRuleReqVO reqVO) {

        if (reqVO.getAge() == null || CollectionUtils.isEmpty(reqVO.getProductUsageAndDosageRuleQueryDtoList())) {
            return success(InquiryProductUsageAndDosageRuleRespVO.builder().productUsageAndDosageRuleDetailDtoList(Lists.newArrayList()).build());
        }

        // 入参数据转换
        List<UsageAndDosageRuleDrugDetailQueryDto> usageAndDosageRuleDrugDetailQueryDtoList = reqVO.getProductUsageAndDosageRuleQueryDtoList().stream()
            .map(item -> UsageAndDosageRuleDrugDetailQueryDto.builder()
                .commonName(item.getCommonName())
                .specification(item.getAttributeSpecification()).build()).toList();

        UsageAndDosageRuleDrugQueryForwardDto usageAndDosageRuleDrugQueryForwardDto = UsageAndDosageRuleDrugQueryForwardDto.builder()
            .age(reqVO.getAge())
            .usageAndDosageRuleDrugDetailQueryDtoList(usageAndDosageRuleDrugDetailQueryDtoList).build();
        ForwardResult<UsageAndDosageDrugRuleForwardDto> forwardResult = inquiryProductForwardClient.queryProductUsageAndDosageRule(usageAndDosageRuleDrugQueryForwardDto);

        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }

        if (forwardResult.getResult() == null || CollectionUtils.isEmpty(forwardResult.getResult().getUsageAndDosageDrugRuleDetailDtoList())) {
            return success(InquiryProductUsageAndDosageRuleRespVO.builder().productUsageAndDosageRuleDetailDtoList(Lists.newArrayList()).build());
        }

        // List<DictDataRespDTO> dictDataList = dictDataApi.getDictDataList(reqVO.getTenantId(), DictTypeConstants.DRUG_USE_FREQUENCY);
        // Map<String, String> dictDataMap = dictDataList.stream().collect(Collectors.toMap(DictDataRespDTO::getLabel, DictDataRespDTO::getValue, (v1, v2) -> v2));

        // 返参数据转换
        List<ProductUsageAndDosageRuleDetailDto> list = forwardResult.getResult().getUsageAndDosageDrugRuleDetailDtoList().stream()
            .map(item -> {

                // List<String> useFrequencyList = new ArrayList<>();
                //
                // if (CollectionUtils.isNotEmpty(item.getUseFrequencyListList())) {
                //     useFrequencyList = item.getUseFrequencyListList().stream()
                //         .filter(dictDataMap::containsKey)
                //         .map(dictDataMap::get).toList();
                // }

                return ProductUsageAndDosageRuleDetailDto.builder()
                    .commonName(item.getCommonName())
                    .attributeSpecification(item.getSpecification())
                    .minSingleDose(item.getMinSingleDose())
                    .maxSingleDose(item.getMaxSingleDose())
                    .useFrequencyList(item.getUseFrequencyListList())
                    .singleUnit(item.getSingleUnit())
                    .directions(item.getDirections()).build();
            }).toList();

        return success(InquiryProductUsageAndDosageRuleRespVO.builder().productUsageAndDosageRuleDetailDtoList(list).build());
    }

    @Override
    public CommonResult<List<InquiryProductDetailDto>> getProductStandardListByBarcodeList(List<String> barCodeList, Integer medicineType) {

        if (CollectionUtils.isEmpty(barCodeList)) {
            return success(Lists.newArrayList());
        }

        List<String> barCodeDistinctList = barCodeList.stream().filter(StringUtils::isNotBlank).distinct().toList();

        if (CollectionUtils.isEmpty(barCodeDistinctList)) {
            return success(Lists.newArrayList());
        }

        if (selfSearchSwitch) {
            Long catalogId = getInternetSuperVisionCatalogId();
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto()
                .setSpuCategory(MedicineTypeEnum.getSpuCategory(medicineType))
                .setBarcodeList(barCodeList)
                .setInternetRegulatoryCatalogId(catalogId)
                .setIsMidStdlib(true);
            List<InquiryProductDetailDto> respVOS = stdlibService.searchStdlibProductList(searchDto, 1000)
                .stream().map(ProductConvert.INSTANCE::ProductStdlibDto2InquiryProductDetailDto).toList();
            return success(respVOS);
        }

        ForwardResult<List<ProductStandardForwardDto>> forwardResult = inquiryProductForwardClient.getProductStandardListByBarcodeList(barCodeDistinctList);
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        return success(ProductForwardConvert.INSTANCE.convertProductDetailDto(forwardResult.getResult()));
    }

    @Override
    public CommonResult<List<InquiryProductDetailDto>> getProductStandardListByApprovalNoList(List<String> approvalNoList, Integer medicineType) {

        if (CollectionUtils.isEmpty(approvalNoList)) {
            return success(Lists.newArrayList());
        }

        List<String> approvalNoDistinctList = approvalNoList.stream().filter(StringUtils::isNotBlank).distinct().toList();

        if (CollectionUtils.isEmpty(approvalNoDistinctList)) {
            return success(Lists.newArrayList());
        }

        if (selfSearchSwitch) {
            Long catalogId = getInternetSuperVisionCatalogId();
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto()
                .setSpuCategory(MedicineTypeEnum.getSpuCategory(medicineType))
                .setApprovalNumberList(approvalNoList)
                .setInternetRegulatoryCatalogId(catalogId)
                .setIsMidStdlib(true);
            List<InquiryProductDetailDto> respVOS = stdlibService.searchStdlibProductList(searchDto, 1000)
                .stream().map(ProductConvert.INSTANCE::ProductStdlibDto2InquiryProductDetailDto).toList();
            return success(respVOS);
        }

        ForwardResult<List<ProductStandardForwardDto>> forwardResult = inquiryProductForwardClient.getProductStandardListByApprovalNoList(approvalNoDistinctList);
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        return success(ProductForwardConvert.INSTANCE.convertProductDetailDto(forwardResult.getResult()));
    }

    @Override
    public CommonResult<List<InquiryProductDetailDto>> getProductStandardByProductNamesAndType(List<String> commonNameList, Integer medicineType) {

        if (selfSearchSwitch) {
            Long catalogId = getInternetSuperVisionCatalogId();
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto()
                .setSpuCategory(MedicineTypeEnum.getSpuCategory(medicineType))
                .setCommonNameList(commonNameList)
                .setInternetRegulatoryCatalogId(catalogId)
                .setIsMidStdlib(true);
            List<InquiryProductDetailDto> respVOS = stdlibService.searchStdlibProductList(searchDto, 1000)
                .stream().map(ProductConvert.INSTANCE::ProductStdlibDto2InquiryProductDetailDto).toList();
            return success(respVOS);
        }

        ProductForwardDto productForwardDto = new ProductForwardDto();
        productForwardDto.setProductNameList(commonNameList);
        productForwardDto.setProductType(medicineType);
        ForwardResult<List<ProductStandardForwardDto>> forwardResult = inquiryProductForwardClient.getProductStandardByProductNamesAndType(productForwardDto);
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        return success(ProductForwardConvert.INSTANCE.convertProductDetailDto(forwardResult.getResult()));
    }


    /**
     * 查询当前TenantId门店 互联网监管目录id 用于搜索过滤
     *
     * @return
     */
    private Long getInternetSuperVisionCatalogId() {
        return tenantServicePackRelationApi.getTenantOrganCatalogId(OrganTypeEnum.INTERNET_SUPERVISION);
    }
}
