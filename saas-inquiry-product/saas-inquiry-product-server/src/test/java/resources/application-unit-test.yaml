spring:
  cloud:
    nacos:
      discovery:
        server-addr: 10.22.68.231:8848
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        import-check:
          enabled: false
  #        file-extension: yaml
  main:
    lazy-initialization: true # 开启懒加载，加快速度
    banner-mode: off # 单元测试，禁用 Banner

--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  datasource:
    name: xyy_saas_inquiry_test
    #    url: ******************************/${spring.datasource.name}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
    url: ************************************************************************************************************************************************************************************** # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
    #          url: ********************************/${spring.datasource.dynamic.datasource.master.name} # PostgreSQL 连接的示例
    #          url: *********************************** # Oracle 连接的示例
    #          url: ********************************************=${spring.datasource.dynamic.datasource.master.name} # SQLServer 连接的示例
    #          url: jdbc:dm://***********:5236?schema=RUOYI_VUE_PRO # DM 连接的示例
    username: app_remote_prescription_w
    password: UY27Hdy9uTYe
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      async-init: true # 单元测试，异步初始化 Druid 连接池，提升启动速度
      initial-size: 1 # 单元测试，配置为 1，提升启动速度
  sql:
    init:
      schema-locations: classpath:/sql/create_tables.sql

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: db2-saas-test.redis.ybm100.top # 地址
      port: 40001 # 端口
      database: 1 # 数据库索引
      password: LeQp10w4swa76Ies # 密码，建议生产环境开启

mybatis:
  lazy-initialization: true # 单元测试，设置 MyBatis Mapper 延迟加载，加速每个单元测试

dubbo:
  config-center:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}
  registry:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}
  metadata-report:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}


--- #################### 定时任务相关配置 ####################

--- #################### 配置中心相关配置 ####################

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项（单元测试，禁用 Lock4j）

--- #################### 监控相关配置 ####################

--- #################### 芋道相关配置 ####################

# 芋道配置项，设置当前项目所有自定义的配置
yudao:
  info:
    base-package: cn.iocoder.yudao.module
