package com.xyy.saas.inquiry.signature.api.prescription.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 处方签章Dto
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrescriptionSignatureInitDto extends PrescriptionSignatureBaseDto {

    /**
     * 文档参数
     */
    @NotNull(message = "文档参数不能为空")
    private PrescriptionParamDto param;

    /**
     * 是否自动开方：0 否  、 1是
     */
    private Integer autoInquiry;

    /**
     * 签章平台标识
     */
    private Integer signaturePlatform;

    /**
     * 法大大应用
     */
    private Integer signaturePlatformConfigId;

}
