package com.xyy.saas.inquiry.signature.server.controller.admin.person.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 签章平台用户新增/修改 Request VO")
@Data
public class InquirySignaturePersonSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5521")
    private Long id;

    @Schema(description = "用户uid", requiredMode = Schema.RequiredMode.REQUIRED, example = "10984")
    @NotNull(message = "用户uid不能为空")
    private Long userId;

    @Schema(description = "法大大平台为该用户在该应用appId范围内分配的唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "7810")
    @NotEmpty(message = "法大大平台为该用户在该应用appId范围内分配的唯一标识不能为空")
    private String openUserId;

    /**
     * 签章平台  0-无 1-法大大 {@link com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum}
     */
    private Integer signaturePlatform;

    /**
     * 签章平台应用标识id
     */
    private Integer signaturePlatformConfigId;

    @Schema(description = "个人用户的法大大帐号，仅限手机号或邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "个人用户的法大大帐号，仅限手机号或邮箱不能为空")
    private String accountName;

    @Schema(description = "个人用户真实姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "个人用户真实姓名不能为空")
    private String userName;

    @Schema(description = "个人手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "个人手机号不能为空")
    private String mobile;

    @Schema(description = "个人银行账户号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "个人银行账户号不能为空")
    private String bankAccountNo;

    @Schema(description = "证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证 不能为空")
    private String userIdentType;

    @Schema(description = "证件号。跟证件类型关联", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "证件号。跟证件类型关联不能为空")
    private String userIdentNo;

    @Schema(description = "法大大签名印章ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3985")
    @NotEmpty(message = "法大大签名印章ID不能为空")
    private String sealId;

    @Schema(description = "0-未认证,1-已认证,3已设置签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "0-未认证,1-已认证,3已设置签名不能为空")
    private Integer userStatus;

}