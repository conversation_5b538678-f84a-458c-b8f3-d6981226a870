package com.xyy.saas.inquiry.signature.server.convert.prescriptiontemplate;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateFieldDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.api.prescriptiontemplate.dto.InquiryPrescriptionTemplateDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractAddParticipantVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.prescriptiontemplate.InquiryPrescriptionTemplateDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：PrescriptionTemplateConvert
 * @Author: xucao
 * @Date: 2024/10/31 16:11
 * @Description: 处方笺模型转换类
 */
@Mapper
public interface PrescriptionTemplateConvert {

    PrescriptionTemplateConvert INSTANCE = Mappers.getMapper(PrescriptionTemplateConvert.class);

    InquiryPrescriptionTemplateDto convertRespVO2DTO(InquiryPrescriptionTemplateRespVO informationDO);

    default TemplateSignCheckedDto convertPsInit(PrescriptionSignatureInitDto psInitDto) {
        return TemplateSignCheckedDto.builder()
            .templateId(psInitDto.getTemplateId())
            .field(null).userId(psInitDto.getParticipantItem().getUserId())
            .checkSignParam(Objects.equals(AutoInquiryEnum.NO.getCode(), psInitDto.getAutoInquiry()))
            .signaturePlatformConfigId(psInitDto.getSignaturePlatformConfigId()).build();
    }

    default TemplateSignCheckedDto convertAppendPart(InquiryPrescriptionTemplateDO prescriptionTemplateDO, InquirySignatureContractDO signatureContractDO, InquirySignatureContractAddParticipantVO addParticipantVO) {
        String field = CollUtil.isEmpty(signatureContractDO.getParticipants()) ? null : signatureContractDO.getParticipants().getLast().getActorField();

        return TemplateSignCheckedDto.builder()
            .templateId(prescriptionTemplateDO.getId())
            .field(field).userId(addParticipantVO.getParticipantItem().getUserId())
            .checkSignParam(!addParticipantVO.isAutoAudit())
            .signaturePlatformConfigId(signatureContractDO.extGet().getPlatformConfigId()).build();
    }

    PrescriptionTemplateFieldDto convertField(PrescriptionTemplateField nextTemplateField);

    default PrescriptionTemplateFieldDto convertField(PrescriptionTemplateField nextTemplateField, InquirySignatureCaAuthRespVO caAuthRespVO) {
        PrescriptionTemplateFieldDto templateFieldDto = convertField(nextTemplateField);
        templateFieldDto.setAccessPlatform(caAuthRespVO.isDrawnSign() || caAuthRespVO.isOfflinePharmacist() || caAuthRespVO.getUserId() == null ? CommonStatusEnum.DISABLE.getStatus() : nextTemplateField.getAccessPlatform());
        templateFieldDto.setUserSignImgUrl(caAuthRespVO.isDrawnSign() ? caAuthRespVO.getDrawnSignUrl() : caAuthRespVO.getRealSignatureUrl());
        return templateFieldDto;
    }
}
