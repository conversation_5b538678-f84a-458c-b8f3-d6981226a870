package com.xyy.saas.inquiry.signature.server.dal.mysql.ca;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.ca.InquirySignatureCaAuthDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * CA认证 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquirySignatureCaAuthMapper extends BaseMapperX<InquirySignatureCaAuthDO> {

    default InquirySignatureCaAuthDO selectOneByUserId(Long userId, Integer signaturePlatform) {
        return selectOne(new LambdaQueryWrapperX<InquirySignatureCaAuthDO>()
                .eq(InquirySignatureCaAuthDO::getUserId, userId)
                .eqIfPresent(InquirySignatureCaAuthDO::getSignaturePlatform, signaturePlatform)
            , false);
    }


}