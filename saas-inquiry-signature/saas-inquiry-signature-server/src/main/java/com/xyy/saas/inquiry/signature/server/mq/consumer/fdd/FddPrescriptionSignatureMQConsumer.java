package com.xyy.saas.inquiry.signature.server.mq.consumer.fdd;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.template.TemplateFieldTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.convert.prescription.InquiryPrescriptionSignatureConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.FddPrescriptionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureMessage;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSignTaskBussService;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.SignTaskCallbackDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import com.xyy.saas.inquiry.signature.server.service.fdd.handler.FddCallbackSignTaskHandler;
import com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Desc 法大大处方签署任务回调
 * <AUTHOR> {@link FddCallbackSignTaskHandler#handle(FddCallbackEvent, String)}
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_FddSignPrescriptionMQConsumer",
    topic = FddPrescriptionSignatureEvent.TOPIC)
public class FddPrescriptionSignatureMQConsumer {

    @Resource
    private InquirySignaturePrescriptionService inquirySignaturePrescriptionService;

    @Resource
    private InquiryPrescriptionTemplateService inquiryPrescriptionTemplateService;

    @Resource
    protected InquirySignatureContractService inquirySignatureContractService;

    @Resource
    private FddSignTaskBussService fddSignTaskBussService;


    @EventBusListener
    public void fddPrescriptionSignatureMQConsumer(FddPrescriptionSignatureEvent fddPrescriptionSignatureEvent) {
        SignTaskCallbackDto signTaskCallbackDto = fddPrescriptionSignatureEvent.getMsg();
        log.info("【法大大处方回调MQ】,bizId:{}", signTaskCallbackDto.getTransReferenceId());
        // 签署为空, 异常、超时、完结、拒签 || 无签署方 无需处理
        if (StringUtils.isAllBlank(signTaskCallbackDto.getTransReferenceId(), signTaskCallbackDto.getActorId())
            || !FddCallbackEvent.isSignedEvent(signTaskCallbackDto.getEventId())) {
            return;
        }
        // 检查合同和处方模板
        InquirySignatureContractDO signatureContract = inquirySignatureContractService.querySignatureContract(InquirySignatureContractSaveReqVO.builder()
            .bizId(signTaskCallbackDto.getTransReferenceId())
            .thirdId(signTaskCallbackDto.getSignTaskId()).build());
        if (signatureContract == null || signatureContract.getTemplateIdLong() == null) {
            return;
        }
        // 如果是签署完成事件  处理签署pdf 从法大大获取签章图片
        if (StringUtils.equalsIgnoreCase(signTaskCallbackDto.getEventId(), FddCallbackEvent.SIGN_TASK_FINISHED.eventCode)) {
            signFinishHandlePdfUrl(signatureContract);
            return;
        }
        // 处理签章结束
        signTaskFinish(signatureContract, signTaskCallbackDto);

        // 此mq仅处理法大大处方回调，非法大大或者降级处方不处理后续
        if (!Objects.equals(signatureContract.getSignaturePlatform(), SignaturePlatformEnum.FDD.getCode()) || CommonStatusEnum.isEnable(signatureContract.getSelfDrawn())) {
            log.info("【法大大处方回调MQ】非法大大 或处方降级,不处理-bizId:{}", signTaskCallbackDto.getTransReferenceId());
            return;
        }
        PrescriptionSignatureMessage psMsg = InquiryPrescriptionSignatureConvert.INSTANCE.convertFddCallbackMessage(signTaskCallbackDto, signatureContract);
        // 系统全部取自绘处方
        psMsg.setPdfUrl(inquirySignaturePrescriptionService.drawnPrescriptionSelf(signatureContract.getPref(), true));
        inquirySignaturePrescriptionService.signaturePrescriptionCallback(psMsg);
    }

    private void signTaskFinish(InquirySignatureContractDO signatureContract, SignTaskCallbackDto signTaskCallbackDto) {
        List<PrescriptionTemplateField> templateAccessPlatformFields = inquiryPrescriptionTemplateService.getTemplateAccessPlatformFields(signatureContract.getTemplateIdLong());

        boolean accessPlatformEnd = Optional.ofNullable(templateAccessPlatformFields).orElse(List.of()).stream()
            .filter(f -> Objects.equals(f.getFieldType(), TemplateFieldTypeEnum.SIGN_PICTURE.code))
            .sorted(Comparator.comparing(f -> Optional.ofNullable(f.getSorted()).orElse(0)))
            .dropWhile(f -> !Objects.equals(f.getField(), signTaskCallbackDto.getActorId())) // 找到第一个满足的条件为止，后面所有的元素
            .skip(1)  // 跳过当前匹配项
            .noneMatch(f -> CommonStatusEnum.isEnable(f.getAccessPlatform())); // 如果后续都没有签署平台，结束

        if (accessPlatformEnd) {
            log.info("【法大大处方回调MQ】签署任务完成,templateAccessPlatformFields:{}", templateAccessPlatformFields);
            fddSignTaskBussService.signTaskFinish(signatureContract.extGet().getPlatformConfigId(), signTaskCallbackDto.getSignTaskId());
        }
    }

    /**
     * 签章结束处理处理pdf图片
     *
     * @param signatureContract
     */
    private void signFinishHandlePdfUrl(InquirySignatureContractDO signatureContract) {
        log.info("【法大大处方回调MQ】签署任务完成,bizId:{}", signatureContract.getBizId());
        try {
            String platformPdfUrl = fddSignTaskBussService.downContractAndUpload(signatureContract.extGet().getPlatformConfigId(), signatureContract.getThirdId(), FileTypeEnum.PDF);
            if (StringUtils.isNotBlank(platformPdfUrl)) {
                inquirySignatureContractService.updateSignatureContract(InquirySignatureContractSaveReqVO.builder()
                    .id(signatureContract.getId())
                    .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
                    .ext(signatureContract.extGet().setPlatformPdfUrl(platformPdfUrl))
                    .build());
            }
        } catch (Exception e) {
            log.error("【法大大处方回调MQ】签署任务完成，下载处方笺保存失败,bizId:{}", signatureContract.getBizId(), e);
        }
    }


}
