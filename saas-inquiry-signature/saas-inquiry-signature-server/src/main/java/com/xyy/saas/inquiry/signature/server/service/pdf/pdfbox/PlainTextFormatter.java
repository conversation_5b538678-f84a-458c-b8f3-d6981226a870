package com.xyy.saas.inquiry.signature.server.service.pdf.pdfbox;


import org.apache.pdfbox.pdmodel.PDAppearanceContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * TextFormatter to handle plain text formatting.
 * <p>
 * The text formatter will take a single value or an array of values which are treated as paragraphs.
 */

public class PlainTextFormatter {

    enum TextAlign {
        LEFT(0), CENTER(1), RIGHT(2), JUSTIFY(4);

        private final int alignment;

        private TextAlign(int alignment) {
            this.alignment = alignment;
        }

        int getTextAlign() {
            return alignment;
        }

        public static PlainTextFormatter.TextAlign valueOf(int alignment) {
            for (PlainTextFormatter.TextAlign textAlignment : PlainTextFormatter.TextAlign.values()) {
                if (textAlignment.getTextAlign() == alignment) {
                    return textAlignment;
                }
            }
            return PlainTextFormatter.TextAlign.LEFT;
        }
    }

    /**
     * The scaling factor for font units to PDF units
     */
    private static final int FONTSCALE = 1000;

    private final AppearanceStyle appearanceStyle;
    private final boolean wrapLines;
    private final float width;

    private final PDAppearanceContentStream contents;

    private final List<Float[]> positionList;
    private final List<String> textList;
    private final PDRectangle clipRect;

    private final PlainText textContent;
    private final PlainTextFormatter.TextAlign textAlignment;

    private float horizontalOffset;
    private float verticalOffset;

    void newLineAtOffset(float x, float y) throws IOException {
        if (contents != null) {
            contents.newLineAtOffset(x, y);
            return;
        }
        positionList.add(new Float[]{x, y});
    }

    void showText(String text) throws IOException {
        if (contents != null) {
            contents.showText(text);
            return;
        }
        textList.add(text);
    }

    public List<Float[]> getPositionList() {
        return positionList;
    }

    public List<String> getTextList() {
        return textList;
    }

    public PDRectangle getClipRect() {
        return clipRect;
    }

    static class Builder {

        // required parameters
        private final PDAppearanceContentStream contents;

        private final List<Float[]> positionList;
        private final List<String> textList;

        // optional parameters
        private AppearanceStyle appearanceStyle;
        private boolean wrapLines = false;
        private float width = 0f;
        private PDRectangle clipRect;
        private PlainText textContent;
        private PlainTextFormatter.TextAlign textAlignment = PlainTextFormatter.TextAlign.LEFT;


        // initial offset from where to start the position of the first line
        private float horizontalOffset = 0f;
        private float verticalOffset = 0f;

        Builder() {
            this.contents = null;
            this.positionList = new ArrayList<>();
            this.textList = new ArrayList<>();
        }

        Builder(PDAppearanceContentStream contents) {
            this.contents = contents;
            this.positionList = new ArrayList<>();
            this.textList = new ArrayList<>();
        }

        PlainTextFormatter.Builder style(AppearanceStyle appearanceStyle) {
            this.appearanceStyle = appearanceStyle;
            return this;
        }

        PlainTextFormatter.Builder wrapLines(boolean wrapLines) {
            this.wrapLines = wrapLines;
            return this;
        }

        PlainTextFormatter.Builder width(float width) {
            this.width = width;
            return this;
        }

        PlainTextFormatter.Builder clipRect(PDRectangle clipRect) {
            this.clipRect = clipRect;
            return this;
        }

        PlainTextFormatter.Builder textAlign(int alignment) {
            this.textAlignment = PlainTextFormatter.TextAlign.valueOf(alignment);
            return this;
        }

        PlainTextFormatter.Builder textAlign(
            PlainTextFormatter.TextAlign alignment) {
            this.textAlignment = alignment;
            return this;
        }


        PlainTextFormatter.Builder text(
            PlainText textContent) {
            this.textContent = textContent;
            return this;
        }

        PlainTextFormatter.Builder initialOffset(float horizontalOffset, float verticalOffset) {
            this.horizontalOffset = horizontalOffset;
            this.verticalOffset = verticalOffset;
            return this;
        }

        PlainTextFormatter build() {
            return new PlainTextFormatter(this);
        }
    }

    private PlainTextFormatter(PlainTextFormatter.Builder builder) {
        appearanceStyle = builder.appearanceStyle;
        wrapLines = builder.wrapLines;
        width = builder.width;
        contents = builder.contents;
        positionList = builder.positionList;
        textList = builder.textList;
        clipRect = builder.clipRect;
        textContent = builder.textContent;
        textAlignment = builder.textAlignment;
        horizontalOffset = builder.horizontalOffset;
        verticalOffset = builder.verticalOffset;
    }

    /**
     * Format the text block.
     *
     * @throws IOException if there is an error writing to the stream.
     */
    public void format() throws IOException {
        if (textContent != null && !textContent.getParagraphs().isEmpty()) {
            boolean isFirstParagraph = true;
            for (PlainText.Paragraph paragraph : textContent.getParagraphs()) {
                if (wrapLines) {
                    List<PlainText.Line> lines = paragraph.getLines(
                        appearanceStyle.getFont(),
                        appearanceStyle.getFontSize(),
                        width
                    );
                    processLines(lines, isFirstParagraph);
                    isFirstParagraph = false;
                } else {
                    float startOffset = 0f;

                    float lineWidth = appearanceStyle.getFont().getStringWidth(paragraph.getText()) *
                        appearanceStyle.getFontSize() / FONTSCALE;

                    if (lineWidth < width) {
                        switch (textAlignment) {
                            case CENTER:
                                startOffset = (width - lineWidth) / 2;
                                break;
                            case RIGHT:
                                startOffset = width - lineWidth;
                                break;
                            case JUSTIFY:
                            default:
                                startOffset = 0f;
                        }
                    }

                    this.newLineAtOffset(horizontalOffset + startOffset, verticalOffset);
                    this.showText(paragraph.getText());
                }
            }
        }
    }

    /**
     * Process lines for output.
     * <p>
     * Process lines for an individual paragraph and generate the commands for the content stream to show the text.
     *
     * @param lines the lines to process.
     * @throws IOException if there is an error writing to the stream.
     */
    private void processLines(List<PlainText.Line> lines, boolean isFirstParagraph) throws IOException {
        float wordWidth;

        float lastPos = 0f;
        float startOffset = 0f;
        float interWordSpacing = 0f;

        for (PlainText.Line line : lines) {
            switch (textAlignment) {
                case CENTER:
                    startOffset = (width - line.getWidth()) / 2;
                    break;
                case RIGHT:
                    startOffset = width - line.getWidth();
                    break;
                case JUSTIFY:
                    if (lines.indexOf(line) != lines.size() - 1) {
                        interWordSpacing = line.getInterWordSpacing(width);
                    }
                    break;
                default:
                    startOffset = 0f;
            }

            float offset = -lastPos + startOffset + horizontalOffset;

            if (lines.indexOf(line) == 0 && isFirstParagraph) {
                this.newLineAtOffset(offset, verticalOffset);
            } else {
                // keep the last position
                verticalOffset = verticalOffset - appearanceStyle.getLeading();
                this.newLineAtOffset(offset, -appearanceStyle.getLeading());
            }

            lastPos += offset;

            List<PlainText.Word> words = line.getWords();
            int wordIndex = 0;
            for (PlainText.Word word : words) {
                this.showText(word.getText());
                wordWidth = (Float) word.getAttributes().getIterator().getAttribute(PlainText.TextAttribute.WIDTH);
                if (wordIndex != words.size() - 1) {
                    this.newLineAtOffset(wordWidth + interWordSpacing, 0f);
                    lastPos = lastPos + wordWidth + interWordSpacing;
                }
                ++wordIndex;
            }
        }
        horizontalOffset = horizontalOffset - lastPos;
    }
}
