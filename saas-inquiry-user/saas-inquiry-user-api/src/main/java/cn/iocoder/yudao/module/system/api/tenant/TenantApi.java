package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.util.List;

/**
 * 多门店的 API 接口
 *
 * <AUTHOR>
 */
public interface TenantApi {

    /**
     * 获得所有门店
     *
     * @return 门店编号数组
     */
//    List<Long> getTenantIdList();

    /**
     * 获取门店信息 对外暴露，根据header中tenantId获取
     *
     * @return 门店DTO
     */
    TenantDto getTenant();


    /**
     * 获取门店信息对外暴露
     *
     * @return 门店DTO
     */
    TenantDto getTenant(Long tenantId);

    /**
     * 批量获取门店信息对外暴露
     *
     * @return 门店DTO
     */
    List<TenantDto> getTenantList(List<Long> tenantId);

    /**
     * 根据门店名称或者编号等搜索门店列表
     *
     * @param nameOrPref
     * @return
     */
    List<TenantDto> getTenantList(String nameOrPref);


    PageResult<TenantRespDto> pageTenant(TenantReqDto tenantReqDto);

    /**
     * 校验门店是否合法
     *
     * @param id 门店编号
     */
    void validateTenant(Long id);


}
