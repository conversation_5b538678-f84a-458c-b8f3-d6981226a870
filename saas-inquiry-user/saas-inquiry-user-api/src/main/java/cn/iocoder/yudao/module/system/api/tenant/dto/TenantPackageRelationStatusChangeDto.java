package cn.iocoder.yudao.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/09/27 17:26
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenantPackageRelationStatusChangeDto implements Serializable {

    @Schema(description = "原因", example = "用户退款")
    @Length(max = 256, message = "原因最大长度为 {value}")
    private String remark;

    @Schema(description = "退款类型", example = "1")
    private Integer refundType;

    @Schema(description = "退款价格", example = "99")
    private BigDecimal refundPrice;


}
