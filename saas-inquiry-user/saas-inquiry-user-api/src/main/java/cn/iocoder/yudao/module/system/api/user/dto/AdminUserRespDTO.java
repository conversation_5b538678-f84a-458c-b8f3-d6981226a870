package cn.iocoder.yudao.module.system.api.user.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * Admin 用户 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AdminUserRespDTO implements Serializable {

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 姓名
     */
    private String nickname;
    /**
     * 帐号状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 岗位编号数组
     */
    private Set<Long> postIds;

    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 用户头像
     */
    private String avatar;


    /**
     * 用户性别，参见 SexEnum 枚举类
     */
    private Integer sex;

    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 用户角色codes
     */
    private List<String> roleCodes;
    /**
     * 用户角色id
     */
    private List<Long> roleIds;

    /**
     * 用户角色name
     */
    private List<String> roleNames;

    /**
     * 原系统用户guid
     */
    private String guid;

    @Schema(description = "门店用户关系id")
    private Long tenantUserRelationId;

    /**
     * 门店关联状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer relationStatus;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer needClockIn;

    /**
     * 是否打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer clockInStatus;

}
