package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserClockInDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationBindDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationUpdateVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantUserRelationConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationService;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.user.UserAccountStatusEnum;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * 多门店的 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class TenantUserRelationApiImpl implements TenantUserRelationApi {

    @Resource
    private TenantUserRelationService tenantUserRelationService;

    @Resource
    private TenantService tenantService;


    @Override
    public List<TenantUserRelationDto> getTenantUserRelationsByRoleCode(RoleCodeEnum roleCodeEnum) {
        return tenantUserRelationService.getTenantUserRelationsByRoleCode(roleCodeEnum);
    }

    @Override
    public List<TenantUserRelationDto> getUserTenantRelationsByRoleCode(RoleCodeEnum roleCodeEnum) {
        return tenantUserRelationService.getUserTenantRelationsByRoleCode(roleCodeEnum);
    }

    public List<TenantUserRelationDto> getAvailableTenantListByUserId(Long userId) {
        List<TenantUserRelationDO> tenantUserRelationDOS = tenantUserRelationService.getAvailableTenantListByUserId(userId);
        return TenantUserRelationConvert.INSTANCE.convertDo2Dto(tenantUserRelationDOS);
    }

    @Override
    public TenantUserRelationDto getAvailableTenantUserRelation(Long userId, Long tenantId) {
        TenantUserRelationDO tenantUserRelation = tenantUserRelationService.getTenantUserRelation(userId, tenantId);
        if (tenantUserRelation == null || !Objects.equals(tenantUserRelation.getStatus(), UserAccountStatusEnum.ENABLE.getCode())) {
            return null;
        }
        TenantDO tenant = tenantService.getTenant(tenantUserRelation.getTenantId());
        if (tenant == null || CommonStatusEnum.isDisable(tenant.getStatus())) {
            return null;
        }
        return TenantUserRelationConvert.INSTANCE.convertDto(tenantUserRelation, tenant);
    }

    @Override
    public void reBindNewTenantUserRelation(TenantUserRelationBindDto bindDto) {
        TenantUserRelationUpdateVO bindVO = TenantUserRelationConvert.INSTANCE.convert(bindDto);
        tenantUserRelationService.reBindNewTenantUserRelation(bindVO);
    }


    @Override
    public void tenantUserClockIn(TenantUserClockInDto clockInDto) {
        tenantUserRelationService.tenantUserClockIn(clockInDto);
    }
}
