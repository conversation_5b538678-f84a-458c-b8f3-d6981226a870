package cn.iocoder.yudao.module.system.api.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_MOBILE_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.convert.user.UserConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * Admin 用户 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Primary
public class AdminUserApiImpl implements AdminUserApi {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private TenantUserRelationService tenantUserRelationService;
    @Resource
    @Lazy
    private PermissionService permissionService;


    @Override
    public AdminUserRespDTO getUser() {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return getUser(loginUserId);
    }

    @Override
    public AdminUserRespDTO getUser(Long id) {
        AdminUserDO user = userService.getUserStore(id);
        List<RoleDO> roleDOS = permissionService.getRoleByUser(id);
        return UserConvert.INSTANCE.convertDto(user, roleDOS);
    }

    @Override
    public AdminUserRespDTO getUserBaseInfo(Long id) {
        AdminUserDO user = userService.getUserById(id);
        return UserConvert.INSTANCE.convertDto(user);
    }

    @Override
    public AdminUserRespDTO getUserByMobile(String mobile) {
        AdminUserDO user = userService.getUserByMobileSystem(mobile);
        return UserConvert.INSTANCE.convertDto(user);
    }

    @Override
    public void checkUserMobileCanCreateOnTenant(String mobile) {
        AdminUserDO user = userService.getUserByMobileSystem(mobile);
        if (user == null) {
            return;
        }
        TenantUserRelationDO userRelation = tenantUserRelationService.getTenantUserRelation(user.getId(), TenantContextHolder.getRequiredTenantId());
        if (userRelation != null) {
            return;
        }
        throw exception(USER_MOBILE_EXISTS, mobile);
    }

    @Override
    public Long saveOrUpdateUserByMobile(AdminUserSaveDTO userSaveDTO) {
        UserSaveReqVO userSaveReqVO = UserConvert.INSTANCE.convertSaveOrUpdateVo(userSaveDTO);
        userSaveReqVO.setId(null); // 根据手机号控制，id置空
        userSaveReqVO.setUsername(StringUtils.defaultIfBlank(userSaveDTO.getUsername(), userSaveDTO.getMobile()));
        // 如果手机号为空,创建一个
        if (StringUtils.isBlank(userSaveDTO.getMobile())) {
            return userService.createUserStore(userSaveReqVO);
        }
        AdminUserRespDTO userByMobile = getUserByMobile(userSaveReqVO.getMobile());
        if (userByMobile == null) {
            return userService.createUserStore(userSaveReqVO);
        }
        userService.updateUserStore(userSaveReqVO.setId(userByMobile.getId()));
        return userByMobile.getId();
    }

    @Override
    public Long updateUser(AdminUserSaveDTO userSaveDTO) {
        AdminUserDO userStore = userService.getUserStore(userSaveDTO.getId());

        UserSaveReqVO userSaveReqVO = UserConvert.INSTANCE.convertSaveOrUpdateVo(userSaveDTO);
        userService.updateUserStore(userSaveReqVO.setId(userStore.getId()));

        return userStore.getId();
    }

    @Override
    public List<AdminUserRespDTO> getUserListBySubordinate(Long id) {
        // 1.1 获取用户负责的部门
        AdminUserDO user = userService.getUserStore(id);
        if (user == null) {
            return Collections.emptyList();
        }
        ArrayList<Long> deptIds = new ArrayList<>();
        DeptDO dept = deptService.getDept(user.getDeptId());
        if (dept == null) {
            return Collections.emptyList();
        }
        if (ObjUtil.notEqual(dept.getLeaderUserId(), id)) { // 校验为负责人
            return Collections.emptyList();
        }
        deptIds.add(dept.getId());
        // 1.2 获取所有子部门
        List<DeptDO> childDeptList = deptService.getChildDeptList(dept.getId());
        if (CollUtil.isNotEmpty(childDeptList)) {
            deptIds.addAll(convertSet(childDeptList, DeptDO::getId));
        }

        // 2. 获取部门对应的用户信息
        List<AdminUserDO> users = userService.getUserListByDeptIds(deptIds);
        users.removeIf(item -> ObjUtil.equal(item.getId(), id)); // 排除自己
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public List<AdminUserRespDTO> getUserList(Collection<Long> ids) {
        List<AdminUserDO> users = userService.getUserList(ids);
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public List<AdminUserRespDTO> getUserListByDeptIds(Collection<Long> deptIds) {
        List<AdminUserDO> users = userService.getUserListByDeptIds(deptIds);
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public List<AdminUserRespDTO> getUserListByPostIds(Collection<Long> postIds) {
        List<AdminUserDO> users = userService.getUserListByPostIds(postIds);
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public void validateUserList(Collection<Long> ids) {
        userService.validateUserList(ids);
    }

    @Override
    public void resetUserGuid(Long userId) {
        userService.resetUserGuid(userId);
    }

    @Override
    public void deleteUserSystem(Long id) {
        userService.deleteUserSystem(id);
    }

    @Override
    public List<AdminUserRespDTO> getUserListByRoleCodes(List<String> roleCodes) {
        List<AdminUserDO> users = userService.getUserListByRoleCodes(roleCodes);
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public AdminUserRespDTO getTenantUser() {
        return userService.getTenantUser();
    }

    @Override
    public List<AdminUserRespDTO> getUserListByLikeNickName(String nickname) {
        if (StringUtils.isBlank(nickname)) {
            return Collections.emptyList();
        }
        List<AdminUserDO> users = userService.getUserListByNickname(nickname);
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public void logout(Long userId) {
        userService.logout(userId, CommonStatusEnum.DISABLE.getStatus());
    }
}
