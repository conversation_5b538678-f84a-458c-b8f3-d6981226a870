package cn.iocoder.yudao.module.system.controller.admin.auth;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.config.SecurityProperties;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.*;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import cn.iocoder.yudao.module.system.service.permission.MenuPermissionService;
import com.xyy.saas.inquiry.enums.system.ClientTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 认证")
@RestController
@RequestMapping(value = {"/admin-api/system/auth", "/app-api/system/auth"})
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;

    @Resource
    private MenuPermissionService menuPermissionService;

    @Resource
    private SecurityProperties securityProperties;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/login-confirm-with-tenant-id")
    @Operation(summary = "多门店登录完用户名和密码后,选择门店id进入系统")
    public CommonResult<AuthLoginRespVO> loginWithTenantId(@RequestBody @Valid AuthLoginTenantReqVO reqVO, HttpServletRequest request) {
        reqVO.setToken(SecurityFrameworkUtils.obtainAuthorization(request,
            securityProperties.getTokenHeader(), securityProperties.getTokenParameter()));
        return success(authService.loginWithTenantId(reqVO));
    }


    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
            securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/logoff")
    @PermitAll
    @Operation(summary = "注销账号")
    public CommonResult<Boolean> logoff(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
            securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logoff(token);
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    @GetMapping("/get-permission-info")
    @Operation(summary = "获取登录用户的权限信息 app")
    @Parameter(name = "client", description = "端标识 0-app,1-web")
    public CommonResult<AuthPermissionInfoRespVo> getPermissionInfo(@RequestParam(required = false) Integer client) {
        return success(menuPermissionService.getMenuPermissionInfo(ClientTypeEnum.fromCode(client)));
    }

    // ========== 短信登录相关 ==========

    @PostMapping("/sms-login")
    @PermitAll
    @Operation(summary = "使用短信验证码登录")
    public CommonResult<AuthLoginRespVO> smsLogin(@RequestBody @Valid AuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    @PostMapping("/send-sms-code")
    @PermitAll
    @Operation(summary = "发送手机验证码")
    public CommonResult<Boolean> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        authService.sendSmsCode(reqVO);
        return success(true);
    }

    // ========== OA登录相关 ==========

    @PostMapping("/OA-login")
    @PermitAll
    @Operation(summary = "使用OA账号密码登录")
    public CommonResult<AuthLoginRespVO> OALogin(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.OALogin(reqVO));
    }

    // ========== 社交登录相关 ==========

//    @GetMapping("/social-auth-redirect")
//    @PermitAll
//    @Operation(summary = "社交授权的跳转")
//    @Parameters({
//            @Parameter(name = "type", description = "社交类型", required = true),
//            @Parameter(name = "redirectUri", description = "回调路径")
//    })
//    public CommonResult<String> socialLogin(@RequestParam("type") Integer type,
//                                            @RequestParam("redirectUri") String redirectUri) {
//        return success(socialClientService.getAuthorizeUrl(
//                type, UserTypeEnum.ADMIN.getValue(), redirectUri));
//    }

//    @PostMapping("/social-login")
//    @PermitAll
//    @Operation(summary = "社交快捷登录，使用 code 授权码", description = "适合未登录的用户，但是社交账号已绑定用户")
//    public CommonResult<AuthLoginRespVO> socialQuickLogin(@RequestBody @Valid AuthSocialLoginReqVO reqVO) {
//        return success(authService.socialLogin(reqVO));
//    }


    // ========== 三方接口授权登录相关 ==========

    @PostMapping("/api-login")
    @PermitAll
    @Operation(summary = "使用三方应用授权登录")
    public CommonResult<AuthLoginRespVO> apiLogin(@RequestBody @Valid AuthLoginApiReqVO reqVO) {
        return success(authService.apiLogin(reqVO));
    }
}
