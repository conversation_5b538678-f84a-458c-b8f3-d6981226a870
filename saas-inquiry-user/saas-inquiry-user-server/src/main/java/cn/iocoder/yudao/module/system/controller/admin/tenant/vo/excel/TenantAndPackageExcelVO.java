package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import com.xyy.saas.inquiry.util.excel.validator.DictValid;
import com.xyy.saas.inquiry.util.excel.validator.ValidDateFormat;
import com.xyy.saas.inquiry.util.excel.validator.ValidNumberFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.groups.Default;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.validator.constraints.Length;

/**
 * 批量开通编辑门店ExcelVO
 */
@Getter
public class TenantAndPackageExcelVO extends ImportExcelVoDto {


    @ExcelProperty(value = "门店编码", index = 0)
    @Size(max = 64, message = "门店编码超出最大长度64限制", groups = Default.class)
    private String pref;

    @ExcelProperty(value = "门店名", index = 1)
    @Size(max = 64, message = "门店名超出最大长度64限制", groups = Default.class)
    @NotBlank(message = "门店名不能为空", groups = AddTenantValid.class)
    private String name;

    @ExcelProperty(value = "手机号", index = 2)
    @Size(max = 11, message = "门店名超出最大长度11限制", groups = Default.class)
    @NotBlank(message = "手机号不能为空", groups = AddTenantValid.class)
    @Mobile(message = "手机号格式不正确", groups = Default.class)
    private String contactMobile;

    @ExcelProperty(value = "联系人", index = 3)
    @Size(max = 32, message = "联系人超出最大长度32限制", groups = Default.class)
    @NotBlank(message = "联系人不能为空", groups = AddTenantValid.class)
    private String contactName;

    @ExcelProperty(value = "状态", index = 4)
    @DictValid(dictType = DictTypeConstants.COMMON_STATUS, message = "无法找到对应的状态", groups = Default.class)
    @NotBlank(message = "状态不能为空", groups = AddTenantValid.class)
    private String status;

    @ExcelProperty(value = "门店类型", index = 5)
    @DictValid(dictType = DictTypeConstants.TENANT_TYPE, message = "无法找到对应的门店类型", groups = Default.class)
    @NotBlank(message = "门店类型不能为空", groups = AddTenantValid.class)
    private String type;

    @ExcelProperty(value = "连锁总部门店编码", index = 6)
    @Size(max = 64, message = "连锁总部门店编码超出最大长度64限制", groups = Default.class)
    private String headTenantPref;

    @ExcelProperty(value = "系统业务线", index = 7)
    @NotBlank(message = "系统业务线不能为空", groups = AddTenantValid.class)
    @Size(max = 64, message = "系统业务线超出最大长度10限制", groups = Default.class)
    private String bizType;

    @ExcelProperty(value = "省", index = 8)
    @Size(max = 16, message = "省超出最大长度16限制", groups = Default.class)
    @NotBlank(message = "省不能为空", groups = AddTenantValid.class)
    private String province;

    @ExcelProperty(value = "市", index = 9)
    @Size(max = 16, message = "市超出最大长度16限制", groups = Default.class)
    @NotBlank(message = "市不能为空", groups = AddTenantValid.class)
    private String city;

    @ExcelProperty(value = "区", index = 10)
    @Size(max = 16, message = "区超出最大长度16限制", groups = Default.class)
    @NotBlank(message = "区不能为空", groups = AddTenantValid.class)
    private String area;

    @ExcelProperty(value = "地址", index = 11)
    @Size(max = 255, message = "地址超出最大长度255限制", groups = Default.class)
    @NotBlank(message = "地址不能为空", groups = AddTenantValid.class)
    private String address;

    // 门店配置信息 ----------- start

    @ExcelProperty(value = "荷叶问诊服务", index = 12)
    @DictValid(dictType = DictTypeConstants.COMMON_STATUS, message = "无法找到对应的荷叶问诊服务", groups = Default.class)
    @NotBlank(message = "荷叶问诊服务不能为空", groups = AddTenantValid.class)
    private String inquiryService;

    @ExcelProperty(value = "处方日期格式", index = 13)
    @DictValid(dictType = DictTypeConstants.PRESCRIPTION_DATE_TYPE, message = "无法找到对应的处方日期格式", groups = Default.class)
    @NotBlank(message = "处方日期格式不能为空", groups = AddTenantValid.class)
    private String prescriptionDateFormat;

    @ExcelProperty(value = "审方类型", index = 14)
    @DictValid(dictType = DictTypeConstants.PRESCRIPTION_DRUGSTORE_AUDIT_TYPE, message = "无法找到对应的审方类型", groups = Default.class)
    @NotBlank(message = "审方类型不能为空", groups = AddTenantValid.class)
    private String reviewType;

    // 门店配置信息 ----------- end


    @ExcelProperty(value = "营业执照名称", index = 15)
    @Size(max = 64, message = "营业执照名称超出最大长度64限制", groups = Default.class)
    @NotBlank(message = "营业执照名称不能为空", groups = AddTenantValid.class)
    private String businessLicenseName;

    @ExcelProperty(value = "营业执照编号", index = 16)
    @Size(max = 64, message = "营业执照编号超出最大长度64限制", groups = Default.class)
    @NotBlank(message = "营业执照编号不能为空", groups = AddTenantValid.class)
    private String businessLicenseNumber;

    // 资质证件信息 ------- start

    @ExcelProperty(value = "药品经营许可证件编号", index = 17)
    @Size(max = 256, message = "药品经营许可证件编号超出最大长度256限制", groups = Default.class)
    private String drugBusinessLicenseNumber;

    @ExcelProperty(value = "药品经营许可注册发证日期", index = 18)
    @ValidDateFormat(message = "药品经营许可注册发证日期格式错误，应为 yyyy-MM-dd", groups = Default.class)
    private String drugBusinessRegistrationDate;

    @ExcelProperty(value = "药品经营许可有效期", index = 19)
    @ValidDateFormat(message = "药品经营许可有效期格式错误，应为 yyyy-MM-dd", groups = Default.class)
    private String drugBusinessLicenseExpiry;

    @ExcelProperty(value = "药品经营质量管理证件编号", index = 20)
    @Size(max = 256, message = "药品经营质量管理证件编号超出最大长度256限制", groups = Default.class)
    private String gspLicenseNumber;

    // 资质证件信息 ------- end

    // 套餐包信息 ------- start

    @ExcelProperty(value = "套餐包编码", index = 21)
    @Size(max = 64, message = "套餐包编码超出最大长度64限制", groups = Default.class)
    private String packagePref;

    @ExcelProperty(value = "服务包性质", index = 22)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_NATURE, message = "无法找到对应的服务包性质", groups = Default.class)
    @NotBlank(message = "服务包性质不能为空", groups = AddPackageValid.class)
    private String packageNature;

    @ExcelProperty(value = "收款方式", index = 23)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_PAYMENT_TYPE, message = "无法找到对应的收款方式", groups = Default.class)
    @NotBlank(message = "收款方式不能为空", groups = AddPackageValid.class)
    private String paymentType;

    @ExcelProperty(value = "实收金额", index = 24)
    @ValidNumberFormat(message = "实收金额不正确,请填写0-9999999.99的数字类型(保留两位小数)", groups = Default.class)
    private String actualAmount;

    @ExcelProperty(value = "收款账户", index = 25)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_COLLECT_ACCOUNT, message = "无法找到对应的收款账户", groups = Default.class)
    private String collectAccount;

    @ExcelProperty(value = "付款流水号", index = 26)
    @Size(max = 256, message = "付款流水号超出最大长度256限制", groups = Default.class)
    private String payNo;

    @ExcelProperty(value = "服务生效时间", index = 27)
    @ValidDateFormat(message = "服务生效时间格式错误，应为 yyyy-MM-dd", groups = Default.class)
    @NotBlank(message = "服务生效时间不能为空", groups = AddPackageValid.class)
    private String startTime;

    @ExcelProperty(value = "服务失效时间", index = 28)
    @ValidDateFormat(message = "服务失效时间格式错误，应为 yyyy-MM-dd", groups = Default.class)
    @NotBlank(message = "服务失效时间不能为空", groups = AddPackageValid.class)
    private String endTime;

    @ExcelProperty(value = "签约日期", index = 29)
    @ValidDateFormat(message = "签约日期格式错误，应为 yyyy-MM-dd", groups = Default.class)
    @NotBlank(message = "签约日期不能为空", groups = AddPackageValid.class)
    private String signTime;

    @ExcelProperty(value = "签约渠道", index = 30)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_SIGN_CHANNEL, message = "无法找到对应的签约渠道", groups = Default.class)
    @NotBlank(message = "签约渠道不能为空", groups = AddPackageValid.class)
    private String signChannel;

    @ExcelProperty(value = "签约人", index = 31)
    @Size(max = 64, message = "签约人超出最大长度64限制", groups = Default.class)
    private String signUser;

    @ExcelProperty(value = "代理人", index = 32)
    @Size(max = 64, message = "代理人超出最大长度64限制", groups = Default.class)
    private String proxyUser;

    @ExcelProperty(value = "备注", index = 33)
    @Length(max = 256, message = "备注超出最大长度256限制", groups = Default.class)
    private String remark;

    // 新增门店校验
    public static interface AddTenantValid {

    }

    // 开通套餐校验
    public static interface AddPackageValid {

    }


    @Override
    public void valid() {
        // 新增门店校验
        if (StringUtils.isBlank(pref)) {
            EasyExcelUtil.checkAndValidatorImportData(this, AddTenantValid.class);
        }
        if (StringUtils.equals(type, TenantTypeEnum.CHAIN_STORE.getCode() + "") && StringUtils.isBlank(headTenantPref)) {
            this.errMsg += "连锁门店需要填写连锁总部门店编码,";
        }

        if (type() != null && !Objects.equals(type(), TenantTypeEnum.CHAIN_STORE.getCode()) && StringUtils.isNotBlank(headTenantPref)) {
            this.errMsg += "非连锁门店不需要填写连锁总部门店编码,";
        }

        if (StringUtils.isNotBlank(getBizType())) {
            if (Arrays.stream(StringUtils.split(getBizType(), ","))
                .anyMatch(s -> !StringUtils.equals(s, BizTypeEnum.HYWZ.getCode() + "")
                    && !StringUtils.equals(s, BizTypeEnum.ZHL.getCode() + ""))) {
                this.errMsg += "无法找到对应的系统业务线,";
            }
        }

        // 转换省市区
        if (StringUtils.isNotBlank(getProvince()) && AreaUtils.parseArea(getProvince()) == null) {
            this.errMsg += "省不存在,";
        }
        if (StringUtils.isNotBlank(getCity()) && AreaUtils.parseArea(getCity()) == null) {
            this.errMsg += "市不存在,";
        }
        if (StringUtils.isNotBlank(getArea()) && AreaUtils.parseArea(getArea()) == null) {
            this.errMsg += "区不存在,";
        }
        // 新增套餐校验
        if (StringUtils.isNotBlank(packagePref)) {
            EasyExcelUtil.checkAndValidatorImportData(this, AddPackageValid.class);
            if (localStartTime() != null && localEndTime() != null && localEndTime().isBefore(localStartTime())) {
                this.errMsg += "服务失效时间不能早于服务生效时间,";
            }
        }
    }


    public LocalDateTime localEndTime() {
        if (StringUtils.isBlank(endTime)) {
            return null;
        }
        try {
            return LocalDate.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atTime(23, 59, 59);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    public LocalDateTime localStartTime() {
        if (StringUtils.isBlank(startTime)) {
            return null;
        }
        try {
            return LocalDate.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    public LocalDateTime localSignTime() {
        if (StringUtils.isBlank(signTime)) {
            return null;
        }
        try {
            return LocalDate.parse(signTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    public Integer status() {
        return NumberUtils.isDigits(status) ? NumberUtils.toInt(status) : null;
    }

    public Integer type() {
        return NumberUtils.isDigits(type) ? NumberUtils.toInt(type) : null;
    }


    public LocalDateTime drugBusinessRegistrationDate() {
        return StringUtils.isBlank(drugBusinessRegistrationDate) ? null : LocalDate.parse(drugBusinessRegistrationDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
    }

    public LocalDateTime drugBusinessLicenseExpiry() {
        return StringUtils.isBlank(drugBusinessLicenseExpiry) ? null : LocalDate.parse(drugBusinessLicenseExpiry, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
    }


    public BigDecimal actualAmount() {
        if (StringUtils.isNotBlank(actualAmount)) {
            try {
                return new BigDecimal(actualAmount);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    public void setErrMsg(String errMsg) {
        super.setErrMsg(errMsg);
    }


    public void setPref(String pref) {
        this.pref = pref;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setInquiryService(String inquiryService) {
        this.inquiryService = inquiryService;
    }

    public void setPrescriptionDateFormat(String prescriptionDateFormat) {
        this.prescriptionDateFormat = prescriptionDateFormat;
    }

    public void setReviewType(String reviewType) {
        this.reviewType = reviewType;
    }

    public void setBusinessLicenseName(String businessLicenseName) {
        this.businessLicenseName = businessLicenseName;
    }

    public void setBusinessLicenseNumber(String businessLicenseNumber) {
        this.businessLicenseNumber = businessLicenseNumber;
    }

    public void setDrugBusinessLicenseNumber(String drugBusinessLicenseNumber) {
        this.drugBusinessLicenseNumber = drugBusinessLicenseNumber;
    }

    public void setHeadTenantPref(String headTenantPref) {
        this.headTenantPref = headTenantPref;
    }

    public void setDrugBusinessRegistrationDate(String drugBusinessRegistrationDate) {
        this.drugBusinessRegistrationDate = drugBusinessRegistrationDate;
    }

    public void setDrugBusinessLicenseExpiry(String drugBusinessLicenseExpiry) {
        this.drugBusinessLicenseExpiry = drugBusinessLicenseExpiry;
    }

    public void setGspLicenseNumber(String gspLicenseNumber) {
        this.gspLicenseNumber = gspLicenseNumber;
    }

    public void setPackagePref(String packagePref) {
        this.packagePref = packagePref;
    }

    public void setPackageNature(String packageNature) {
        this.packageNature = packageNature;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public void setActualAmount(String actualAmount) {
        this.actualAmount = actualAmount;
    }

    public void setCollectAccount(String collectAccount) {
        this.collectAccount = collectAccount;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    public void setSignChannel(String signChannel) {
        this.signChannel = signChannel;
    }

    public void setSignUser(String signUser) {
        this.signUser = signUser;
    }

    public void setProxyUser(String proxyUser) {
        this.proxyUser = proxyUser;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
