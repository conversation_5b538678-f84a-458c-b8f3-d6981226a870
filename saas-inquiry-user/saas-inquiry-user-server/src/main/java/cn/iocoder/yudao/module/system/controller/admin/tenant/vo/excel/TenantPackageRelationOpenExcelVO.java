package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel;

import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import com.xyy.saas.inquiry.util.excel.validator.DictValid;
import com.xyy.saas.inquiry.util.excel.validator.ValidDateFormat;
import com.xyy.saas.inquiry.util.excel.validator.ValidNumberFormat;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import jakarta.validation.groups.Default;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 批量续费套餐ExcelVO
 */
@Getter
public class TenantPackageRelationOpenExcelVO extends ImportExcelVoDto {

    @ExcelProperty(value = "门店编码", index = 0)
    @NotBlank(message = "门店编码不能为空")
    @Size(max = 64, message = "门店编码超出最大长度64限制")
    private String tenantPref;

    @ExcelProperty(value = "套餐包编码", index = 1)
    @NotBlank(message = "套餐包编码不能为空")
    @Size(max = 64, message = "套餐包编码超出最大长度64限制")
    private String packagePref;

    @ExcelProperty(value = "服务包性质", index = 2)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_NATURE, message = "无法找到对应的服务包性质")
    @NotNull(message = "服务包性质不能为空")
    private String packageNature;

    @ExcelProperty(value = "收款方式", index = 3)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_PAYMENT_TYPE, message = "无法找到对应的收款方式")
    @NotNull(message = "收款方式不能为空")
    private String paymentType;

    @ExcelProperty(value = "实收金额", index = 4)
    @ValidNumberFormat(message = "实收金额不正确,请填写0-9999999.99的数字类型(保留两位小数)")
    private String actualAmount;

    @ExcelProperty(value = "收款账户", index = 5)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_COLLECT_ACCOUNT, message = "无法找到对应的收款账户")
    private String collectAccount;

    @ExcelProperty(value = "付款流水号", index = 6)
    @Size(max = 256, message = "付款流水号超出最大长度256限制")
    private String payNo;

    @ExcelProperty(value = "服务生效时间", index = 7)
    @ValidDateFormat(message = "服务生效时间格式错误，应为 yyyy-MM-dd")
    @NotBlank(message = "服务生效时间不能为空")
    private String startTime;

    @ExcelProperty(value = "服务失效时间", index = 8)
    @ValidDateFormat(message = "服务失效时间格式错误，应为 yyyy-MM-dd")
    @NotBlank(message = "服务失效时间不能为空")
    private String endTime;

    @ExcelProperty(value = "签约日期", index = 9)
    @ValidDateFormat(message = "签约日期格式错误，应为 yyyy-MM-dd")
    @NotBlank(message = "签约日期不能为空")
    private String signTime;

    @ExcelProperty(value = "签约渠道", index = 10)
    @DictValid(dictType = DictTypeConstants.TENANT_PACKAGE_RELATION_SIGN_CHANNEL, message = "无法找到对应的签约渠道")
    @NotBlank(message = "签约渠道不能为空")
    private String signChannel;

    @ExcelProperty(value = "签约人", index = 11)
    @Size(max = 64, message = "签约人超出最大长度64限制")
    private String signUser;

    @ExcelProperty(value = "代理人", index = 12)
    @Size(max = 64, message = "代理人超出最大长度64限制")
    private String proxyUser;

    @ExcelProperty(value = "备注", index = 13)
    @Size(max = 256, message = "备注超出最大长度256限制")
    private String remark;

    /**
     * 校验当前对象基础字段信息
     */
    public void valid() {
        if (localStartTime() != null && localEndTime() != null && localEndTime().isBefore(localStartTime())) {
            this.errMsg += "服务失效时间不能早于服务生效时间;";
        }
    }

    public LocalDateTime localEndTime() {
        if (StringUtils.isBlank(endTime)) {
            return null;
        }
        try {
            return LocalDate.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atTime(23, 59, 59);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    public LocalDateTime localStartTime() {
        if (StringUtils.isBlank(startTime)) {
            return null;
        }
        try {
            return LocalDate.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    public LocalDateTime localSignTime() {
        if (StringUtils.isBlank(signTime)) {
            return null;
        }
        try {
            return LocalDate.parse(signTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    public BigDecimal actualAmount() {
        if (StringUtils.isNotBlank(actualAmount)) {
            try {
                return new BigDecimal(actualAmount);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }


    public void setTenantPref(String tenantPref) {
        this.tenantPref = tenantPref;
    }

    public void setPackagePref(String packagePref) {
        this.packagePref = packagePref;
    }

    public void setPackageNature(String packageNature) {
        this.packageNature = packageNature;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public void setActualAmount(String actualAmount) {
        this.actualAmount = actualAmount;
    }

    public void setCollectAccount(String collectAccount) {
        this.collectAccount = collectAccount;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    public void setSignChannel(String signChannel) {
        this.signChannel = signChannel;
    }

    public void setSignUser(String signUser) {
        this.signUser = signUser;
    }

    public void setProxyUser(String proxyUser) {
        this.proxyUser = proxyUser;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setErrMsg(String errMsg) {
        super.setErrMsg(errMsg);
    }
}
