package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationStatusChangeDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.PackageNatureEnum;
import com.xyy.saas.inquiry.enums.tenant.PackagePaymentTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 门店套餐订单新增/修改 Request VO")
@Data
@Accessors(chain = true)
public class TenantPackageRelationSaveReqVO {

    @Schema(description = "门店套餐关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13351")
    private Long id;

    @Schema(description = "编码", example = "xx100001")
    private String pref;

    @Schema(description = "门店id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14967")
    @NotNull
    private Long tenantId;

    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bizType;

    @Schema(description = "门店套餐编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14967")
    @NotNull(message = "门店套餐编号不能为空")
    private Long packageId;

    /**
     * 套餐包编码
     */
    private String packagePref;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String packageName;

    /**
     * 问诊医院id
     */
    @Schema(description = "问诊医院id", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private List<String> hospitalPrefs;

    @Schema(description = "问诊业务类型", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    private Integer inquiryAuditType;

    /**
     * 套餐定价
     */
    @Schema(description = "套餐定价", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private BigDecimal price;

    /**
     * 问诊方式类型 {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private List<Integer> inquiryWayTypes;

    /**
     * 问诊包信息
     */
    @Schema(description = "问诊包信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private List<InquiryPackageItem> inquiryPackageItems;


    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    @Schema(description = "开通状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @DiffLogField(name = "服务开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @DiffLogField(name = "服务结束时间")
    private LocalDateTime endTime;

    @Schema(description = "套餐包性质：0赠送 1购买 2体验", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(value = PackageNatureEnum.class, message = "套餐包性质必须是 {value}")
    @DiffLogField(name = "套餐包性质")
    private Integer packageNature;

    @Schema(description = "收款方式：0线上，1线下", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "收款方式：0线上，1线下不能为空")
    @InEnum(value = PackagePaymentTypeEnum.class, message = "收款方式必须是 {value}")
    @DiffLogField(name = "收款方式")
    private Integer paymentType;

    @Schema(description = "签约日期")
    @NotNull(message = "结束时间不能为空")
    @DiffLogField(name = "签约日期")
    private LocalDateTime signTime;

    @Schema(description = "签约人", requiredMode = Schema.RequiredMode.REQUIRED)
    // @NotEmpty(message = "签约人不能为空")
    @DiffLogField(name = "签约人")
    @Length(max = 64, message = "签约人最大长度为64")
    private String signUser;

    @Schema(description = "代理人/中间人", requiredMode = Schema.RequiredMode.REQUIRED)
    @DiffLogField(name = "代理人")
    @Length(max = 64, message = "代理人最大长度为64")
    private String proxyUser;

    @Schema(description = "签约渠道(eg:3智鹿)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "签约渠道(eg:3智鹿)不能为空")
    @DiffLogField(name = "签约渠道")
    private Integer signChannel;

    @Schema(description = "实收金额")
    @Max(value = ********, message = "实收金额最大为 9999999.99")
    @DiffLogField(name = "实收金额")
    private BigDecimal actualAmount;

    @Schema(description = "收款账户 eg:(微信对公-成都)", requiredMode = Schema.RequiredMode.REQUIRED, example = "31497")
    // @NotNull(message = "收款账户不能为空")
    @DiffLogField(name = "收款账户不能为空")
    private Integer collectAccount;

    @Schema(description = "付款流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @Length(max = 256, message = "付款流水号最大长度为256")
    @DiffLogField(name = "付款流水号")
    private String payNo;

    @Schema(description = "付款凭证url", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> payVoucherUrls;

    @Schema(description = "备注", example = "你猜")
    @Length(max = 256, message = "备注最大长度为256")
    private String remark;

    /**
     * 失效信息
     */
    private TenantPackageRelationStatusChangeDto statusChangeInfo;


    @AssertTrue(message = "问诊套餐基本信息不能为空")
    @JsonIgnore
    public boolean isRechargeCostValid() {
        if (!TenantPackageConstant.isRechargePackageId(getPackageId())) {
            return true;
        }
        return ObjectUtil.isAllNotEmpty(packageName, price, inquiryBizType, inquiryPackageItems); // 额度充值基本信息
    }


}