package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.framework.operatelog.core.CommonStatusParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.TenantParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.TenantTypeParseFunction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import com.xyy.saas.inquiry.constant.SystemConstant;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 门店创建/修改 Request VO")
@Data
@Accessors(chain = true)
public class TenantSaveReqVO {

    @Schema(description = "门店编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14411")
    private Long id;

//    @Schema(description = "门店编码", example = "MD100001")
//    private String pref;

    @Schema(description = "租户类型（1-单店 2连锁门店 3连锁总部）", example = "1")
    @DiffLogField(name = "门店类型", function = TenantTypeParseFunction.NAME)
    private Integer type;

    @Schema(description = "总部名称（总部）", example = "1")
    @DiffLogField(name = "总部名称", function = TenantParseFunction.NAME)
    private Long headTenantId;

    @Schema(description = "门店状态（0正常 1停用）", example = "1")
    @DiffLogField(name = "门店状态", function = CommonStatusParseFunction.NAME)
    private Integer status;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "问诊系统业务类型开通", example = "0")
    @DiffLogField(name = "问诊系统", function = CommonStatusParseFunction.NAME)
    private Integer wzBizTypeStatus;

    @Schema(description = "账号数量", example = "26469")
    @DiffLogField(name = "账号数量")
    private Integer wzAccountCount;


    @Schema(description = "智慧脸业务线类型开通", example = "2")
    private Integer zhlBizTypeStatus;

    @Schema(description = "智慧脸账号数量", example = "17503")
    private Integer zhlAccountCount;


    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "门店名不能为空")
    @Size(min = 4, max = 64, message = "门店名长度为 4-64 个字符")
    @DiffLogField(name = "门店名")
    private String name;

    @Schema(description = "联系人的用户id", example = "11333")
    private Long contactUserId;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "联系人不能为空")
    @Length(max = 32, message = "联系人最大长度不可超过32")
    @DiffLogField(name = "联系人")
    private String contactName;

    @Schema(description = "联系手机")
    @NotEmpty(message = "联系人手机不能为空")
    @Mobile(message = "联系人手机格式不正确")
    @DiffLogField(name = "联系手机")
    private String contactMobile;

    @Schema(description = "营业执照名称", example = "张三")
    @Length(max = 64, message = "营业执照名称最大长度不可超过64")
    @DiffLogField(name = "营业执照名称")
    private String businessLicenseName;

    @Schema(description = "营业执照号")
    @Length(max = 64, message = "营业执照号最大长度不可超过64")
    @DiffLogField(name = "营业执照号")
    private String businessLicenseNumber;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "省不能为空")
    @DiffLogField(name = "省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "市不能为空")
    @DiffLogField(name = "市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "区不能为空")
    @DiffLogField(name = "区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "地址不能为空")
    @Length(max = 256, message = "地址最大长度为 {value}")
    @DiffLogField(name = "地址")
    private String address;

    @Schema(description = "资质证件信息")
    private List<TenantCertificateSaveReqVO> certificates;

    @Schema(description = "问诊参数信息")
    private List<TenantParamConfigDto> inquiryParamConfigs;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据")
    private String envTag;

    @Schema(description = "租户拓展字段")
    private TenantExtDto ext;

    // ========== 仅【创建】时，需要传递的字段 ==========

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
//    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Pattern(regexp = SystemConstant.PASSWORD_PATTERN_REGEXP, message = SystemConstant.PASSWORD_PATTERN_REGEXP_MESSAGE)
    private String password;

    /**
     * 是否是导入
     */
    private boolean isImport;

    // @AssertTrue(message = "用户账号、密码不能为空")
    // @JsonIgnore
    // public boolean isUsernameValid() {
    //     return id != null // 修改时，不需要传递
    //         || (ObjectUtil.isAllNotEmpty(username, password)); // 新增时，必须都传递 username、password
    // }

    @AssertTrue(message = "系统业务类型不能为空")
    @JsonIgnore
    public boolean isBizTypeValid() {
        return !ObjectUtil.isAllEmpty(wzBizTypeStatus, zhlBizTypeStatus); // 两种类型都为空
    }

}
