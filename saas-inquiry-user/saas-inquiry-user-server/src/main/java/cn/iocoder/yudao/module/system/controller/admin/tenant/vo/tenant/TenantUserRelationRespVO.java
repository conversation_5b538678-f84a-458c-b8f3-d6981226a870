package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 门店用户关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantUserRelationRespVO extends PageParam {

    private Long id;

    @Schema(description = "userId", example = "111")
    private Long userId;

    @Schema(description = "编码", example = "111")
    private String pref;

    /**
     * {@link com.xyy.saas.inquiry.enums.user.UserStatusEnum}
     */
    @Schema(description = "员工状态", example = "1")
    private Integer status;
    /**
     * {@link com.xyy.saas.inquiry.enums.user.UserStatusEnum}
     */
    @Schema(description = "账号状态", example = "1")
    private Integer accountStatus;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否需要打卡 (0是 1否)")
    private Integer needClockIn;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("门店名")
    private String name;

    @Schema(description = "联系人的用户id", example = "11333")
    @ExcelProperty("联系人的用户id")
    private Long contactUserId;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("联系人")
    private String contactName;

    @Schema(description = "联系手机")
    @ExcelProperty("联系手机")
    private String contactMobile;

//    @Schema(description = "门店状态（0正常 1停用）", example = "1")
//    private Integer storeStatus;
    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "问诊系统业务类型开通", example = "0")
    private Integer wzBizTypeStatus;

    @Schema(description = "账号数量", example = "26469")
    private Integer wzAccountCount;


    @Schema(description = "智慧脸业务线类型开通", example = "2")
    private Integer zhlBizTypeStatus;

    @Schema(description = "智慧脸账号数量", example = "17503")
    private Integer zhlAccountCount;


    @Schema(description = "营业执照名称", example = "张三")
    @ExcelProperty("营业执照名称")
    private String businessLicenseName;

    @Schema(description = "营业执照号")
    @ExcelProperty("营业执照号")
    private String businessLicenseNumber;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("省")
    private String province;

    @Schema(description = "省编码")
    @ExcelProperty("省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("市")
    private String city;

    @Schema(description = "市编码")
    @ExcelProperty("市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("区")
    private String area;

    @Schema(description = "区编码")
    @ExcelProperty("区编码")
    private String areaCode;

    @Schema(description = "药店地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("药店地址")
    private String address;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
