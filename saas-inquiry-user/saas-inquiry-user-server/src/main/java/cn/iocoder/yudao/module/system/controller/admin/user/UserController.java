package cn.iocoder.yudao.module.system.controller.admin.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_HAS_ROLE_NO_ALLOW_ERROR;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_UPDATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_UPDATE_SUCCESS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.datapermission.core.annotation.DataPermission;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserInfoVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserUpdatePasswordReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserUpdateStatusReqVO;
import cn.iocoder.yudao.module.system.convert.user.UserConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 用户(员工)")
@RestController
@RequestMapping(value = {"/admin-api/system/user", "/app-api/system/user"})
@Validated
@DataPermission(enable = false)
public class UserController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private TenantUserRelationService tenantUserRelationService;

    @PostMapping("/store/create")
    @Operation(summary = "新增员工")
    @PreAuthorize("@ss.hasPermission('system:employee:create')")
    public CommonResult<Long> createUserStore(@Valid @RequestBody UserSaveReqVO reqVO) {
        Long id = TenantUtils.execute(Optional.ofNullable(reqVO.getTenantId()).orElse(TenantContextHolder.getTenantId()), () -> userService.createUserStore(reqVO));
        return success(id);
    }

    @PutMapping("/store/update")
    @Operation(summary = "修改员工")
    @PreAuthorize("@ss.hasAnyPermissions('system:employee:update','system:user:update')")
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",
        success = SYSTEM_USER_UPDATE_SUCCESS)
    public CommonResult<Boolean> updateUserStore(@Valid @RequestBody UserSaveReqVO reqVO) {
        TenantUtils.execute(Optional.ofNullable(reqVO.getTenantId()).orElse(TenantContextHolder.getTenantId()), () -> userService.updateUserStore(reqVO));
        return success(true);
    }

    @DeleteMapping("/store/delete")
    @Operation(summary = "删除员工")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('system:employee:delete','system:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id, @RequestParam(value = "tenantId", required = false) Long tenantId) {
        TenantUtils.execute(Optional.ofNullable(tenantId).orElse(TenantContextHolder.getTenantId()), () -> userService.deleteUserStore(id));
        return success(true);
    }

    @PutMapping("/store/update-password")
    @Operation(summary = "重置员工密码")
    @PreAuthorize("@ss.hasAnyPermissions('system:user:update-password','system:employee:update-password')")
    public CommonResult<Boolean> updateUserPassword(@Valid @RequestBody UserUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(reqVO.getId(), reqVO.getPassword());
        return success(true);
    }

    @PutMapping("/store/update-status")
    @Operation(summary = "修改员工状态")
    @PreAuthorize("@ss.hasPermission('system:employee:update')")
    public CommonResult<Boolean> updateUserStatusStore(@Valid @RequestBody UserUpdateStatusReqVO reqVO) {
        TenantUtils.execute(Optional.ofNullable(reqVO.getTenantId()).orElse(TenantContextHolder.getTenantId()), () -> userService.updateEmployeeStatus(reqVO.getId(), reqVO.getStatus()));
        return success(true);
    }

    @GetMapping("/store/page")
    @Operation(summary = "获得员工分页列表")
    @PreAuthorize("@ss.hasPermission('system:employee:list')")
    public CommonResult<PageResult<UserRespVO>> getUserPageStore(@Valid UserPageReqVO pageReqVO) {
        // 获得员工分页列表
        PageResult<UserRespVO> pageResult = TenantUtils.execute(Optional.ofNullable(pageReqVO.getTenantId()).orElse(TenantContextHolder.getTenantId()), () -> userService.getUserPageStore(pageReqVO));
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal()));
        }
        // 拼接数据
        Map<Long, DeptDO> deptMap = TenantUtils.execute(Optional.ofNullable(pageReqVO.getTenantId()).orElse(TenantContextHolder.getTenantId()), () -> deptService.getDeptMap(
            convertList(pageResult.getList(), UserRespVO::getDeptId)));
        return success(new PageResult<>(UserConvert.INSTANCE.convertList(pageResult.getList(), deptMap),
            pageResult.getTotal()));
    }

    @GetMapping({"/store/list-all-simple", "/store/simple-list"})
    @Operation(summary = "获取员工精简信息列表", description = "只包含被开启的员工，主要用于前端的下拉选项")
    public CommonResult<List<UserSimpleRespVO>> getSimpleUserListStore() {
        List<AdminUserDO> list = userService.getSimpleUserListStore(CommonStatusEnum.ENABLE.getStatus());
        // 拼接数据
        Map<Long, DeptDO> deptMap = deptService.getDeptMap(
            convertList(list, AdminUserDO::getDeptId));
        return success(UserConvert.INSTANCE.convertSimpleList(list, deptMap));
    }

    @GetMapping("/store/get")
    @Operation(summary = "获得员工详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasAnyPermissions('system:employee:query','system:user:query')")
    public CommonResult<UserRespVO> getUserStore(@RequestParam("id") Long id) {
        AdminUserDO user = userService.getUserStore(id);
        if (user == null) {
            return success(null);
        }
        // 拼接数据
        DeptDO dept = deptService.getDept(user.getDeptId());
        return success(UserConvert.INSTANCE.convert(BeanUtils.toBean(user, UserRespVO.class), dept));
    }

    @GetMapping("/store/get-by-mobile")
    @Operation(summary = "根据手机号获得user")
    @Parameter(name = "mobile", description = "手机号", required = true, example = "15926350000")
    @PreAuthorize("@ss.hasAnyPermissions('system:employee:query','system:user:query')")
    public CommonResult<UserRespVO> getUserByMobileStore(@RequestParam("mobile") String mobile) {
        AdminUserDO user = userService.getUserByMobileStore(mobile);
        return success(UserConvert.INSTANCE.convertUserVo(user));
    }


    @GetMapping("/store/get-info")
    @Operation(summary = "获取登录用户信息")
    public CommonResult<UserInfoVO> getUserInfo() {
        return success(userService.getUserInfo());
    }

//    @GetMapping("/export")
//    @Operation(summary = "导出用户")
//    @PreAuthorize("@ss.hasPermission('system:user:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportUserList(@Validated UserPageReqVO exportReqVO,
//                               HttpServletResponse response) throws IOException {
//        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<UserRespVO> list = userService.getEmployeePage(exportReqVO).getList();
//        // 输出 Excel
//        Map<Long, DeptDO> deptMap = deptService.getDeptMap(
//                convertList(list, UserRespVO::getDeptId));
//        ExcelUtils.write(response, "用户数据.xls", "数据", UserRespVO.class,
//                UserConvert.INSTANCE.convertList(list, deptMap));
//    }
//
//    @GetMapping("/get-import-template")
//    @Operation(summary = "获得导入用户模板")
//    public void importTemplate(HttpServletResponse response) throws IOException {
//        // 手动创建导出 demo
//        List<UserImportExcelVO> list = Arrays.asList(
//                UserImportExcelVO.builder().username("yunai").deptId(1L).email("<EMAIL>").mobile("15601691300")
//                        .nickname("芋道").status(CommonStatusEnum.ENABLE.getStatus()).sex(SexEnum.MALE.getSex()).build(),
//                UserImportExcelVO.builder().username("yuanma").deptId(2L).email("<EMAIL>").mobile("15601701300")
//                        .nickname("源码").status(CommonStatusEnum.DISABLE.getStatus()).sex(SexEnum.FEMALE.getSex()).build()
//        );
//        // 输出
//        ExcelUtils.write(response, "用户导入模板.xls", "用户列表", UserImportExcelVO.class, list);
//    }
//
//    @PostMapping("/import")
//    @Operation(summary = "导入用户")
//    @Parameters({
//            @Parameter(name = "file", description = "Excel 文件", required = true),
//            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
//    })
//    @PreAuthorize("@ss.hasPermission('system:user:import')")
//    public CommonResult<UserImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
//                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
//        List<UserImportExcelVO> list = ExcelUtils.read(file, UserImportExcelVO.class);
//        return success(userService.importUserList(list, updateSupport));
//    }

//**************** 超级管理员 忽略门店配置 **************

    @GetMapping("/system/page")
    @Operation(summary = "超管-获得用户分页列表")
    @PreAuthorize("@ss.hasPermission('system:user:list')")
    @TenantIgnore
    public CommonResult<PageResult<UserRespVO>> getUserPageSystem(@Valid UserPageReqVO pageReqVO) {
        // 获得用户分页列表
        PageResult<UserRespVO> pageResult = userService.getUserPageSystem(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal()));
        }
        return success(pageResult);
    }

    @PutMapping("/system/update-status")
    @Operation(summary = "修改用户状态")
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    @TenantIgnore
    public CommonResult<Boolean> updateUserStatusSystem(@Valid @RequestBody UserUpdateStatusReqVO reqVO) {
        userService.updateUserStatusSystem(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @DeleteMapping("/system/delete")
    @Operation(summary = "删除员工")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:employee:delete')")
    public CommonResult<Boolean> deleteUserSystem(@RequestParam("id") Long id) {
        TenantUtils.executeIgnore(() -> userService.deleteUserSystem(id));
        return success(true);
    }

    @GetMapping("/system/create-get-by-mobile")
    @Operation(summary = "根据手机号获得user")
    @Parameter(name = "mobile", description = "手机号", required = true, example = "15926350000")
    @PreAuthorize("@ss.hasAnyPermissions('system:employee:query','system:user:query')")
    @TenantIgnore
    public CommonResult<UserRespVO> getUserByMobileSystem(UserPageReqVO reqVO) {
        if (StringUtils.isBlank(reqVO.getMobile())) {
            return success(null);
        }

        AdminUserDO user = userService.getUserByMobileSystem(reqVO.getMobile());
        if (user == null) {
            return success(null);
        }
        // 创建医生 查询药师
        if (StringUtils.equals(reqVO.getRoleCode(), RoleCodeEnum.DOCTOR.getCode())) {
            if (CollUtil.isNotEmpty(permissionService.selectUserRoleByUserIdRoleCodes(user.getId(), Set.of(RoleCodeEnum.PHARMACIST.getCode())))) {
                throw exception(USER_HAS_ROLE_NO_ALLOW_ERROR, RoleCodeEnum.PHARMACIST.getName());
            }
            // 查询医生 判断是否存在门店绑定关系
            List<TenantUserRelationDO> relationDOList = tenantUserRelationService.getTenantListByUserId(user.getId());
            if (CollUtil.isNotEmpty(relationDOList) && relationDOList.stream().anyMatch(t -> !Objects.equals(t.getTenantId(), TenantConstant.DEFAULT_TENANT_ID))) {
                throw exception(USER_HAS_ROLE_NO_ALLOW_ERROR, "门店等");
            }
        }

        // 创建药师查询医生
        if (StringUtils.equals(reqVO.getRoleCode(), RoleCodeEnum.PHARMACIST.getCode())) {
            if (CollUtil.isNotEmpty(permissionService.selectUserRoleByUserIdRoleCodes(user.getId(), Set.of(RoleCodeEnum.DOCTOR.getCode())))) {
                throw exception(USER_HAS_ROLE_NO_ALLOW_ERROR, RoleCodeEnum.DOCTOR.getName());
            }
        }

        // 查询当前用户关联门店
        List<TenantUserRelationDO> relationDOList = tenantUserRelationService.getTenantListByUserId(user.getId());
        return success(UserConvert.INSTANCE.convertUserVoWithTenantList(user, relationDOList));
    }


}
