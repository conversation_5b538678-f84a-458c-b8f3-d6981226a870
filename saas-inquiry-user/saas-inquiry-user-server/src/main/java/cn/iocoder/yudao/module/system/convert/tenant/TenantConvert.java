package cn.iocoder.yudao.module.system.convert.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantAndPackageExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantInfoVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantCertificateTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateMessageDto;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantConvert {

    TenantConvert INSTANCE = Mappers.getMapper(TenantConvert.class);

    default UserSaveReqVO convert02(TenantSaveReqVO bean) {
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setUsername(bean.getUsername());
        reqVO.setPassword(bean.getPassword());
        reqVO.setNickname(bean.getContactName()).setMobile(bean.getContactMobile());
        return reqVO;
    }

    default List<TenantSimpleRespVO> convert2SimpleRespVO(List<TenantDO> tenantDOS) {
        if (tenantDOS == null) {
            return null;
        }
        return tenantDOS.stream().map(t -> {
            TenantSimpleRespVO respVO = new TenantSimpleRespVO();
            respVO.setId(t.getId());
            respVO.setName(t.getName());
            respVO.setAddress(Stream.of(t.getProvince(), t.getCity(), t.getArea(), t.getAddress()).filter(StringUtils::isNotBlank).collect(Collectors.joining()));
            return respVO;
        }).collect(Collectors.toList());


    }

    TenantRespVO convertDo2Vo(TenantDO tenant);

    @Mapping(target = "type", expression = "java(com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum.fromCode(tenantDO.getType()))")
    @Mapping(target = "headTenantId", expression = "java(tenantDO.getHeadTenantId() == null ? tenantDO.getId() : tenantDO.getHeadTenantId())")
    TenantDto convertDto(TenantDO tenantDO);

    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getMdPref())")
    TenantDO convertInitDo(TenantSaveReqVO createReqVO);

    List<TenantDto> convertDtos(List<TenantDO> tenantDOList);


    @Mapping(target = "id", source = "contactUserId")
    @Mapping(target = "username", source = "contactMobile")
    @Mapping(target = "mobile", source = "contactMobile")
    @Mapping(target = "nickname", source = "contactName")
    @Mapping(target = "status", ignore = true)
    UserSaveReqVO convertUser(TenantSaveReqVO updateReqVO);

    List<TenantRespVO> convertDo2Vos(List<TenantDO> records);


    TenantPageReqVO convert(TenantReqDto tenantReqDto);

    List<TenantRespDto> convertRespDtos(List<TenantRespVO> list);

    TenantInfoUpdateMessageDto convertUpdateInfo(TenantDO tenantDO);

    default TenantSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO, Map<String, TenantDO> tenantDOMap, List<TenantCertificateRespVO> certs) {

        int accountCount = 100;

        TenantSaveReqVO tenantSaveReqVO = StringUtils.isBlank(excelVO.getPref()) ? convertImportVo(excelVO) : convertDo2SaveVo(tenantDOMap.get(excelVO.getPref()));
        tenantSaveReqVO.setHeadTenantId(StringUtils.isBlank(excelVO.getHeadTenantPref()) ? null : tenantDOMap.get(excelVO.getHeadTenantPref()).getId());

        tenantSaveReqVO.setWzAccountCount(accountCount);
        tenantSaveReqVO.setZhlAccountCount(accountCount);

        if (StringUtils.isNotBlank(excelVO.getBizType())) {
            tenantSaveReqVO.setWzBizTypeStatus(StringUtils.contains(excelVO.getBizType(), BizTypeEnum.HYWZ.getCode() + "") ? CommonStatusEnum.ENABLE.getStatus() : CommonStatusEnum.DISABLE.getStatus());
            tenantSaveReqVO.setZhlBizTypeStatus(StringUtils.contains(excelVO.getBizType(), BizTypeEnum.ZHL.getCode() + "") ? CommonStatusEnum.ENABLE.getStatus() : CommonStatusEnum.DISABLE.getStatus());
        }

        // 修改
        if (StringUtils.isNotBlank(excelVO.getPref())) {
            TenantDO tenantDO = tenantDOMap.get(excelVO.getPref());
            copyNonNull(excelVO, tenantSaveReqVO);
            // 非连锁门店，设置 headTenantId 为 null
            if (excelVO.type() != null && !Objects.equals(excelVO.type(), TenantTypeEnum.CHAIN_STORE.getCode())) {
                tenantSaveReqVO.setHeadTenantId(null);
            } else {
                tenantSaveReqVO.setHeadTenantId(StringUtils.isBlank(excelVO.getHeadTenantPref()) ? tenantDO.getHeadTenantId() : tenantDOMap.get(excelVO.getHeadTenantPref()).getId());
            }
            tenantSaveReqVO.setWzAccountCount(CommonStatusEnum.isEnable(tenantSaveReqVO.getWzBizTypeStatus()) && CommonStatusEnum.isDisable(tenantDO.getWzBizTypeStatus())
                ? accountCount : tenantDO.getWzAccountCount());
            tenantSaveReqVO.setZhlAccountCount(CommonStatusEnum.isEnable(tenantSaveReqVO.getZhlBizTypeStatus()) && CommonStatusEnum.isDisable(tenantDO.getZhlBizTypeStatus())
                ? accountCount : tenantDO.getZhlAccountCount());
        }

        // 转换省市区
        tenantSaveReqVO.setProvinceCode(StringUtils.isBlank(excelVO.getProvince()) ? null : AreaUtils.parseArea(excelVO.getProvince()).getId().toString());
        tenantSaveReqVO.setCityCode(StringUtils.isBlank(excelVO.getCity()) ? null : AreaUtils.parseArea(excelVO.getCity()).getId().toString());
        tenantSaveReqVO.setAreaCode(StringUtils.isBlank(excelVO.getArea()) ? null : AreaUtils.parseArea(excelVO.getArea()).getId().toString());

        // 处理配置参数
        List<TenantParamConfigDto> paramConfigDtos = Stream.of(StringUtils.isBlank(excelVO.getInquiryService()) ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_SERVER.getType())
                .paramValue(excelVO.getInquiryService())
                .build(),
            StringUtils.isBlank(excelVO.getPrescriptionDateFormat()) ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_PRES_DATE_TYPE.getType())
                .paramValue(excelVO.getPrescriptionDateFormat())
                .build(),
            StringUtils.isBlank(excelVO.getReviewType()) ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_PRES_AUDIT_TYPE.getType())
                .paramValue(excelVO.getReviewType())
                .build()
        ).filter(Objects::nonNull).toList();
        tenantSaveReqVO.setInquiryParamConfigs(paramConfigDtos);

        // 处理资质
        if (CollUtil.isEmpty(certs)) {
            tenantSaveReqVO.setCertificates(List.of(
                TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                    .certificateType(TenantCertificateTypeEnum.YYZJ.getType())
                    .certificateName(excelVO.getBusinessLicenseName())
                    .certificateNo(excelVO.getBusinessLicenseNumber())
                    .build(),
                TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                    .certificateType(TenantCertificateTypeEnum.JYXK.getType())
                    .certificateName(TenantCertificateTypeEnum.JYXK.getDescription())
                    .certificateNo(excelVO.getDrugBusinessLicenseNumber())
                    .registerTime(excelVO.drugBusinessRegistrationDate())
                    .validTime(excelVO.drugBusinessLicenseExpiry())
                    .build(),
                TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                    .certificateType(TenantCertificateTypeEnum.JYZLGL.getType())
                    .certificateName(TenantCertificateTypeEnum.JYZLGL.getDescription())
                    .certificateNo(excelVO.getGspLicenseNumber())
                    .build()
            ));
        } else {
            for (TenantCertificateRespVO cert : certs) {
                if (Objects.equals(cert.getCertificateType(), TenantCertificateTypeEnum.YYZJ.getType())) {
                    cert.setCertificateName(excelVO.getBusinessLicenseName());
                    cert.setCertificateNo(excelVO.getBusinessLicenseNumber());
                }
                if (Objects.equals(cert.getCertificateType(), TenantCertificateTypeEnum.JYXK.getType())) {
                    cert.setCertificateNo(excelVO.getDrugBusinessLicenseNumber());
                    cert.setRegisterTime(excelVO.drugBusinessRegistrationDate());
                    cert.setValidTime(excelVO.drugBusinessLicenseExpiry());
                }
                if (Objects.equals(cert.getCertificateType(), TenantCertificateTypeEnum.JYZLGL.getType())) {
                    cert.setCertificateNo(excelVO.getGspLicenseNumber());
                }
            }
            tenantSaveReqVO.setCertificates(TenantCertificateConvert.INSTANCE.convert2Do(certs));
        }

        return tenantSaveReqVO;

    }

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void copyNonNull(TenantAndPackageExcelVO source, @MappingTarget TenantSaveReqVO target);

    TenantSaveReqVO convertDo2SaveVo(TenantDO tenantDO);

    @Mapping(target = "username", source = "contactMobile")
    TenantSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO);

    TenantInfoVO convertDto2Vo(TenantDO tenantDO);
}
