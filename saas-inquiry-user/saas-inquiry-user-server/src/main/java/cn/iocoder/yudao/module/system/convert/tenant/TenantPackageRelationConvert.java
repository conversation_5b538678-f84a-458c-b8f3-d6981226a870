package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationStatusChangeDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantAndPackageExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantPackageRelationOpenExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantPackageRelationUpdateExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationStatusChangeReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageRelationConvert {

    TenantPackageRelationConvert INSTANCE = Mappers.getMapper(TenantPackageRelationConvert.class);

    //    @Mapping(target = "inquiryWayTypes",expression = "java(createReqVO.getInquiryPackageItems().stream().map(InquiryPackageItem::getInquiryWayType).distinct().collect(java.util.stream.Collectors.toList()))")
    TenantPackageRelationDO tenantPackageRelationUpdateReqVO2DO(TenantPackageRelationSaveReqVO createReqVO);

    @Mapping(target = "inquiryWayTypes", expression = "java(java.util.Optional.ofNullable(createReqVO.getInquiryPackageItems()).orElse(List.of()).stream().map(InquiryPackageItem::getInquiryWayType).distinct().collect(java.util.stream"
        + ".Collectors.toList()))")
    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getTcbPref())")
    TenantPackageRelationDO tenantPackageRelationInitReqVO2DO(TenantPackageRelationSaveReqVO createReqVO);


    TenantPackageRelationDO invalidConvert(TenantPackageRelationStatusChangeReqVO invalidReqVO);

    TenantPackageRelationRespVO convertVO(TenantPackageRelationDO tp);

    /**
     * 填充套餐信息 至 套餐包
     * @param vo 套餐包
     * @param pk 套餐
     */
//    default void fillPackage(TenantPackageRelationRespVO vo, TenantPackageRespVO pk){
//        vo.setPlatformReview(pk.getPlatformReview());
//        vo.setPrice(pk.getPrice());
//        vo.setTerm(pk.getTerm() + DateTermTypeEnum.fromCode(pk.getTermType()).getDesc());
//        vo.setInquiryPackageItemStr(pk.getInquiryPackageItemStr());
//        vo.setHospitalName(pk.getHospitalName());
//    }

//    @Mapping(source = "createTime",target = "openTime")
//    @Mapping(source = "id",target = "tenantPackageId")
//    TenantPackageRelationRespDto convertDo2Dto(TenantPackageRelationDO p);

    /**
     * 填充套餐关系信息  服务时间和状态
     *
     * @param tenantPackageDO       套餐
     * @param tenantPackageRelation 套餐订单
     */
    default void fillPackageRelationInfo(TenantPackageDO tenantPackageDO, TenantPackageRelationDO tenantPackageRelation) {
        LocalDateTime endTime = DateTermTypeEnum.fromCode(tenantPackageDO.getTermType()).calculationTermDate(tenantPackageDO.getTerm());
        tenantPackageRelation.setEndTime(tenantPackageRelation.getEndTime() == null ? endTime : tenantPackageRelation.getEndTime());
        fillPackageInfo(tenantPackageDO, tenantPackageRelation);
    }

    /**
     * 填充套餐信息
     *
     * @param tenantPackageDO
     * @param tenantPackageRelation
     */
    @Mapping(target = "packageId", source = "id")
    @Mapping(target = "status", expression = "java(com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum.NORMAL.getCode())")
    @Mapping(target = "packageName", source = "name")
    @Mapping(target = "inquiryWayTypes", expression = "java(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(tenantPackageDO.getInquiryPackageItems(), InquiryPackageItem::getInquiryWayType))")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "updater", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    void fillPackageInfo(TenantPackageDO tenantPackageDO, @MappingTarget TenantPackageRelationDO tenantPackageRelation);
    // tenantPackageRelation.setPackageId(tenantPackageDO.getId());
    // tenantPackageRelation.setStatus(TenantPackageRelationStatusEnum.NORMAL.getCode());
    // tenantPackageRelation.setPackageName(tenantPackageDO.getName());
    // // 套餐包信息
    // tenantPackageRelation.setBizType(tenantPackageDO.getBizType());
    // tenantPackageRelation.setHospitalPrefs(tenantPackageDO.getHospitalPrefs());
    // tenantPackageRelation.setInquiryPackageItems(tenantPackageDO.getInquiryPackageItems());
    // tenantPackageRelation.setInquiryWayTypes(CollectionUtils.convertList(tenantPackageDO.getInquiryPackageItems(), InquiryPackageItem::getInquiryWayType));
    // tenantPackageRelation.setPrice(tenantPackageDO.getPrice());
    // tenantPackageRelation.setPlatformReview(tenantPackageDO.getPlatformReview());


    List<TenantPackageRelationRespVO> convertDo2Vo(List<TenantPackageRelationDO> relationList);

    List<TenantPackageRelationRespDto> convertVo2Dto(List<TenantPackageRelationRespVO> tenantPackageRelationList);

    default void fillBaseInfo(TenantPackageRelationRespVO tp, TenantPackageDO tenantPackageDO, TenantDO tenantDO) {
        tp.setTerm(tenantPackageDO.getTerm());
        tp.setTermType(tenantPackageDO.getTermType());
        tp.setPackagePref(tenantPackageDO.getPref());
        tp.setTenantPref(tenantDO.getPref());
        tp.setTenantName(tenantDO.getName());
        tp.setProvince(tenantDO.getProvince());
        tp.setCity(tenantDO.getCity());
        tp.setArea(tenantDO.getArea());
    }


    default List<TenantPackageRelationDO> convertUpdateStatus(List<TenantPackageRelationDO> tcbList, TenantPackageRelationStatusEnum statusEnum, TenantPackageRelationStatusChangeDto changeDto) {
        return tcbList.stream().map(t -> TenantPackageRelationDO.builder()
            .id(t.getId()).tenantId(t.getTenantId()).status(statusEnum.getCode())
            .statusChangeInfo(changeDto).build()).collect(Collectors.toList());
    }

    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getTcbPref())")
    TenantPackageRelationDO tenantPackageRelationInitCopy(TenantPackageRelationDO tcb);

    TenantPackageRelationPageReqVO convertDto2Vo(TenantPackageRelationPageReqDto pageReqDto);

    @Mapping(target = "endTime", expression = "java(updateExcelVO.localEndTime())")
    @Mapping(target = "signTime", expression = "java(updateExcelVO.localSignTime())")
    @Mapping(target = "actualAmount", expression = "java(updateExcelVO.actualAmount())")
    @Mapping(target = "status", expression = "java(updateExcelVO.status())")
    TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationUpdateExcelVO updateExcelVO);

    default TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationUpdateExcelVO updateExcelVO, TenantPackageRelationDO oldRelation) {
        TenantPackageRelationSaveReqVO relationSaveReqVO = convertImportVo(updateExcelVO);
        TenantPackageRelationStatusChangeDto changeInfo = Optional.ofNullable(oldRelation.getStatusChangeInfo()).orElse(TenantPackageRelationStatusChangeDto.builder().build());
        changeInfo.setRefundType(updateExcelVO.refundType());
        changeInfo.setRemark(updateExcelVO.getReason());
        changeInfo.setRefundPrice(updateExcelVO.refundPrice());
        relationSaveReqVO.setStatusChangeInfo(changeInfo);
        relationSaveReqVO.setId(oldRelation.getId());
        return relationSaveReqVO;
    }


    default TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationOpenExcelVO excelVO, TenantDO tenantDO, TenantPackageDO packageDO) {
        TenantPackageRelationSaveReqVO saveReqVO = convertImportVo(excelVO);
        saveReqVO.setTenantId(tenantDO.getId());
        saveReqVO.setPackageId(packageDO.getId());
        saveReqVO.setPackagePref(packageDO.getPref());
        saveReqVO.setBizType(packageDO.getBizType());
        saveReqVO.setPackageName(packageDO.getName());
        return saveReqVO;
    }

    @Mapping(target = "startTime", expression = "java(excelVO.localStartTime())")
    @Mapping(target = "endTime", expression = "java(excelVO.localEndTime())")
    @Mapping(target = "actualAmount", expression = "java(excelVO.actualAmount())")
    @Mapping(target = "signTime", expression = "java(excelVO.localSignTime())")
    TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationOpenExcelVO excelVO);


    default TenantPackageRelationSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO, Long tenantId, TenantPackageDO packageDO) {
        TenantPackageRelationSaveReqVO saveReqVO = convertImportVo(excelVO);
        saveReqVO.setTenantId(tenantId);
        saveReqVO.setPackageId(packageDO.getId());
        saveReqVO.setPackagePref(packageDO.getPref());
        saveReqVO.setBizType(packageDO.getBizType());
        saveReqVO.setPackageName(packageDO.getName());
        return saveReqVO;
    }

    @Mapping(target = "startTime", expression = "java(excelVO.localStartTime())")
    @Mapping(target = "endTime", expression = "java(excelVO.localEndTime())")
    @Mapping(target = "signTime", expression = "java(excelVO.localSignTime())")
    @Mapping(target = "status", expression = "java(excelVO.status())")
    @Mapping(target = "actualAmount", expression = "java(excelVO.actualAmount())")
    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "bizType", ignore = true)
    TenantPackageRelationSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO);
}
