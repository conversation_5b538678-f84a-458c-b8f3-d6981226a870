package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationBindDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationUpdateVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantUserRelationConvert {

    TenantUserRelationConvert INSTANCE = Mappers.getMapper(TenantUserRelationConvert.class);

    TenantUserRelationDO dto2Do(TenantUserRelationDto relationDto);

    default TenantUserRelationDto userSaveReqVO2Dto(UserSaveReqVO createReqVO, Long id) {
        TenantUserRelationDto tenantUserRelationDto = new TenantUserRelationDto();
        tenantUserRelationDto.setUserId(id);
        tenantUserRelationDto.setNeedClockIn(createReqVO.getNeedClockIn());
        tenantUserRelationDto.setDeptId(createReqVO.getDeptId());
        tenantUserRelationDto.setPostIds(createReqVO.getPostIds());
        return tenantUserRelationDto;
    }

    default List<TenantUserRelationDO> convertUpdateDo(List<TenantUserRelationDO> userRelationDOS, TenantUserRelationDto tenantUserRelationDto) {
        return userRelationDOS.stream().peek(u -> {
            u.setDeptId(tenantUserRelationDto.getDeptId());
            u.setPostIds(tenantUserRelationDto.getPostIds());
            u.setStatus(tenantUserRelationDto.getStatus());
            u.setJoinTime(tenantUserRelationDto.getJoinTime());
            u.setResignationTime(tenantUserRelationDto.getResignationTime());
        }).collect(Collectors.toList());
    }

    TenantUserRelationDto convertDto(TenantUserRelationDO tenantUserRelation);

    default List<TenantUserRelationDO> convertBindDo(TenantUserRelationUpdateVO bindVO) {
        return bindVO.getTenantIds().stream()
            .map(t -> TenantUserRelationDO.builder().tenantId(t)
                .userId(bindVO.getUserId())
                .status(UserStatusEnum.ENABLE.getCode())
                .needClockIn(bindVO.getNeedClockIn())
                .joinTime(LocalDateTime.now()).build())
            .collect(Collectors.toList());
    }

    List<TenantUserRelationDto> convertDo2Dto(List<TenantUserRelationDO> tenantUserRelationDOS);

    TenantUserRelationUpdateVO convert(TenantUserRelationBindDto bindDto);

    default TenantUserRelationDto convertDto(TenantUserRelationDO tenantUserRelation, TenantDO tenantDO) {
        TenantUserRelationDto t = convertDto(tenantUserRelation);
        t.setTenantType(tenantDO.getType());
        t.setHeadTenantId(tenantDO.getHeadTenantId());
        return t;
    }

    default void fillUserRelationDto(TenantUserRelationDO userRelationDO, AdminUserRespDTO userRespDTO) {
        if (userRelationDO != null) {
            userRespDTO.setTenantUserRelationId(userRelationDO.getId());
            userRespDTO.setRelationStatus(userRelationDO.getStatus());
            userRespDTO.setNeedClockIn(userRelationDO.getNeedClockIn());
            userRespDTO.setClockInStatus(userRelationDO.getClockInStatus());
        }

    }
}
