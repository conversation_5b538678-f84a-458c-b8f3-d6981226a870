package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.servicepack.ServicePackRelationExtDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 门店-开通服务包关系 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_tenant_transmission_service_pack_relation", autoResultMap = true)
@KeySequence("saas_tenant_transmission_service_pack_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantTransmissionServicePackRelationDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 门店id
     */
    private Long tenantId;
    /**
     * 服务包ID
     */
    private Integer servicePackId;
    /**
     * 目录版本id
     */
    private Long catalogId;
    /**
     * 医药行业行政机构ID
     */
    private Integer organId;
    /**
     * 服务机构类型（1-医保、2-药监、3-互联网医院监管、99-其他）
     */
    private Integer organType;
    /**
     * 版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122）
     */
    private Long servicePackVersion;

    /**
     * 开通关系扩展信息 - 网络、(医院)等
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ServicePackRelationExtDto ext;

    /**
     * 状态 0未开通 1开通
     */
    private Integer status;

    @JsonIgnore
    public ServicePackRelationExtDto extGet() {
        if (ext == null) {
            ext = new ServicePackRelationExtDto();
        }
        return ext;
    }

}