package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 门店员工关系 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant_user_relation", autoResultMap = true)
@KeySequence("system_tenant_user_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TenantUserRelationDO extends BaseDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 门店ID
     */
    private Long tenantId;

    /**
     * 部门编号
     */
    private Long deptId;

    /**
     * 岗位编号数组
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private Set<Long> postIds;

    /**
     * 入职时间
     */
    private LocalDateTime joinTime;

    /**
     * 离职时间
     */
    private LocalDateTime resignationTime;

    /**
     * 员工状态 {@link UserStatusEnum}
     */
    private Integer status;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer needClockIn;

    /**
     * 是否打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer clockInStatus;


}