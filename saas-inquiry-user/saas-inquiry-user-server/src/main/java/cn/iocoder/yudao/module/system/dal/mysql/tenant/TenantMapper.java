package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 门店 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantMapper extends BaseMapperX<TenantDO> {

    default PageResult<TenantDO> selectPage(TenantPageReqVO reqVO) {
        LambdaQueryWrapperX<TenantDO> queryWrapper = new LambdaQueryWrapperX<TenantDO>()
            .likeIfPresent(TenantDO::getName, reqVO.getName())
            .eqIfPresent(TenantDO::getPref, reqVO.getPref())
            .likeIfPresent(TenantDO::getContactName, reqVO.getContactName())
            .likeIfPresent(TenantDO::getContactMobile, reqVO.getContactMobile())
            .likeIfPresent(TenantDO::getBusinessLicenseName, reqVO.getBusinessLicenseName())
            .likeIfPresent(TenantDO::getBusinessLicenseNumber, reqVO.getBusinessLicenseNumber())
            .eqIfPresent(TenantDO::getStatus, reqVO.getStatus())
            .eqIfPresent(TenantDO::getProvince, reqVO.getProvince())
            .eqIfPresent(TenantDO::getProvinceCode, reqVO.getProvinceCode())
            .eqIfPresent(TenantDO::getCity, reqVO.getCity())
            .eqIfPresent(TenantDO::getCityCode, reqVO.getCityCode())
            .eqIfPresent(TenantDO::getArea, reqVO.getArea())
            .eqIfPresent(TenantDO::getAreaCode, reqVO.getAreaCode())
            .eqIfPresent(TenantDO::getAddress, reqVO.getAddress())
            .eqIfPresent(TenantDO::getEnvTag, reqVO.getEnvTag())
            .betweenIfPresent(TenantDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TenantDO::getId);

        if (StringUtils.isNotBlank(reqVO.getNameOrPref())) {
            queryWrapper.and(orWrapper -> orWrapper.or(o -> o.like(TenantDO::getName, reqVO.getNameOrPref()))
                .or(a -> a.eq(TenantDO::getPref, reqVO.getNameOrPref())));
        }

        return selectPage(reqVO, queryWrapper);
    }

    default List<TenantDO> selectListByNameOrPref(String name) {
        LambdaQueryWrapperX<TenantDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.like(TenantDO::getName, name)
            .or()
            .eq(TenantDO::getPref, name);
        return selectList(queryWrapper);
    }


    default TenantDO selectByName(String name) {
        return selectOne(TenantDO::getName, name);
    }

    default TenantDO selectByBusinessLicenseNumber(String businessLicenseNumber) {
        return selectOne(TenantDO::getBusinessLicenseNumber, businessLicenseNumber);
    }

    default TenantDO selectByBusinessLicenseName(String businessLicenseName) {
        return selectOne(TenantDO::getBusinessLicenseName, businessLicenseName);
    }

    default List<TenantDO> getAvailableTenantByIds(List<Long> tenantIdList) {
        return selectList(new LambdaQueryWrapperX<TenantDO>().in(TenantDO::getId, tenantIdList).eq(TenantDO::getStatus, CommonStatusEnum.ENABLE.getStatus()));
    }

    default List<TenantDO> getTenantByPrefs(List<String> prefs) {
        return selectList(new LambdaQueryWrapperX<TenantDO>().in(TenantDO::getPref, prefs));
    }

    default List<TenantDO> getTenantByNames(List<String> names) {
        return selectList(new LambdaQueryWrapperX<TenantDO>().in(TenantDO::getName, names));
    }

    default List<TenantDO> getTenantBusinessLicenseNumbers(List<String> numbers) {
        return selectList(new LambdaQueryWrapperX<TenantDO>().in(TenantDO::getBusinessLicenseNumber, numbers));
    }

    default List<TenantDO> getTenantBusinessLicenseNames(List<String> names) {
        return selectList(new LambdaQueryWrapperX<TenantDO>().in(TenantDO::getBusinessLicenseName, names));
    }

    IPage<TenantDO> getTenantPage(Page<Object> objectPage, TenantPageReqVO pageReqVO);

}
