package cn.iocoder.yudao.module.system.dal.redis.common;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_USER_LOCKED;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.util.MathUtil;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class UserLockRedisDAO {

    @Resource
    private ConfigApi configApi;


    /**
     * 记录用户锁次数
     *
     * @param lockKey   锁定key
     * @param lockValue 锁定值
     */
    public void lockRecord(String lockKey, String lockValue) {
        String key = lockKey + lockValue;
        //锁定多少秒，默认30分钟  =  30 * 60 = 1800 秒
        int timeOut = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(lockKey.concat("_time")), 1800);
        Integer count = (Integer) RedisUtils.get(key);
        if (ObjectUtil.isEmpty(count)) {
            RedisUtils.set(key, 1, timeOut);
            return;
        }
        //次数加1 , 并重新设置当前key的过期时间
        RedisUtils.set(key, count + 1, timeOut);
    }

    /**
     * 校验当前锁次数
     *
     * @param lockKey
     * @param lockValue
     * @param errorCode
     */
    public void isLocked(String lockKey, String lockValue, ErrorCode errorCode) {
        String key = lockKey + lockValue;
        Integer count = (Integer) RedisUtils.get(key);
        // 当前key不存在直接返回
        if (ObjectUtil.isEmpty(count)) {
            return;
        }
        int failCount = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(lockKey), 5);
        if (count >= failCount) {
            // 获取key 剩余时长，单位秒
            long time = RedisUtils.getExpire(key);
            throw exception(errorCode, Math.ceilDiv(time, 60));
        }
    }


    /**
     * 记录失败用户锁定
     *
     * @param username
     */
    public void lockRecord(String username) {

        lockRecord(RedisKeyConstants.LOGIN_FAIL_COUNT, username);
        // String key = RedisKeyConstants.LOGIN_FAIL_COUNT + username;
        // //锁定多少秒，默认30分钟  =  30 * 60 = 1800 秒
        // int timeOut = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(SystemConstant.LOGIN_FAIL_LOCK_TIME), 1800);
        // Integer count = (Integer) RedisUtils.get(key);
        // if (ObjectUtil.isEmpty(count)) {
        //     RedisUtils.set(key, 1, timeOut);
        //     return;
        // }
        // //次数加1 , 并重新设置当前key的过期时间
        // RedisUtils.set(key, count + 1, timeOut);
    }

    /**
     * 校验当前登录用户锁定
     *
     * @param username
     */
    public void isLocked(String username) {
        isLocked(RedisKeyConstants.LOGIN_FAIL_COUNT, username, AUTH_LOGIN_USER_LOCKED);
        // String key = RedisKeyConstants.LOGIN_FAIL_COUNT + username;
        // Integer count = (Integer) RedisUtils.get(key);
        // // 当前key不存在直接返回
        // if (ObjectUtil.isEmpty(count)) {
        //     return;
        // }
        // int failCount = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(SystemConstant.LOGIN_FAIL_LOCK_COUNT), 5);
        // if (count >= failCount) {
        //     // 获取key 剩余时长，单位秒
        //     long time = RedisUtils.getExpire(key);
        //     throw exception(AUTH_LOGIN_USER_LOCKED, Math.ceilDiv(time, 60));
        // }
    }

    /**
     * 校验api授权登陆nonce是否重复
     *
     * @param nonce
     * @param expireSeconds
     */
    public boolean setNonceExpire(String nonce, long expireSeconds) {
        if (StringUtils.isBlank(nonce) || expireSeconds <= 0) {
            return false;
        }
        String key = RedisKeyConstants.LOGIN_API_NONCE + nonce;
        Object exist = RedisUtils.get(key);
        // 当前key不存在直接返回
        if (exist != null) {
            return false;
        }
        RedisUtils.set(key, 1, expireSeconds);
        return true;
    }

    /**
     * 释放锁
     *
     * @param lockKey
     * @param lockValue
     * @return
     */
    public void releaseLock(String lockKey, String lockValue) {
        RedisUtils.del(lockKey + lockValue);
    }


}
