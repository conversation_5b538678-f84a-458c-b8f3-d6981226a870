package cn.iocoder.yudao.module.system.mq.producer.tenant;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageCostSaveEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 门店套餐额度
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = TenantPackageCostSaveEvent.TOPIC
)
public class TenantPackageCostProducer extends EventBusRocketMQTemplate {

}
