package cn.iocoder.yudao.module.system.service.auth;

import cn.iocoder.yudao.module.system.controller.admin.auth.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;

import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 管理后台的认证 Service 接口
 * <p>
 * 提供用户的登录、登出的能力
 *
 * <AUTHOR>
 */
public interface AdminAuthService {

    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    AdminUserDO authenticate(String username, String password);

    /**
     * 校验用户密码 + 锁
     *
     * @param userId
     * @param password
     * @return
     */
    AdminUserDO authUserPassword(Long userId, String password);

    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO login(@Valid AuthLoginReqVO reqVO);

    /**
     * 基于 token 退出登录
     *
     * @param token   token
     * @param logType 登出类型
     */
    void logout(String token, Integer logType);

    /**
     * 短信验证码发送
     *
     * @param reqVO 发送请求
     */
    void sendSmsCode(AuthSmsSendReqVO reqVO);

    /**
     * 短信登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO);

    /**
     * 社交快捷登录，使用 code 授权码
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO socialLogin(@Valid AuthSocialLoginReqVO reqVO);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AuthLoginRespVO refreshToken(String refreshToken);

    /**
     * 多门店根据userid 和 tenantId 创建最终token
     *
     * @param reqVO
     * @return
     */
    AuthLoginRespVO loginWithTenantId(@Valid AuthLoginTenantReqVO reqVO);

    /**
     * 根据OA账号登录
     *
     * @param reqVO
     * @return
     */
    AuthLoginRespVO OALogin(@Valid AuthLoginReqVO reqVO);

    /**
     * 根据三方应用授权登录
     *
     * @param reqVO
     * @return
     */
    AuthLoginRespVO apiLogin(@Valid AuthLoginApiReqVO reqVO);

    /**
     * 强制退出userId
     *
     * @param userId  用户id
     * @param logType 退出类型
     */
    void logout(Long userId, LoginLogTypeEnum logType);

    /**
     * 强制退出userId
     *
     * @param userId    用户id
     * @param tenantIds 门店ids
     * @param logType   退出类型
     */
    void logout(Long userId, List<Long> tenantIds, LoginLogTypeEnum logType);

    /**
     * 注销登录
     *
     * @param token
     */
    void logoff(String token);
}
