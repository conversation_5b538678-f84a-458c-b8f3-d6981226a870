package cn.iocoder.yudao.module.system.service.oauth2;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.oauth2.OAuth2AccessTokenMapper;
import cn.iocoder.yudao.module.system.dal.mysql.oauth2.OAuth2RefreshTokenMapper;
import cn.iocoder.yudao.module.system.dal.redis.oauth2.OAuth2AccessTokenRedisDAO;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import cn.iocoder.yudao.module.system.util.JwtUtil;
import jakarta.annotation.Resource;
import java.security.PrivateKey;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.jose4j.json.JsonUtil;
import org.jose4j.jwk.RsaJsonWebKey;
import org.jose4j.lang.JoseException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * OAuth2.0 Token Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OAuth2TokenServiceImpl implements OAuth2TokenService {

    @Resource
    private OAuth2AccessTokenMapper oauth2AccessTokenMapper;
    @Resource
    private OAuth2RefreshTokenMapper oauth2RefreshTokenMapper;

    @Resource
    private OAuth2AccessTokenRedisDAO oauth2AccessTokenRedisDAO;

    @Resource
    private OAuth2ClientService oauth2ClientService;
    @Resource
    @Lazy // 懒加载，避免循环依赖
    private AdminUserService adminUserService;

    @Override
    @Transactional
    public OAuth2AccessTokenDO createAccessToken(Long userId, Integer userType, String clientId, List<String> scopes) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        // 创建刷新令牌
        OAuth2RefreshTokenDO refreshTokenDO = createOAuth2RefreshToken(userId, userType, clientDO, scopes);
        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO refreshAccessToken(String refreshToken, String clientId) {
        // 查询访问令牌
        OAuth2RefreshTokenDO refreshTokenDO = oauth2RefreshTokenMapper.selectByRefreshToken(refreshToken);
        if (refreshTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "无效的刷新令牌");
        }

        // 校验 Client 匹配
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        if (ObjectUtil.notEqual(clientId, refreshTokenDO.getClientId())) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "刷新令牌的客户端编号不正确");
        }

        // 移除相关的访问令牌
        List<OAuth2AccessTokenDO> accessTokenDOs = oauth2AccessTokenMapper.selectListByRefreshToken(refreshToken);
        if (CollUtil.isNotEmpty(accessTokenDOs)) {
            oauth2AccessTokenMapper.deleteBatchIds(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getId));
            oauth2AccessTokenRedisDAO.deleteList(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getAccessToken));
        }

        // 已过期的情况下，删除刷新令牌
        if (DateUtils.isExpired(refreshTokenDO.getExpiresTime())) {
            oauth2RefreshTokenMapper.deleteById(refreshTokenDO.getId());
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "刷新令牌已过期");
        }

        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO getAccessToken(String accessToken) {
        // 优先从 Redis 中获取
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenRedisDAO.get(accessToken);
        if (accessTokenDO != null) {
            return accessTokenDO;
        }

        // 获取不到，从 MySQL 中获取
        accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        // 如果在 MySQL 存在，则往 Redis 中写入
        if (accessTokenDO != null && !DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            oauth2AccessTokenRedisDAO.set(accessTokenDO);
        }
        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO checkAccessToken(String accessToken) {
        OAuth2AccessTokenDO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }
        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO removeAccessToken(String accessToken) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        if (accessTokenDO == null) {
            return null;
        }
        oauth2AccessTokenMapper.deleteById(accessTokenDO.getId());
        oauth2AccessTokenRedisDAO.delete(accessToken);
        // 删除刷新令牌
        oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
        return accessTokenDO;
    }

    @Override
    public PageResult<OAuth2AccessTokenDO> getAccessTokenPage(OAuth2AccessTokenPageReqVO reqVO) {
        return oauth2AccessTokenMapper.selectPage(reqVO);
    }


    @Override
    public int removeAccessTokenByUserId(Long userId) {
        List<OAuth2AccessTokenDO> accessTokenDOS = oauth2AccessTokenMapper.selectAvailableByUserId(userId);
        accessTokenDOS.forEach(accessTokenDO -> {
            oauth2AccessTokenMapper.deleteById(accessTokenDO.getId());
            oauth2AccessTokenRedisDAO.delete(accessTokenDO.getJwtToken());
        });
        oauth2RefreshTokenMapper.selectAvailableByUserId(userId).forEach(refreshTokenDO -> {
            oauth2RefreshTokenMapper.deleteById(refreshTokenDO.getId());
        });
        return accessTokenDOS.size();
    }


    @Override
    @TenantIgnore
    public int removeAccessTokenByUserIdTenants(Long userId, List<Long> tenantIds) {
        log.info("removeAccessTokenByUserIdTenants userId:{},tenantIds:{}", userId, tenantIds);
        if (CollUtil.isEmpty(tenantIds)) {
            return 0;
        }

        List<OAuth2AccessTokenDO> accessTokenDOS = oauth2AccessTokenMapper.selectAvailableByUserIdTenantIds(userId, tenantIds);
        if (CollUtil.isEmpty(accessTokenDOS)) {
            return 0;
        }

        int i = oauth2AccessTokenMapper.deleteByIds(convertSet(accessTokenDOS, OAuth2AccessTokenDO::getId));
        oauth2AccessTokenRedisDAO.deleteList(convertSet(accessTokenDOS, OAuth2AccessTokenDO::getJwtToken));

        List<OAuth2RefreshTokenDO> refreshTokenDOS = oauth2RefreshTokenMapper.selectAvailableByUserIdTenants(userId, tenantIds);
        if (CollUtil.isNotEmpty(refreshTokenDOS)) {
            oauth2RefreshTokenMapper.deleteByIds(convertSet(refreshTokenDOS, OAuth2RefreshTokenDO::getId));
        }
        return i;
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO, OAuth2ClientDO clientDO) {
        String user = refreshTokenDO.getUserType() + ":" + refreshTokenDO.getUserId();
        String originToken = JwtUtil.originToken();
        String jwt = JwtUtil.builder(user, originToken, clientDO.getAccessTokenValiditySeconds());
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(originToken).setJwtToken(jwt)
            .setUserId(refreshTokenDO.getUserId()).setUserType(refreshTokenDO.getUserType())
            .setUserInfo(buildUserInfo(refreshTokenDO.getUserId(), refreshTokenDO.getUserType()))
            .setClientId(clientDO.getClientId()).setScopes(refreshTokenDO.getScopes())
            .setRefreshToken(JwtUtil.originToken(refreshTokenDO.getRefreshToken()))
            .setExpiresTime(LocalDateTime.now().plusSeconds(clientDO.getAccessTokenValiditySeconds()));
        // 手动设置门店编号，避免缓存到 Redis 的时候，无对应的门店编号
        accessTokenDO.setTenantId(TenantContextHolder.getTenantId());
        // 入库使用原token(非jwt)
        oauth2AccessTokenMapper.insert(accessTokenDO);
        // 缓存和请求使用jwt
        accessTokenDO.setAccessToken(jwt);
        // 记录到 Redis 中
        oauth2AccessTokenRedisDAO.set(accessTokenDO);
        return accessTokenDO;
    }

    private OAuth2RefreshTokenDO createOAuth2RefreshToken(Long userId, Integer userType, OAuth2ClientDO clientDO, List<String> scopes) {
        String user = userType + ":" + userId;
        String originToken = JwtUtil.originToken();
        String jwt = JwtUtil.builder(user, originToken, clientDO.getRefreshTokenValiditySeconds());
        OAuth2RefreshTokenDO refreshTokenDO = new OAuth2RefreshTokenDO().setRefreshToken(originToken)
            .setUserId(userId).setUserType(userType)
            .setClientId(clientDO.getClientId()).setScopes(scopes)
            .setExpiresTime(LocalDateTime.now().plusSeconds(clientDO.getRefreshTokenValiditySeconds()));
        refreshTokenDO.setTenantId(TenantContextHolder.getTenantId());
        // 入库使用原token(非jwt)
        oauth2RefreshTokenMapper.insert(refreshTokenDO);
        // 缓存和请求使用jwt
        refreshTokenDO.setRefreshToken(jwt);
        return refreshTokenDO;
    }

    /**
     * 加载用户信息，方便 {@link cn.iocoder.yudao.framework.security.core.LoginUser} 获取到昵称、部门等信息
     *
     * @param userId   用户编号
     * @param userType 用户类型
     * @return 用户信息
     */
    private Map<String, String> buildUserInfo(Long userId, Integer userType) {
        if (userType.equals(UserTypeEnum.ADMIN.getValue())) {
            AdminUserDO user = adminUserService.getUserStore(userId);
            return MapUtil.builder(LoginUser.INFO_KEY_NICKNAME, user.getNickname())
                .put(LoginUser.INFO_KEY_DEPT_ID, StrUtil.toStringOrNull(user.getDeptId())).build();
        } else if (userType.equals(UserTypeEnum.MEMBER.getValue())) {
            // 注意：目前 Member 暂时不读取，可以按需实现
            return Collections.emptyMap();
        }
        return null;
    }


    public static void main(String[] args) throws JoseException {
        String keyId = "wenzhen";
        //使用本文 2.1 节生成的 Keypair
        String privateKeyJson =
            "{\n" + "    \"p\": \"9xlTQSY7VKOMVelWAcTuqK9ME6P2iq-XNZ5jvljz_F83MyructfO87-TQNOsdYDF-4VFur-0HsNGSE3in7JgYOfqTXXV_7NhARGy2U9J3EHG7U4tn5V6NY7wCw3FhF4wxIZLtdvv23CuJ8zCodpcsiRIbw7B-1fj8zw93ssgxbU\",\n" + "    \"kty\": \"RSA\",\n"
                + "    \"q\": \"9OhrEDTHUdLTyo6f33LuAsu68tPtC4QcsnRmeWhj45NzuEqNKHpI1MpWykfB93_N717GBCuNbr77jmmjVQTryGqyEUgnAwcANABtHvcooaRrGSYKxYaziMHuxGUEsgU4XC2jwScbqv5CP9Toc7BA8jbgiWeCD1LcS5jQtE-rzf8\",\n"
                + "    \"d\": \"LZezXnRILxTj3iVQBNuMo22nO3Yb3VTU1q61MtSv9Ecv6SeUjdqn93KarC9AUv3EkXtGI59zUu5ufAbiqXsUziBUBTM1xiFrr1Z2vrY-fzt5rZUN-kXBvNYXZ1tnuexsxW5_1oS0yNfIwzFPFr32YD_biok297QSm3uS5Pk_Oy1577kAKwhuiuAuwG_dvJUIVvUDuxusradhDoQ-S4-nltO6uYyWcGEmJRxGoK857h8F9B6_2zB5Rdt7xQ011gsWKBsaOF9HozI57_sdhGI3q457qDeCpNAQWnsC7JCVWuagwL1E6bWne4RFVCD-NI8lpgKRnMoNabWPTHwIBSTRAQ\",\n"
                + "    \"e\": \"AQAB\",\n" + "    \"kid\": \"wenzhen\",\n"
                + "    \"qi\": \"vFHTgfPTmT3Cnx8lug1P4amXq7BHrwKfoKrLvx7Wur1SxUFjnzJ_Gx0J7c5xxzAu97OOWSQR2NNtsve_3oxKs0Cf5alWLpXYMVIYZHMZiHzN78jtpgEFit0l2ftOZ7iYSUFJ5WBNrYdZ8vuHWkOhuowK3tR6MNqGMvsoDD11LSQ\",\n"
                + "    \"dp\": \"wdLUsmLZ4T9UokMgmsyt5BV1GGbhydGIS5_NX48I-2p4syM23VCY3ivkxtIDEjyLkArvMliTYhoMtESbwma6IOlpqY8H6MX9DkiiwMDoD-RstNJBCU_IB-DyO8l2qKj1mgI5uxIlKD1P7po4BZtyYK4DP1IVKSG9BZu4cF1nfI0\",\n" + "    \"alg\": \"PS512\",\n"
                + "    \"dq\": \"lnMA8l-54mYlfVXImLg9xrHpEqtl_6EVD_u5x_N9CZzXhesmdCtK-o2ia3lRiv2UQOxzsLQgmlvwpTf4Ao6XWfNRmJQ-gvmWAlwQrOkgqMwcjsJz84wgVm7sgvGbxNC4IshWxJ2FvbGceSIN-qPYAfP8U-ymVUuN0VL7Qwl-fXk\",\n"
                + "    \"n\": \"7GR5pMXOKhE8AsdBBB92lPKbFK91szbO-j0pgnf0hAiCmW17ksdotgGpxysHLK4rFWJXeKj4mFbfzodtNmURr2pVA2MOxX4Xq8pUrpoxCuVIxVI4DBDFE0cNjvaVfsmx8P91aG-zzGgNgiX1yx65-AZlUHX8IUbaE7rxwLti7NJj2Uvs-0P6dWClV4cEd8GMnVK-2wlVlK0TSqJLhLRLBBJSxqTCyx4SjI5AMJz64flti__WILspgWGg73Ec9tSCgFPUcWF3b7lKgLp9xh4ooPkjbiCimu_0UmWQFoGp-6ISmAbpOBRMc7a9rNBlscW0_LQkhRWVwsp5M-jAft3gSw\"\n"
                + "}";
        // JwtClaims claims = new JwtClaims();
        // claims.setGeneratedJwtId();
        // claims.setIssuedAtToNow();
        // //过期时间一定要设置
        // NumericDate date = NumericDate.now();
        // date.addSeconds(120*60);
        // claims.setExpirationTime(date);
        // claims.setNotBeforeMinutesInThePast(1);
        // claims.setSubject("YOUR_SUBJECT");
        // claims.setAudience("YOUR_AUDIENCE");
        // //添加自定义参数，所有值请都使用String类型
        // claims.setClaim("userId", "1213234");
        // claims.setClaim("email", "<EMAIL>");
        // JsonWebSignature jws = new JsonWebSignature();
        // jws.setAlgorithmHeaderValue(AlgorithmIdentifiers.RSA_PSS_USING_SHA512);
        // jws.setKeyIdHeaderValue(keyId);
        // jws.setPayload(claims.toJson());
        PrivateKey privateKey = new RsaJsonWebKey(JsonUtil.parseJson(privateKeyJson)).getPrivateKey();

        System.out.println("私钥: " + privateKey);
    }

}
