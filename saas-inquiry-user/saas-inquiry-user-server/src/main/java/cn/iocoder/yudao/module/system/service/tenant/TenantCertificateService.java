package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantCertificateDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 门店资质证件信息 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantCertificateService {

    /**
     * 创建门店资质证件信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantCertificate(@Valid TenantCertificateSaveReqVO createReqVO);

    /**
     * 更新门店资质证件信息
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantCertificate(@Valid TenantCertificateSaveReqVO updateReqVO);

    /**
     * 删除门店资质证件信息
     *
     * @param id 编号
     */
    void deleteTenantCertificate(Long id);

    /**
     * 获得门店资质证件信息
     *
     * @param id 编号
     * @return 门店资质证件信息
     */
    TenantCertificateDO getTenantCertificate(Long id);

    /**
     * 创建或者修改证件信息
     *
     * @param certificates 证件列表
     */
    void createOrUpdateTenantCertificates(List<TenantCertificateSaveReqVO> certificates);

    /**
     * 根据门店id获取证件信息
     *
     * @param tenantId
     * @return
     */
    List<TenantCertificateRespVO> getTenantCertificates(Long tenantId);

    List<TenantCertificateRespVO> getTenantCertificates(List<Long> tenantIds);
}