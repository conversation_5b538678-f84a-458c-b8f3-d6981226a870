package cn.iocoder.yudao.module.system.service.tenant.servicepack;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_CATALOG_DISABLE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_CATALOG_IDS_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_CATALOG_PROVINCE_NO_SAME;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_PACK_IDS_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_PACK_INFO_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_PACK_NOT_REPEAT;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_PROVINCE_NO_SAME;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_SERVICE_PACK_RELATION;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_SERVICE_PACK_RELATION_CHANGE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_SERVICE_PACK_RELATION_OPERATE_SUB_TYPE;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.api.logger.dto.OperateLogCreateReqDTO;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationBatchChangeVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationDetailRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationItemVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantServicePackRelationConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantTransmissionServicePackRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantTransmissionServicePackRelationMapper;
import cn.iocoder.yudao.module.system.service.logger.IOperateLogService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;
import com.xyy.saas.inquiry.product.api.catalog.CatalogApi;
import com.xyy.saas.inquiry.product.api.catalog.dto.CatalogRespDTO;
import com.xyy.saas.inquiry.util.UserUtil;
import com.xyy.saas.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganNetworkConfigDTO;
import com.xyy.saas.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 门店-开通服务包关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantTransmissionServicePackRelationServiceImpl implements TenantTransmissionServicePackRelationService {

    @Resource
    private TenantTransmissionServicePackRelationMapper tenantTransmissionServicePackRelationMapper;

    @Resource
    @Lazy
    private TenantService tenantService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private IOperateLogService iOperateLogService;

    @Resource
    @Lazy
    private TransmissionServicePackApi transmissionServicePackApi;

    @Resource
    private CatalogApi catalogApi;

    @Resource
    @Lazy
    private TransmissionOrganApi transmissionOrganApi;


    public Map<Integer, Long> selectCountByOrgans(List<Integer> organIds, Integer status) {
        if (CollUtil.isEmpty(organIds)) {
            return Map.of();
        }
        return tenantTransmissionServicePackRelationMapper.selectCountByOrgans(organIds, status).stream().collect(Collectors.toMap(CommonGroupStatisticsDto::getIntKey, CommonGroupStatisticsDto::getCountValue));
    }

    public Map<Integer, Long> selectCountByServicePacks(List<Integer> servicePackIds, Integer status) {
        if (CollUtil.isEmpty(servicePackIds)) {
            return Map.of();
        }
        return tenantTransmissionServicePackRelationMapper.selectCountByServicePacks(servicePackIds, status).stream().collect(Collectors.toMap(CommonGroupStatisticsDto::getIntKey, CommonGroupStatisticsDto::getCountValue));
    }

    public Map<Long, Long> selectCountByCatalogIds(List<Long> catalogIds, Integer status) {
        if (CollUtil.isEmpty(catalogIds)) {
            return Map.of();
        }

        List<CommonGroupStatisticsDto> commonGroupStatisticsDtos = tenantTransmissionServicePackRelationMapper.selectCountByCatalogIds(catalogIds, status);

        if (CollUtil.isEmpty(commonGroupStatisticsDtos)) {
            return Map.of();
        }

        return commonGroupStatisticsDtos.stream()
            .filter(item -> item.getLongKey() != null && item.getCountValue() != null)
            .collect(Collectors.toMap(CommonGroupStatisticsDto::getLongKey, CommonGroupStatisticsDto::getCountValue, (v1, v2) -> v2));
    }

    @Override
    public List<TenantTransmissionServicePackRelationDO> selectByCondition(TenantServicePackRelationPageReqVO reqVO) {
        return tenantTransmissionServicePackRelationMapper.selectByCondition(reqVO);
    }


    @Override
    public TenantTransmissionServicePackRelationDO selectTenantServicePack(TenantServicePackRelationPageReqVO reqVO) {
        return tenantTransmissionServicePackRelationMapper.selectOneByCondition(reqVO);
    }


    @Override
    public TenantServicePackRelationDetailRespVO getTenantTransmissionServicePackRelation(Long tenantId) {
        TenantDO tenantDO = tenantService.getRequiredTenant(tenantId);
        TenantServicePackRelationDetailRespVO packRelationRespVO = TenantServicePackRelationDetailRespVO.builder().tenantDto(TenantConvert.INSTANCE.convertDto(tenantDO)).build();

        List<TenantTransmissionServicePackRelationDO> relationDOS = tenantTransmissionServicePackRelationMapper.selectByCondition(TenantServicePackRelationPageReqVO.builder().tenantId(tenantId).build());
        if (CollUtil.isEmpty(relationDOS)) {
            return packRelationRespVO;
        }
        List<TenantServicePackRelationItemVO> servicePackRelationItemVOS = TenantServicePackRelationConvert.INSTANCE.convertVos(relationDOS);

        // 组装服务包数据
        // fillServicePackInfo(relationDOS);
        // 组装目录信息
        fillCatalogInfo(servicePackRelationItemVOS);

        // 转化出参对象
        TenantServicePackRelationConvert.INSTANCE.convertRespVo(packRelationRespVO, servicePackRelationItemVOS);
        return packRelationRespVO;
    }

    private void fillCatalogInfo(List<TenantServicePackRelationItemVO> servicePackRelationItemVOS) {
        List<Long> catalogs = CollectionUtils.convertList(servicePackRelationItemVOS, TenantServicePackRelationItemVO::getCatalogId);
        if (CollUtil.isNotEmpty(catalogs)) {
            Map<Long, String> catalogMap = catalogApi.getCatalogByIdList(catalogs).stream().collect(Collectors.toMap(CatalogRespDTO::getId, CatalogRespDTO::getPref, (a, b) -> b));
            for (TenantServicePackRelationItemVO relationItemVO : servicePackRelationItemVOS) {
                relationItemVO.setCatalogPref(catalogMap.get(relationItemVO.getCatalogId()));
            }
        }
    }

    private void fillServicePackInfo(List<TenantTransmissionServicePackRelationDO> relationDOS) {
        List<Integer> servicePackIds = CollectionUtils.convertList(relationDOS, TenantTransmissionServicePackRelationDO::getServicePackId);
        if (CollUtil.isNotEmpty(servicePackIds)) {
            Map<Integer, TenantTransmissionServicePackRespDTO> servicePackRespDTOMap = transmissionServicePackApi.selectServicePacksByIds(servicePackIds.stream().distinct().toList()).stream()
                .collect(Collectors.toMap(TenantTransmissionServicePackRespDTO::getId, Function.identity(), (a, b) -> b));
            for (TenantTransmissionServicePackRelationDO relationDO : relationDOS) {
                relationDO.setOrganId(servicePackRespDTOMap.getOrDefault(relationDO.getServicePackId(), new TenantTransmissionServicePackRespDTO()).getOrganId());
            }
        }

    }


    @Override
    public PageResult<TenantRespVO> getTransmissionServicePackRelationTenantPage(TenantServicePackRelationPageReqVO pageReqVO) {

        IPage<TenantRespVO> packRelationPage = tenantTransmissionServicePackRelationMapper.getTenantServicePackRelationPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);

        return new PageResult<>(packRelationPage.getRecords(), packRelationPage.getTotal());
    }

    @Override
    public PageResult<TenantServicePackRelationRespVO> getTenantTransmissionServicePackRelationPage(TenantServicePackRelationPageReqVO pageReqVO) {
        if (StringUtils.isNotBlank(pageReqVO.getServicePackName())) {
            // 查服务包ids
            List<TenantTransmissionServicePackRespDTO> packRespDTOS = transmissionServicePackApi.selectServicePacks(TransmissionServicePackDTO.builder().name(pageReqVO.getServicePackName()).organType(pageReqVO.getOrganType()).build());
            if (CollUtil.isEmpty(packRespDTOS)) {
                return PageResult.empty();
            }
            pageReqVO.setServicePackIds(CollectionUtils.convertList(packRespDTOS, TenantTransmissionServicePackRespDTO::getId));
        }
        IPage<TenantRespVO> packRelationPage = tenantTransmissionServicePackRelationMapper.getTenantServicePackRelationPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        if (packRelationPage == null || CollUtil.isEmpty(packRelationPage.getRecords())) {
            return PageResult.empty();
        }

        List<TenantServicePackRelationRespVO> relationRespVOS = TenantServicePackRelationConvert.INSTANCE.convertRelationVos(packRelationPage.getRecords());

        // 组装服务包更新人信息
        List<TenantTransmissionServicePackRelationDO> existsRelationDOS = fetchOldRelations(CollectionUtils.convertList(packRelationPage.getRecords(), TenantRespVO::getId), null, null);
        // 查服务包信息
        Map<Integer, TenantTransmissionServicePackRespDTO> servicePackRespDTOMap = fetchServicePackInfo(existsRelationDOS.stream().distinct().map(TenantTransmissionServicePackRelationDO::getServicePackId).toList());

        // 组装列表字段
        Map<Long, List<TenantTransmissionServicePackRelationDO>> servicePackRelationMap = existsRelationDOS.stream().collect(Collectors.groupingBy(TenantTransmissionServicePackRelationDO::getTenantId));

        for (TenantServicePackRelationRespVO record : relationRespVOS) {
            List<TenantTransmissionServicePackRelationDO> relationDOS = servicePackRelationMap.getOrDefault(record.getTenantId(), List.of());

            List<Integer> servicePacks = CollectionUtils.convertList(relationDOS, TenantTransmissionServicePackRelationDO::getServicePackId);
            record.setServicePackNames(servicePackRespDTOMap.entrySet().stream().filter(s -> servicePacks.contains(s.getKey())).map(e -> e.getValue().getName()).toList());

            record.setMedicalInsuranceStatus(TenantServicePackRelationStatusEnum.convertBoolStatus(
                relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.MEDICAL_INSURANCE.getCode())).anyMatch(s -> TenantServicePackRelationStatusEnum.isOpen(s.getStatus()))));
            record.setDrugSupervisionStatus(TenantServicePackRelationStatusEnum.convertBoolStatus(
                relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.DRUG_SUPERVISION.getCode())).anyMatch(s -> TenantServicePackRelationStatusEnum.isOpen(s.getStatus()))));
            record.setInternetHospitalStatus(TenantServicePackRelationStatusEnum.convertBoolStatus(
                relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.INTERNET_SUPERVISION.getCode())).anyMatch(s -> TenantServicePackRelationStatusEnum.isOpen(s.getStatus()))));
            record.setErpStatus(TenantServicePackRelationStatusEnum.convertBoolStatus(
                relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.ERP.getCode())).anyMatch(s -> TenantServicePackRelationStatusEnum.isOpen(s.getStatus()))));
            record.setHisStatus(TenantServicePackRelationStatusEnum.convertBoolStatus(
                relationDOS.stream().filter(s -> Objects.equals(s.getOrganType(), OrganTypeEnum.HIS.getCode())).anyMatch(s -> TenantServicePackRelationStatusEnum.isOpen(s.getStatus()))));

            // 最后更新人
            relationDOS.stream().max(Comparator.comparing(BaseDO::getUpdateTime)).ifPresent(s -> {
                record.setCreator(s.getCreator());
                record.setUpdater(s.getUpdater());
                record.setUpdateTime(s.getUpdateTime());
            });
        }
        // 组装用户信息
        UserUtil.fillUserInfo(relationRespVOS, adminUserService::getUserMap);
        return new PageResult<>(relationRespVOS, packRelationPage.getTotal());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createTenantTransmissionServicePackRelation(TenantServicePackRelationSaveReqVO createReqVO) {
        List<TenantServicePackRelationItemVO> newRelationList = TenantServicePackRelationConvert.INSTANCE.convertSaveList(createReqVO);
        if (CollUtil.isEmpty(newRelationList)) {
            return 0;
        }
        // 校验服务包重复
        validateServicePackDuplicates(newRelationList);

        List<TenantTransmissionServicePackRelationDO> oldRelationDOS = fetchOldRelations(Collections.singletonList(createReqVO.getTenantId()), null, null);

        Map<Integer, TenantTransmissionServicePackRespDTO> servicePackRespDTOMap = fetchServicePackInfo(newRelationList.stream().map(TenantServicePackRelationItemVO::getServicePackId).distinct().toList());
        // 构建relation关系DO
        List<TenantTransmissionServicePackRelationDO> newRelationDos = buildNewRelations(newRelationList, oldRelationDOS, servicePackRespDTOMap, false);

        // 记录操作日志
        recordServicePackRelationLog(SYSTEM_TENANT_SERVICE_PACK_RELATION_OPERATE_SUB_TYPE, newRelationDos, oldRelationDOS);

        List<Integer> deleteIds = CollectionUtils.convertList(oldRelationDOS, TenantTransmissionServicePackRelationDO::getId);
        if (CollUtil.isNotEmpty(deleteIds)) {
            tenantTransmissionServicePackRelationMapper.deleteIds(deleteIds);
        }
        tenantTransmissionServicePackRelationMapper.insert(newRelationDos);

        return newRelationDos.size();
    }


    private void validateServicePackDuplicates(List<TenantServicePackRelationItemVO> tspVos) {
        Map<Integer, List<TenantServicePackRelationItemVO>> listMap = tspVos.stream()
            .collect(Collectors.groupingBy(a -> a.getServicePackId() + a.getOrganType()));
        String repeatMsg = listMap.values().stream()
            .filter(a -> a.size() > 1)
            .flatMap(List::stream)
            .map(TenantServicePackRelationItemVO::getOrganType)
            .distinct()
            .map(OrganTypeEnum::getServicePackDesc)
            .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(repeatMsg)) {
            throw exception(TENANT_TRANSMISSION_SERVICE_PACK_NOT_REPEAT, repeatMsg);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchChangeServicePack(TenantServicePackRelationBatchChangeVO changeVO) {
        if (changeVO.getServicePackId() == null) {
            throw exception(TENANT_TRANSMISSION_SERVICE_PACK_IDS_NOT_EXISTS);
        }

        List<TenantServicePackRelationItemVO> newRelations = TenantServicePackRelationConvert.INSTANCE.convertChangeList(changeVO);

        // 获取旧服务包信息
        List<TenantTransmissionServicePackRelationDO> oldRelationDOS = fetchOldRelations(changeVO.getTenantIds(), changeVO.getOrganType(), null);

        Map<Integer, TenantTransmissionServicePackRespDTO> servicePackRespDTOMap = fetchServicePackInfo(Collections.singletonList(changeVO.getServicePackId()));
        // 构建新服务包relationDos
        List<TenantTransmissionServicePackRelationDO> newRelationDos = buildNewRelations(newRelations, oldRelationDOS, servicePackRespDTOMap, true);

        recordServicePackRelationLog(SYSTEM_TENANT_SERVICE_PACK_RELATION_CHANGE_SUB_TYPE, newRelationDos, oldRelationDOS);

        List<Integer> deleteIds = CollectionUtils.convertList(oldRelationDOS, TenantTransmissionServicePackRelationDO::getId);
        if (CollUtil.isNotEmpty(deleteIds)) {
            tenantTransmissionServicePackRelationMapper.deleteIds(deleteIds);
        }
        tenantTransmissionServicePackRelationMapper.insertBatch(newRelationDos);

        return newRelationDos.size();
    }

    @NotNull
    private static List<TenantTransmissionServicePackRelationDO> buildNewRelations(List<TenantServicePackRelationItemVO> tspVos, List<TenantTransmissionServicePackRelationDO> oldRelationDOS
        , Map<Integer, TenantTransmissionServicePackRespDTO> servicePackRespDTOMap, boolean holdCatalog) {
        return tspVos.stream().map(newSp -> {

            TenantTransmissionServicePackRespDTO servicePackRespDTO = Optional.ofNullable(servicePackRespDTOMap.get(newSp.getServicePackId()))
                .orElseThrow(() -> exception(TENANT_TRANSMISSION_SERVICE_PACK_INFO_NOT_EXISTS, OrganTypeEnum.getServicePackDesc(newSp.getOrganType()), newSp.getServicePackId()));

            TenantTransmissionServicePackRelationDO relationDO = TenantServicePackRelationConvert.INSTANCE.convertDo(newSp, servicePackRespDTO);
            // 对比包变更时间 和 目录变更时间
            oldRelationDOS.stream().filter(a -> Objects.equals(a.getTenantId(), newSp.getTenantId())
                    && Objects.equals(a.getOrganType(), newSp.getOrganType()))
                .findAny().ifPresent(old -> {
                    if (holdCatalog) {
                        relationDO.setCatalogId(relationDO.getCatalogId() != null ? relationDO.getCatalogId() : old.getCatalogId());
                    }
                    relationDO.extGet().setServicePackChangeTime(Objects.equals(old.getServicePackId(), newSp.getServicePackId()) ? old.extGet().getServicePackChangeTime() : LocalDateTime.now());
                    relationDO.extGet().setCatalogChangeTime(Objects.equals(old.getCatalogId(), newSp.getCatalogId()) ? old.extGet().getCatalogChangeTime() : LocalDateTime.now());
                });

            if (CollUtil.isEmpty(oldRelationDOS)) {
                relationDO.extGet().setServicePackChangeTime(LocalDateTime.now());
                relationDO.extGet().setCatalogChangeTime(LocalDateTime.now());
            }

            return relationDO;
        }).collect(Collectors.toList());
    }


    /**
     * 批量修改服务目录 1.判断当前门店 在organType下是否存在servicePackId 如果不存在则给出提示 2.仅修改已存在的数据
     *
     * @param changeVO
     * @return
     */
    @Override
    public Integer batchChangeServiceCatalog(TenantServicePackRelationBatchChangeVO changeVO) {
        if (changeVO.getCatalogId() == null) {
            throw exception(TENANT_TRANSMISSION_SERVICE_CATALOG_IDS_NOT_EXISTS);
        }

        List<CatalogRespDTO> catalogRespDTOList = catalogApi.getCatalogByIdList(List.of(changeVO.getCatalogId()));
        if (CollUtil.isEmpty(catalogRespDTOList) || catalogRespDTOList.getFirst().getDisable()) {
            throw exception(TENANT_TRANSMISSION_SERVICE_CATALOG_DISABLE);
        }

        // 校验门店区域在不在目录区域
        validateTenantCatalogArea(changeVO.getTenantIds(), catalogRespDTOList.getFirst());

        List<TenantTransmissionServicePackRelationDO> oldRelationDOS = fetchOldRelations(changeVO.getTenantIds(), changeVO.getOrganType(), null);

        Map<Long, List<TenantTransmissionServicePackRelationDO>> tenantPackMap = oldRelationDOS.stream().collect(Collectors.groupingBy(TenantTransmissionServicePackRelationDO::getTenantId));
        // 如果不存在此服务包的给出提醒
        validateTenantServicePackExistence(changeVO, tenantPackMap);
        // 修改目录版本id + 切换时间
        List<TenantTransmissionServicePackRelationDO> changeList = oldRelationDOS.stream().map(s -> {
            TenantTransmissionServicePackRelationDO tenantTransmissionServicePackRelationDO = TenantServicePackRelationConvert.INSTANCE.convertDo2Do(s);
            // 当前后版本不一致则修改切换目录的时间
            if (!changeVO.getCatalogId().equals(s.getCatalogId())) {
                tenantTransmissionServicePackRelationDO.extGet().setCatalogChangeTime(Objects.equals(s.getCatalogId(), changeVO.getCatalogId()) ? s.extGet().getCatalogChangeTime() : LocalDateTime.now());
            }
            tenantTransmissionServicePackRelationDO.setCatalogId(changeVO.getCatalogId());
            return tenantTransmissionServicePackRelationDO;
        }).toList();

        if (CollUtil.isNotEmpty(changeList)) {
            tenantTransmissionServicePackRelationMapper.updateById(changeList);
        }
        // 记录日志
        recordServicePackRelationLog(SYSTEM_TENANT_SERVICE_PACK_RELATION_CHANGE_SUB_TYPE, changeList, oldRelationDOS);

        return changeList.size();
    }

    private void validateTenantCatalogArea(List<Long> tenantIds, CatalogRespDTO catalogRespDTO) {
        if (CollUtil.isEmpty(tenantIds) || catalogRespDTO == null) {
            return;
        }
        Set<String> areaCode = tenantService.getTenantList(tenantIds).stream().map(TenantDO::getProvince).collect(Collectors.toSet());
        if (CollUtil.size(areaCode) > 1) {
            throw exception(TENANT_TRANSMISSION_SERVICE_PROVINCE_NO_SAME); // 请选择同一省的门店后再进行切换操作哦~
        }

        if (!areaCode.contains(catalogRespDTO.getProvince())) {
            throw exception(TENANT_TRANSMISSION_SERVICE_CATALOG_PROVINCE_NO_SAME); // 请选择同一省的门店后再进行切换操作哦~
        }
    }

    private void validateTenantServicePackExistence(TenantServicePackRelationBatchChangeVO changeVO, Map<Long, List<TenantTransmissionServicePackRelationDO>> tenantPackMap) {
        Map<Long, TenantDO> tenantDOMap = tenantService.getTenantList(changeVO.getTenantIds()).stream()
            .collect(Collectors.toMap(TenantDO::getId, Function.identity(), (a, b) -> b));

        String errMsg = changeVO.getTenantIds().stream().filter(t -> !tenantPackMap.containsKey(t))
            .map(t -> tenantDOMap.get(t).getName()).collect(Collectors.joining(","));

        if (StringUtils.isNotBlank(errMsg)) {
            throw exception(TENANT_TRANSMISSION_SERVICE_PACK_INFO_NOT_EXISTS, errMsg, OrganTypeEnum.getServicePackDesc(changeVO.getOrganType()));
        }
    }


    private List<TenantTransmissionServicePackRelationDO> fetchOldRelations(List<Long> tenantIds, Integer organType, Integer servicePack) {
        return tenantTransmissionServicePackRelationMapper.selectByCondition(
            TenantServicePackRelationPageReqVO.builder().tenantIds(tenantIds).organType(organType).servicePackId(servicePack).build());
    }

    private Map<Integer, TenantTransmissionServicePackRespDTO> fetchServicePackInfo(List<Integer> servicePackIds) {
        if (CollUtil.isEmpty(servicePackIds)) {
            return Collections.emptyMap();
        }
        return transmissionServicePackApi.selectServicePacksByIds(servicePackIds).stream()
            .collect(Collectors.toMap(TenantTransmissionServicePackRespDTO::getId, Function.identity(), (a, b) -> b));
    }


    /**
     * 构建当前门店服务包变更日志
     * <p>
     * 1.记录服务包变更 servicePackId ,开通，关闭，移除，切换
     * </p>
     * <p>
     * 2.记录目录变更 catalogId,开通，关闭，移除，切换
     * </p>
     * <p>
     * 3.记录网络变更 ext。networkCode,开通，关闭，移除，切换
     * </p>
     *
     * @param subType        操作类型
     * @param newRelationDos 新关系
     * @param oldRelationDOS 旧关系
     * @return
     */
    private void recordServicePackRelationLog(String subType, List<TenantTransmissionServicePackRelationDO> newRelationDos, List<TenantTransmissionServicePackRelationDO> oldRelationDOS) {
        if (CollUtil.isEmpty(newRelationDos) && CollUtil.isEmpty(oldRelationDOS)) {
            return;
        }

        // 按 tenantId 分组处理
        Map<Long, List<TenantTransmissionServicePackRelationDO>> newRelationMap = Optional.ofNullable(newRelationDos)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(TenantTransmissionServicePackRelationDO::getTenantId));

        Map<Long, List<TenantTransmissionServicePackRelationDO>> oldRelationMap = Optional.ofNullable(oldRelationDOS)
            .orElse(new ArrayList<>()).stream()
            .collect(Collectors.groupingBy(TenantTransmissionServicePackRelationDO::getTenantId));

        // 获取所有 tenantId
        List<Long> tenantIds = Stream.of(newRelationMap.keySet(), oldRelationMap.keySet()).flatMap(Collection::stream).distinct().toList();

        Map<Integer, TenantTransmissionServicePackRespDTO> servicePackMap = getServicePackRespDTOMap(newRelationDos, oldRelationDOS);

        Map<Integer, TransmissionOrganDTO> organMap = getOrganMap(newRelationDos, oldRelationDOS);

        Map<Long, CatalogRespDTO> catalogMap = getCatalogMap(newRelationDos, oldRelationDOS);

        List<OperateLogCreateReqDTO> logs = new ArrayList<>();

        for (Long tenantId : tenantIds) {

            OperateLogCreateReqDTO reqDTO = getOperateLogCreateReqDTO(subType, tenantId);

            List<String> operateAction = new ArrayList<>();

            for (OrganTypeEnum organType : OrganTypeEnum.values()) {

                // 1.服务包状态变更
                List<TenantTransmissionServicePackRelationDO> newRelationList = newRelationMap.getOrDefault(tenantId, new ArrayList<>()).stream()
                    .filter(s -> Objects.equals(s.getOrganType(), organType.getCode())).toList();

                List<TenantTransmissionServicePackRelationDO> oldRelationList = oldRelationMap.getOrDefault(tenantId, new ArrayList<>()).stream()
                    .filter(s -> Objects.equals(s.getOrganType(), organType.getCode())).toList();

                if (CollUtil.isEmpty(newRelationList) && CollUtil.isEmpty(oldRelationList)) {
                    continue;
                }

                // 1.1.构建服务包状态变更日志
                TenantTransmissionServicePackRelationDO nRe = newRelationList.stream().filter(s -> Objects.equals(s.getOrganType(), organType.getCode())).findAny().orElse(new TenantTransmissionServicePackRelationDO());

                TenantTransmissionServicePackRelationDO oRe = oldRelationList.stream().filter(s -> Objects.equals(s.getOrganType(), organType.getCode())).findAny().orElse(new TenantTransmissionServicePackRelationDO());

                if (nRe.getStatus() != null && !Objects.equals(nRe.getStatus(), oRe.getStatus())) {
                    operateAction.add(TenantServicePackRelationStatusEnum.fromStatusCode(nRe.getStatus()).getOperateDesc() + ":" + organType.getServicePackDesc());
                }

                buildDiff("开通", organType, newRelationList, oldRelationList, operateAction, servicePackMap, organMap, catalogMap);

                buildDiff("移除", organType, oldRelationList, newRelationList, operateAction, servicePackMap, organMap, catalogMap);

                if (CollUtil.isNotEmpty(operateAction)) {
                    reqDTO.setAction(String.join(";", operateAction));
                    logs.add(reqDTO);
                }
            }
        }

        if (CollUtil.isNotEmpty(logs)) {
            iOperateLogService.batchCreateOperateLog(logs);
        }

    }

    @NotNull
    private static OperateLogCreateReqDTO getOperateLogCreateReqDTO(String subType, Long tenantId) {
        OperateLogCreateReqDTO reqDTO = new OperateLogCreateReqDTO();
        reqDTO.setType(SYSTEM_TENANT_SERVICE_PACK_RELATION); // 大模块类型
        reqDTO.setSubType(subType);// 操作名称，例如：转移客户
        reqDTO.setBizId(tenantId); // 业务编号，例如：客户编号
        // 3.填充日志其他信息
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser != null) {
            reqDTO.setUserId(loginUser.getId());
            reqDTO.setUserType(loginUser.getUserType());
        }
        // 获得 Request 对象 补全请求信息
        HttpServletRequest request = ServletUtils.getRequest();
        if (request != null) {
            reqDTO.setRequestMethod(request.getMethod());
            reqDTO.setRequestUrl(request.getRequestURI());
            reqDTO.setUserIp(ServletUtils.getClientIP(request));
            reqDTO.setUserAgent(ServletUtils.getUserAgent(request));
        }
        return reqDTO;
    }

    private static void buildDiff(String ope, OrganTypeEnum organType, List<TenantTransmissionServicePackRelationDO> newRelationList, List<TenantTransmissionServicePackRelationDO> oldRelationList, List<String> operateAction,
        Map<Integer, TenantTransmissionServicePackRespDTO> servicePackMap, Map<Integer, TransmissionOrganDTO> organMap, Map<Long, CatalogRespDTO> catalogMap) {
        for (TenantTransmissionServicePackRelationDO ndo : newRelationList) {
            // 2. 服务包变更
            if (ndo.getServicePackId() != null) {

                TenantTransmissionServicePackRelationDO odo = oldRelationList.stream().filter(o -> Objects.equals(o.getServicePackId(), ndo.getServicePackId())).findAny().orElse(null);
                if (odo == null) {
                    operateAction.add(ope + ":" + organType.getServicePackDesc() + "【" + servicePackMap.get(ndo.getServicePackId()).getName() + "】");
                } else {
                    // 3. 网络变更
                    if (StringUtils.isNotBlank(ndo.extGet().getNetworkCode()) && !StringUtils.equals(odo.extGet().getNetworkCode(), ndo.extGet().getNetworkCode()) && organMap.get(ndo.getServicePackId()) != null) {

                        TransmissionOrganNetworkConfigDTO nnt = organMap.get(ndo.getServicePackId()).getNetworkConfig().stream()
                            .filter(n -> StringUtils.equals(n.getCode(), ndo.extGet().getNetworkCode())).findAny().orElse(new TransmissionOrganNetworkConfigDTO());

                        operateAction.add(ope + ":" + servicePackMap.get(ndo.getServicePackId()).getName() + "网络:" + nnt.getName());
                    }
                }
            }
            // 4. 目录变更
            if (ndo.getCatalogId() != null) {
                if (oldRelationList.stream().noneMatch(o -> Objects.equals(o.getCatalogId(), ndo.getCatalogId()))) {
                    operateAction.add(ope + ":" + organType.getDesc() + "目录【" + catalogMap.getOrDefault(ndo.getCatalogId(), new CatalogRespDTO()).getName() + "(" + catalogMap.get(ndo.getCatalogId()).getVersionCode() + ")" + "】");
                }
            }
        }
    }

    private Map<Long, CatalogRespDTO> getCatalogMap(List<TenantTransmissionServicePackRelationDO> newRelationDos, List<TenantTransmissionServicePackRelationDO> oldRelationDOS) {
        List<Long> catalogIds = Stream.of(CollectionUtils.convertSet(newRelationDos, TenantTransmissionServicePackRelationDO::getCatalogId),
            CollectionUtils.convertSet(oldRelationDOS, TenantTransmissionServicePackRelationDO::getCatalogId)).flatMap(Collection::stream).distinct().toList();
        return CollUtil.isEmpty(catalogIds) ? new HashMap<>() : catalogApi.getCatalogByIdList(catalogIds).stream().collect(Collectors.toMap(CatalogRespDTO::getId, Function.identity(), (a, b) -> b));
    }

    private Map<Integer, TransmissionOrganDTO> getOrganMap(List<TenantTransmissionServicePackRelationDO> newRelationDos, List<TenantTransmissionServicePackRelationDO> oldRelationDOS) {
        List<Integer> organIds = Stream.of(CollectionUtils.convertSet(newRelationDos, TenantTransmissionServicePackRelationDO::getOrganId),
            CollectionUtils.convertSet(oldRelationDOS, TenantTransmissionServicePackRelationDO::getOrganId)).flatMap(Collection::stream).distinct().toList();

        return CollUtil.isEmpty(organIds) ? new HashMap<>() : transmissionOrganApi.getTransmissionOrgans(organIds).stream().collect(Collectors.toMap(TransmissionOrganDTO::getId, Function.identity(), (a, b) -> b));
    }

    private Map<Integer, TenantTransmissionServicePackRespDTO> getServicePackRespDTOMap(List<TenantTransmissionServicePackRelationDO> newRelationDos, List<TenantTransmissionServicePackRelationDO> oldRelationDOS) {
        List<Integer> servicePackIds = Stream.of(CollectionUtils.convertSet(newRelationDos, TenantTransmissionServicePackRelationDO::getServicePackId),
            CollectionUtils.convertSet(oldRelationDOS, TenantTransmissionServicePackRelationDO::getServicePackId)).flatMap(Collection::stream).distinct().toList();
        return CollUtil.isEmpty(servicePackIds) ? new HashMap<>() : fetchServicePackInfo(servicePackIds);
    }

    @Override
    public PageResult<CatalogRelationTenantDto> getCatalogRelationTenantPage(TenantServicePackRelationReqDto reqDto) {

        IPage<CatalogRelationTenantDto> packRelationPage = tenantTransmissionServicePackRelationMapper.getCatalogRelationTenantPage(MyBatisUtils.buildPage(reqDto), reqDto);

        if (packRelationPage == null || packRelationPage.getTotal() == 0) {
            return PageResult.empty();
        }

        return new PageResult<>(packRelationPage.getRecords(), packRelationPage.getTotal());
    }
}