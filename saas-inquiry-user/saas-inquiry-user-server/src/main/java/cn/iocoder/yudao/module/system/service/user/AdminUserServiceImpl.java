package cn.iocoder.yudao.module.system.service.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.OA_LOGIN_USER_INFO_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_COUNT_MAX;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_EMAIL_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_HAS_ROLE_NO_ALLOW_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_IS_DISABLE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_IS_QUIT;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_MOBILE_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_PASSWORD_FAILED;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_USERNAME_EXISTS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_CREATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_CREATE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_DELETE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_DELETE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_UPDATE_PASSWORD_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_USER_UPDATE_PASSWORD_SUCCESS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.datapermission.core.annotation.DataPermission;
import cn.iocoder.yudao.framework.datapermission.core.util.DataPermissionUtils;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.SSOUser;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission.UserRoleVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserImportExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserImportRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserInfoVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantUserRelationConvert;
import cn.iocoder.yudao.module.system.convert.user.UserConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.UserPostDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.dept.UserPostMapper;
import cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.mq.producer.user.UserBaseInfoChangeProducer;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.dept.PostService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.tenant.TenantUserRelationService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.annotations.VisibleForTesting;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.user.UserAccountStatusEnum;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoChangeEvent;
import jakarta.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 后台用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service("adminUserService")
@Slf4j
public class AdminUserServiceImpl implements AdminUserService {

    static final String USER_INIT_PASSWORD_KEY = "system.user.init-password";

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private PermissionService permissionService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private RoleService roleService;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private TenantService tenantService;

    @Resource
    private UserPostMapper userPostMapper;

    @Resource
    private FileApi fileApi;
    @Resource
    private ConfigApi configApi;
    @Resource
    private TenantUserRelationService tenantUserRelationService;

    @Resource
    @Lazy
    private AdminAuthService adminAuthService;

    @Resource
    @Lazy
    private TenantUserFingerPrintService tenantUserFingerPrintService;

    @Resource
    private UserBaseInfoChangeProducer userBaseInfoChangeProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_CREATE_SUB_TYPE, bizNo = "{{#user.id}}",
        success = SYSTEM_USER_CREATE_SUCCESS)
    public Long createUserStore(UserSaveReqVO createReqVO) {
        // 1.1 校验账户配合
        tenantService.handleTenantInfo(tenant -> {
            long count = tenantUserRelationService.selectCount();
            if (count >= tenant.getWzAccountCount()) {
                throw exception(USER_COUNT_MAX, tenant.getWzAccountCount());
            }
        });
        // 1.2 校验正确性
        validateUserForCreateOrUpdate(null, createReqVO.getUsername(),
            createReqVO.getMobile(), createReqVO.getEmail(), createReqVO.getDeptId(), createReqVO.getPostIds());
        // 2.1 插入用户
        AdminUserDO user = UserConvert.INSTANCE.convertVo2Do(createReqVO);
        user.setPassword(encodePassword(createReqVO.getPassword())); // 加密密码
        userMapper.insert(user);
        // 2.2 插入关联岗位
        if (CollectionUtil.isNotEmpty(user.getPostIds())) {
            userPostMapper.insertBatch(convertList(user.getPostIds(),
                postId -> new UserPostDO().setUserId(user.getId()).setPostId(postId)));
        }
        // 2.3 写入门店user中间表
        if (createReqVO.isBindTenant()) {
            tenantUserRelationService.createTenantUserRelation(TenantUserRelationConvert.INSTANCE.userSaveReqVO2Dto(createReqVO, user.getId()));
        }
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("user", user);
        return user.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false) // 关闭数据权限，避免只查看自己时
    public Long createTenantUser(UserSaveReqVO createReqVO) {
        AdminUserDO user = getUserByMobileSystem(createReqVO.getMobile());
        if (user == null) {
            return createUserStore(createReqVO);
        }

        // 药师创建判断医生角色
        if (CollUtil.isNotEmpty(permissionService.selectUserRoleByUserIdRoleCodes(user.getId(), Set.of(RoleCodeEnum.DOCTOR.getCode())))) {
            throw exception(USER_HAS_ROLE_NO_ALLOW_ERROR, RoleCodeEnum.DOCTOR.getCode());
        }

        // 绑定 门店user中间表
        tenantUserRelationService.createTenantUserRelation(TenantUserRelationConvert.INSTANCE.userSaveReqVO2Dto(createReqVO, user.getId()));
        return user.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStore(UserSaveReqVO updateReqVO) {
        updateReqVO.setPassword(null); // 特殊：此处不更新密码
        // 1. 校验正确性
        AdminUserDO oldUser = validateUserForCreateOrUpdate(updateReqVO.getId(), updateReqVO.getUsername(),
            updateReqVO.getMobile(), updateReqVO.getEmail(), updateReqVO.getDeptId(), updateReqVO.getPostIds());

        // 2.1 更新用户
        AdminUserDO updateObj = BeanUtils.toBean(updateReqVO, AdminUserDO.class);
        userMapper.updateById(updateObj);
        // 2.2 更新岗位
        updateUserPost(updateReqVO, updateObj);
        // 2.3修改员工表
        tenantUserRelationService.updateTenantUserRelation(TenantUserRelationConvert.INSTANCE.userSaveReqVO2Dto(updateReqVO, updateObj.getId()));
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldUser, UserSaveReqVO.class));
        LogRecordContext.putVariable("user", oldUser);
        // 发送用户基础信息修改mq
        userBaseInfoChangeProducer.sendMessage(UserBaseInfoChangeEvent.builder().msg(UserConvert.INSTANCE.convertChangeDto(oldUser)).build(), LocalDateTime.now().plusSeconds(10));
    }

    private void updateUserPost(UserSaveReqVO reqVO, AdminUserDO updateObj) {
        Long userId = reqVO.getId();
        Set<Long> dbPostIds = convertSet(userPostMapper.selectListByUserId(userId), UserPostDO::getPostId);
        // 计算新增和删除的岗位编号
        Set<Long> postIds = CollUtil.emptyIfNull(updateObj.getPostIds());
        Collection<Long> createPostIds = CollUtil.subtract(postIds, dbPostIds);
        Collection<Long> deletePostIds = CollUtil.subtract(dbPostIds, postIds);
        // 执行新增和删除。对于已经授权的岗位，不用做任何处理
        if (!CollectionUtil.isEmpty(createPostIds)) {
            userPostMapper.insertBatch(convertList(createPostIds,
                postId -> new UserPostDO().setUserId(userId).setPostId(postId)));
        }
        if (!CollectionUtil.isEmpty(deletePostIds)) {
            userPostMapper.deleteByUserIdAndPostId(userId, deletePostIds);
        }
    }

    @Override
    public void updateUserLogin(Long id, String loginIp) {
        userMapper.updateById(new AdminUserDO().setId(id).setLoginIp(loginIp).setLoginDate(LocalDateTime.now()));
    }

    @Override
    public void updateUserProfile(Long id, UserProfileUpdateReqVO reqVO) {
        // 校验正确性
        AdminUserDO userExists = validateUserExists(id);
        validateEmailUnique(id, reqVO.getEmail());
        validateMobileUnique(id, reqVO.getMobile());
        // 执行更新
        AdminUserDO userDO = BeanUtils.toBean(reqVO, AdminUserDO.class).setId(id);
        userMapper.updateById(userDO);
        // 发送用户基础信息修改mq
        userBaseInfoChangeProducer.sendMessage(UserBaseInfoChangeEvent.builder().msg(UserConvert.INSTANCE.convertChangeDto(userExists)).build(), LocalDateTime.now().plusSeconds(10));
    }

    @Override
    public void updateUserPassword(Long id, UserProfileUpdatePasswordReqVO reqVO) {
        // 校验旧密码密码
        validateOldPassword(id, reqVO.getOldPassword());
        // 执行更新
        AdminUserDO updateObj = new AdminUserDO().setId(id);
        updateObj.setPassword(encodePassword(reqVO.getNewPassword())); // 加密密码
        userMapper.updateById(updateObj);
    }

    @Override
    public String updateUserAvatar(Long id, InputStream avatarFile) {
        validateUserExists(id);
        // 存储文件
        String avatar = fileApi.createFile(IoUtil.readBytes(avatarFile));
        // 更新路径
        AdminUserDO sysUserDO = new AdminUserDO();
        sysUserDO.setId(id);
        sysUserDO.setAvatar(avatar);
        userMapper.updateById(sysUserDO);
        return avatar;
    }

    @Override
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_UPDATE_PASSWORD_SUB_TYPE, bizNo = "{{#id}}",
        success = SYSTEM_USER_UPDATE_PASSWORD_SUCCESS)
    public void updateUserPassword(Long id, String password) {
        // 1. 校验用户存在
        AdminUserDO user = validateUserExists(id);

        // 2. 更新密码
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setPassword(encodePassword(password)); // 加密密码
        userMapper.updateById(updateObj);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("user", user);
        LogRecordContext.putVariable("newPassword", updateObj.getPassword());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatusSystem(Long id, Integer status) {
        AdminUserDO user = validateUserExists(id);   // 校验用户存在
        if (ObjectUtil.equals(UserAccountStatusEnum.QUIT.getCode(), user.getStatus())) {
            throw exception(USER_IS_QUIT);
        }
        // 判断是门店管理员不允许操作
        tenantService.validTenantAdminUserId(id);
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        userMapper.updateById(updateObj);
        // 强制退出
        logout(id, status);
        // 判断是否注销
        if (ObjectUtil.notEqual(status, UserAccountStatusEnum.QUIT.getCode())) {
            return;
        }
        // 如果是注销，则修改关联门店状态
        List<TenantUserRelationDO> tenantListByUserId = tenantUserRelationService.getTenantListByUserId(id);
        if (CollUtil.isNotEmpty(tenantListByUserId)) {
            tenantUserRelationService.updateTenantUserRelationStatus(CollectionUtils.convertList(tenantListByUserId, TenantUserRelationDO::getId), UserStatusEnum.DISABLE.getCode());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEmployeeStatus(Long id, Integer status) {
        TenantUserRelationDO tenantUserRelationDO = tenantUserRelationService.validateUserRelationExists(id);
        // 判断是门店管理员不允许操作
        tenantService.validTenantAdminUserId(tenantUserRelationDO.getUserId());
        // 查询员工账号状态
        AdminUserDO userDO = userMapper.selectById(tenantUserRelationDO.getUserId());
        if (ObjectUtil.equals(UserAccountStatusEnum.QUIT.getCode(), userDO.getStatus())) {
            throw exception(USER_IS_QUIT);
        }
        tenantUserRelationService.updateTenantUserRelationStatus(id, status);
        // 强制退出
        logout(tenantUserRelationDO.getUserId(), status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_DELETE_SUB_TYPE, bizNo = "{{#id}}",
        success = SYSTEM_USER_DELETE_SUCCESS)
    public void deleteUserSystem(Long id) {
        // 1. 校验用户存在
        AdminUserDO user = validateUserExists(id);

//        // 2.1 删除用户
        userMapper.deleteById(id);
        // 2.2 删除用户关联数据
        permissionService.processUserDeleted(id);
        // 2.2 删除用户岗位
        userPostMapper.deleteByUserId(id);

        // 2.3 删除门店user中间表
        tenantUserRelationService.deleteTenantUserRelation(id);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("user", user);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_DELETE_SUB_TYPE, bizNo = "{{#id}}",
        success = SYSTEM_USER_DELETE_SUCCESS)
    public void deleteUserStore(Long id) {
        // 1. 校验用户存在
        AdminUserDO user = validateUserExists(id);
        // 判断是门店管理员不允许操作
        tenantService.validTenantAdminUserId(id);
        // 2.删除门店user中间表
        tenantUserRelationService.deleteTenantUserRelation(id);
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("user", user);
    }

    @Override
    public AdminUserDO getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public AdminUserDO getUserById(Long id) {
        AdminUserDO userDO = userMapper.selectById(id);
        if (userDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return userDO;
    }

    @Override
    public AdminUserDO getUserByMobileSystem(String mobile) {
        return userMapper.selectByMobile(mobile);
    }

    @Override
    public PageResult<UserRespVO> getUserPageStore(UserPageReqVO reqVO) {
        IPage<UserRespVO> pageResult = userMapper.selectUserPageStore(MyBatisUtils.buildPage(reqVO), reqVO);
        if (pageResult == null || pageResult.getTotal() == 0) {
            return PageResult.empty();
        }

        // 组装角色信息
        Map<Long, List<UserRoleVO>> userRoleMap = permissionService.getUserRoleByTenantUserIds(pageResult.getRecords().stream().map(UserRespVO::getId).collect(Collectors.toSet()))
            .stream().collect(Collectors.groupingBy(UserRoleVO::getUserId));

        List<UserRespVO> userRespVOS = pageResult.getRecords().stream().peek(u -> {
            List<UserRoleVO> userRoleDOS = userRoleMap.getOrDefault(u.getId(), List.of());
            u.setRoleIds(CollectionUtils.convertSet(userRoleDOS, UserRoleVO::getRoleId));
            u.setRoleNames(CollectionUtils.convertSet(userRoleDOS, UserRoleVO::getRoleName));
            u.setRoleCodes(CollectionUtils.convertSet(userRoleDOS, UserRoleVO::getRoleCode));
        }).collect(Collectors.toList());
        // 填充门店数据
//        Map<Long, Long> userTenantMap = userMapper.selectUserTenantInfo(pageResult.getRecords().stream().map(UserRespVO::getId).distinct().collect(Collectors.toList()))
//                        .stream().collect(Collectors.toMap(UserRespVO::getId, UserRespVO::getTenantCount));
        return new PageResult<>(userRespVOS, pageResult.getTotal());
    }

    @Override
    @DataPermission(enable = false) // 关闭数据权限，避免只查看自己时
    public AdminUserDO getUserStore(Long id) {
        AdminUserDO adminUserDO = userMapper.selectById(id);
        if (adminUserDO == null) {
            throw exception(USER_NOT_EXISTS, id);
        }
        // 填充当前用户在门店的部门等信息
        List<TenantUserRelationDO> tenantListByUserId = tenantUserRelationService.getAvailableTenantListByUserId(id);
        if (CollUtil.isNotEmpty(tenantListByUserId)) {
            adminUserDO.setDeptId(tenantListByUserId.getFirst().getDeptId());
            adminUserDO.setPostIds(tenantListByUserId.getFirst().getPostIds());

        }
        return adminUserDO;
    }

    @Override
    public List<AdminUserDO> getUserListByDeptIds(Collection<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectListByDeptIds(deptIds);
    }

    @Override
    public List<AdminUserDO> getUserListByPostIds(Collection<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        Set<Long> userIds = convertSet(userPostMapper.selectListByPostIds(postIds), UserPostDO::getUserId);
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectBatchIds(userIds);
    }

    @Override
    public List<AdminUserDO> getUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return userMapper.selectBatchIds(ids);
    }

    @Override
    public void validateUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得岗位信息
        List<AdminUserDO> users = userMapper.selectBatchIds(ids);
        Map<Long, AdminUserDO> userMap = CollectionUtils.convertMap(users, AdminUserDO::getId);
        // 校验
        ids.forEach(id -> {
            AdminUserDO user = userMap.get(id);
            if (user == null) {
                throw exception(USER_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(user.getStatus())) {
                throw exception(USER_IS_DISABLE, user.getNickname());
            }
        });
    }

    @Override
    public List<AdminUserDO> getUserListByNickname(String nickname) {
        return userMapper.selectListByNickname(nickname);
    }

    /**
     * 获得部门条件：查询指定部门的子部门编号们，包括自身
     *
     * @param deptId 部门编号
     * @return 部门编号集合
     */
    private Set<Long> getDeptCondition(Long deptId) {
        if (deptId == null) {
            return Collections.emptySet();
        }
        Set<Long> deptIds = convertSet(deptService.getChildDeptList(deptId), DeptDO::getId);
        deptIds.add(deptId); // 包括自身
        return deptIds;
    }

    private AdminUserDO validateUserForCreateOrUpdate(Long id, String username, String mobile, String email,
        Long deptId, Set<Long> postIds) {
        // 关闭数据权限，避免因为没有数据权限，查询不到数据，进而导致唯一校验不正确
        return DataPermissionUtils.executeIgnore(() -> {
            // 校验用户存在
            AdminUserDO user = validateUserExists(id);
            // 校验用户名唯一
            validateUsernameUnique(id, username);
            // 校验手机号唯一
            validateMobileUnique(id, mobile);
            // 校验邮箱唯一
            // validateEmailUnique(id, email);
            // 校验部门处于开启状态
            deptService.validateDeptList(CollectionUtils.singleton(deptId));
            // 校验岗位处于开启状态
            postService.validatePostList(postIds);
            return user;
        });
    }

    @VisibleForTesting
    AdminUserDO validateUserExists(Long id) {
        if (id == null) {
            return null;
        }
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    @VisibleForTesting
    void validateUsernameUnique(Long id, String username) {
        if (StrUtil.isBlank(username)) {
            return;
        }
        AdminUserDO user = userMapper.selectByUsername(username);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_USERNAME_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_USERNAME_EXISTS);
        }
    }

    @VisibleForTesting
    void validateEmailUnique(Long id, String email) {
        if (StrUtil.isBlank(email)) {
            return;
        }
        AdminUserDO user = userMapper.selectByEmail(email);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_EMAIL_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_EMAIL_EXISTS);
        }
    }

    @VisibleForTesting
    void validateMobileUnique(Long id, String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        AdminUserDO user = userMapper.selectByMobile(mobile);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_MOBILE_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_MOBILE_EXISTS);
        }
    }

    /**
     * 校验旧密码
     *
     * @param id          用户 id
     * @param oldPassword 旧密码
     */
    @VisibleForTesting
    void validateOldPassword(Long id, String oldPassword) {
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        if (!isPasswordMatch(oldPassword, user.getPassword())) {
            throw exception(USER_PASSWORD_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有导入
    public UserImportRespVO importUserList(List<UserImportExcelVO> importUsers, boolean isUpdateSupport) {
        // 1.1 参数校验
//        if (CollUtil.isEmpty(importUsers)) {
//            throw exception(USER_IMPORT_LIST_IS_EMPTY);
//        }
//        // 1.2 初始化密码不能为空
//        String initPassword = configApi.getConfigValueByKey(USER_INIT_PASSWORD_KEY);
//        if (StrUtil.isEmpty(initPassword)) {
//            throw exception(USER_IMPORT_INIT_PASSWORD);
//        }
//
//        // 2. 遍历，逐个创建 or 更新
//        UserImportRespVO respVO = UserImportRespVO.builder().createUsernames(new ArrayList<>())
//                .updateUsernames(new ArrayList<>()).failureUsernames(new LinkedHashMap<>()).build();
//        importUsers.forEach(importUser -> {
//            // 2.1.1 校验字段是否符合要求
//            try {
//                ValidationUtils.validate(BeanUtils.toBean(importUser, UserSaveReqVO.class).setPassword(initPassword));
//            } catch (ConstraintViolationException ex){
//                respVO.getFailureUsernames().put(importUser.getUsername(), ex.getMessage());
//                return;
//            }
//            // 2.1.2 校验，判断是否有不符合的原因
//            try {
//                validateUserForCreateOrUpdate(null, null, importUser.getMobile(), importUser.getEmail(),
//                        importUser.getDeptId(), null);
//            } catch (ServiceException ex) {
//                respVO.getFailureUsernames().put(importUser.getUsername(), ex.getMessage());
//                return;
//            }
//
//            // 2.2.1 判断如果不存在，在进行插入
//            AdminUserDO existUser = userMapper.selectByUsername(importUser.getUsername());
//            if (existUser == null) {
//                userMapper.insert(BeanUtils.toBean(importUser, AdminUserDO.class)
//                        .setPassword(encodePassword(initPassword)).setPostIds(new HashSet<>())); // 设置默认密码及空岗位编号数组
//                respVO.getCreateUsernames().add(importUser.getUsername());
//                return;
//            }
//            // 2.2.2 如果存在，判断是否允许更新
//            if (!isUpdateSupport) {
//                respVO.getFailureUsernames().put(importUser.getUsername(), USER_USERNAME_EXISTS.getMsg());
//                return;
//            }
//            AdminUserDO updateUser = BeanUtils.toBean(importUser, AdminUserDO.class);
//            updateUser.setId(existUser.getId());
//            userMapper.updateById(updateUser);
//            respVO.getUpdateUsernames().add(importUser.getUsername());
//        });
        return null;
    }

    @Override
    public List<AdminUserDO> getSimpleUserListStore(Integer status) {
        return userMapper.selectSimpleUserListStore(status);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }


    @Override
    public Long handleOALoginUser(SSOUser ssoUser) {
        // 查询用户存在则更新状态
        AdminUserDO adminUserDO = userMapper.selectByUsername(ssoUser.getAccount());
        if (adminUserDO == null) {
            // 处理新增
            return TenantUtils.execute(TenantConstant.DEFAULT_TENANT_ID, () -> getSelf().createUserStore(UserConvert.INSTANCE.convertSsoUser(ssoUser)));
        }
        // 更新中间表状态
        TenantUserRelationDO tenantUserRelation = tenantUserRelationService.getTenantUserRelation(adminUserDO.getId(), TenantConstant.DEFAULT_TENANT_ID);
        tenantUserRelation.setStatus(ssoUser.getState());

        TenantUserRelationDto tenantUserRelationDto = TenantUserRelationConvert.INSTANCE.convertDto(tenantUserRelation);
        tenantUserRelationDto.setStatus(UserStatusEnum.convertSsoStatus(tenantUserRelation.getStatus()).getCode());
        tenantUserRelationService.updateTenantUserRelation(tenantUserRelationDto);
        // 更新用户基本信息(同步中台)
        UserConvert.INSTANCE.convertSsoUser2Do(ssoUser, adminUserDO);
        userMapper.updateById(adminUserDO);
        if (!Objects.equals(ssoUser.getState(), 1)) {
            throw exception(OA_LOGIN_USER_INFO_ERROR); // 抛出业务异常,不回滚上面的dml
        }
        // 分配OA登录用户 后台运营管理员角色
        // permissionService.assignUserRoleWithSystemRoleCodes(adminUserDO.getId(), Collections.singletonList(RoleCodeEnum.MANAGE_ADMIN.getCode()));

        return adminUserDO.getId();

    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(StringUtils.defaultIfBlank(password, configApi.getConfigValueByKey(USER_INIT_PASSWORD_KEY)));
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private AdminUserServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    public AdminUserDO getUserByMobileStore(String mobile) {
        return userMapper.getUserByMobileStore(mobile, TenantContextHolder.getRequiredTenantId());
    }

    @Override
    public PageResult<UserRespVO> pageInquiryUserSignature(UserPageReqVO pageReqVO) {
        return getUserPageStore(pageReqVO);
    }

    //******** 超级管理员 获取用户信息

    @Override
    public PageResult<UserRespVO> getUserPageSystem(UserPageReqVO reqVO) {
        IPage<UserRespVO> pageResult = userMapper.selectUserPageSystem(MyBatisUtils.buildPage(reqVO), reqVO);
        if (pageResult == null || pageResult.getTotal() == 0) {
            return PageResult.empty();
        }

//        // 填充门店数据
        Map<Long, Long> userTenantMap = userMapper.selectUserTenantInfo(pageResult.getRecords().stream().map(UserRespVO::getId).distinct().collect(Collectors.toList()))
            .stream().collect(Collectors.toMap(UserRespVO::getId, UserRespVO::getTenantCount));
        List<UserRespVO> userRespVOS = pageResult.getRecords().stream().peek(u -> u.setTenantCount(userTenantMap.getOrDefault(u.getId(), 0L))).collect(Collectors.toList());
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());

    }

    @Override
    public void resetUserGuid(Long userId) {
        userMapper.updateById(new AdminUserDO().setId(userId).setGuid(""));
    }


    @Override
    public List<AdminUserDO> getUserListByRoleCodes(List<String> roleCodes) {
        Set<Long> roleIds = roleService.selectByCodes(new HashSet<>(roleCodes)).stream().map(RoleDO::getId).collect(Collectors.toSet());
        List<UserRoleVO> userRoleVOS = permissionService.getUserListByRoleIds(roleIds);
        log.info("getUserListByRoleCodes,t:{},roleIds,:{},userRoleVOS:{}", TenantContextHolder.getTenantId(), roleIds, userRoleVOS);

        if (CollUtil.isEmpty(userRoleVOS)) {
            return Collections.emptyList();
        }
        List<AdminUserDO> userList = userMapper.getTenantAvailableUserList(convertList(userRoleVOS, UserRoleVO::getUserId));
        log.info("getUserListByRoleCodes,t:{},roleIds,:{},userRoleVOS:{},userList:{}", TenantContextHolder.getTenantId(), roleIds, userRoleVOS, userList);
        return userList;

    }

    @Override
    @DataPermission(enable = false)
    public <T extends BaseDO> List<T> fillUserInfo(List<T> list) {
        List<Long> userIdList = list.stream().flatMap(i -> Stream.of(i.getCreator(), i.getUpdater())).map(NumberUtils::toLong).filter(i -> i > 0).toList();
        Map<Long, String> userMap = userMapper.selectBatchIds(userIdList).stream().collect(Collectors.toMap(AdminUserDO::getId, AdminUserDO::getNickname, (a, b) -> b));

        return list.stream().peek(c -> {
            c.setCreator(userMap.getOrDefault(NumberUtils.toLong(c.getCreator()), c.getCreator()));
            c.setUpdater(userMap.getOrDefault(NumberUtils.toLong(c.getUpdater()), c.getUpdater()));
        }).toList();
    }


    @Override
    public AdminUserRespDTO getTenantUser() {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            throw exception(USER_NOT_EXISTS);
        }
        AdminUserDO adminUserDO = userMapper.selectById(loginUserId);
        if (adminUserDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        AdminUserRespDTO userRespDTO = UserConvert.INSTANCE.convertDto(adminUserDO);
        // 填充当前用户在门店的部门等信息
        List<TenantUserRelationDO> tenantListByUserId = tenantUserRelationService.getAvailableTenantListByUserId(loginUserId);
        if (CollUtil.isNotEmpty(tenantListByUserId)) {
            TenantUserRelationConvert.INSTANCE.fillUserRelationDto(tenantListByUserId.getFirst(), userRespDTO);

        }
        return userRespDTO;
    }

    /**
     * 禁用 - 强制退出用户
     *
     * @param userId 用户id
     * @param status 状态
     */
    public void logout(Long userId, Integer status) {
        if (CommonStatusEnum.isEnable(status)) {
            return;
        }
        if (TenantConstant.isSystemTenant()) {
            TenantUtils.executeIgnore(() -> adminAuthService.logout(userId, LoginLogTypeEnum.LOGOUT_DELETE));
        } else {
            adminAuthService.logout(userId, LoginLogTypeEnum.LOGOUT_DELETE);
        }
    }

    @Override
    public UserInfoVO getUserInfo() {
        AdminUserRespDTO tenantUser = getTenantUser();

        TenantDO tenantDO = tenantService.getRequiredTenant(TenantContextHolder.getRequiredTenantId());

        List<RoleDO> roleDOS = permissionService.getRoleByUser(tenantUser.getId());

        boolean hasFingerPrint = tenantUserFingerPrintService.hasFingerPrint(tenantUser.getId());

        return UserConvert.INSTANCE.convertUserInfo(tenantUser, tenantDO, roleDOS).setHasFingerPrint(hasFingerPrint);

    }
}
