package cn.iocoder.yudao.module.system.service.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_USER_FINGER_PRINT_CHECK_FAIL;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_USER_FINGER_PRINT_MORE_THAN;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_USER_FINGER_PRINT_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintCheckReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintSaveReqVO;
import cn.iocoder.yudao.module.system.convert.user.UserFingerPrintConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserFingerPrintDO;
import cn.iocoder.yudao.module.system.dal.mysql.user.TenantUserFingerPrintMapper;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.util.List;

/**
 * 门店员工指纹 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantUserFingerPrintServiceImpl implements TenantUserFingerPrintService {

    @Resource
    private TenantUserFingerPrintMapper tenantUserFingerPrintMapper;

    @Resource
    private AdminAuthService authService;

    /**
     * 用户所在门店最大指纹数
     */
    private static final int MAX_FINGER_PRINT = 1;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTenantUserFingerPrint(TenantUserFingerPrintSaveReqVO createReqVO) {
        // 校验最大
        if (tenantUserFingerPrintMapper.selectUserFingerCount(createReqVO.getUserId()) > MAX_FINGER_PRINT) {
            throw exception(TENANT_USER_FINGER_PRINT_MORE_THAN, MAX_FINGER_PRINT);
        }
        // 插入
        TenantUserFingerPrintDO tenantUserFingerPrint = BeanUtils.toBean(createReqVO, TenantUserFingerPrintDO.class);
        tenantUserFingerPrintMapper.insert(tenantUserFingerPrint);
        // 返回
        return tenantUserFingerPrint.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateTenantUserFingerPrint(TenantUserFingerPrintSaveReqVO updateReqVO) {
        // 校验密码
        if (updateReqVO.isCheckPwd()) {
            authService.authUserPassword(updateReqVO.getUserId(), updateReqVO.getPassword());
            return null;
        }

        List<TenantUserFingerPrintDO> userFingerPrintDOS = tenantUserFingerPrintMapper.selectUserFingerList(updateReqVO.getUserId());
        if (CollUtil.isEmpty(userFingerPrintDOS)) {
            return createTenantUserFingerPrint(updateReqVO);
            // throw exception(TENANT_USER_FINGER_PRINT_NOT_EXISTS);
        }
        // 修改-先删 再增
        tenantUserFingerPrintMapper.deleteByIds(userFingerPrintDOS.stream().map(TenantUserFingerPrintDO::getId).toList());
        return createTenantUserFingerPrint(updateReqVO);
    }

    @Override
    public void deleteTenantUserFingerPrint(Long id) {
        // 校验存在
        validateTenantUserFingerPrintExists(id);
        // 删除
        tenantUserFingerPrintMapper.deleteById(id);
    }

    private TenantUserFingerPrintDO validateTenantUserFingerPrintExists(Long id) {
        TenantUserFingerPrintDO userFingerPrintDO = tenantUserFingerPrintMapper.selectById(id);
        if (userFingerPrintDO == null) {
            throw exception(TENANT_USER_FINGER_PRINT_NOT_EXISTS);
        }
        return userFingerPrintDO;
    }

    //
    @Override
    public TenantUserFingerPrintDO getTenantUserFingerPrint(Long id) {
        return tenantUserFingerPrintMapper.selectById(id);
    }

    @Override
    public PageResult<TenantUserFingerPrintDO> getTenantUserFingerPrintPage(TenantUserFingerPrintPageReqVO pageReqVO) {
        return tenantUserFingerPrintMapper.selectPage(pageReqVO);
    }

    @Override
    public TenantUserFingerPrintRespVO getTenantUserDefaultFingerPrint() {
        List<TenantUserFingerPrintDO> userFingerPrintDOS = tenantUserFingerPrintMapper.selectUserFingerList(SecurityFrameworkUtils.getLoginUserId());
        if (CollUtil.isEmpty(userFingerPrintDOS)) {
            return null;
        }
        return UserFingerPrintConvert.INSTANCE.convert(userFingerPrintDOS.getFirst());
    }

    @Override
    public void checkFingerPrint(TenantUserFingerPrintCheckReqVO checkReqVO) {
        checkReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        TenantUserFingerPrintDO userFingerPrintDO = tenantUserFingerPrintMapper.selectOne(UserFingerPrintConvert.INSTANCE.convert(checkReqVO));
        if (userFingerPrintDO == null) {
            throw exception(TENANT_USER_FINGER_PRINT_CHECK_FAIL);
        }
    }

    @Override
    public boolean hasFingerPrint(Long userId) {
        return tenantUserFingerPrintMapper.selectUserFingerCount(userId) > 0;
    }
}