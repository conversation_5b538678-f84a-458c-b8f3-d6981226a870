package com.xyy.saas.inquiry.user.server;

import cn.iocoder.yudao.framework.websocket.config.YudaoWebSocketAutoConfiguration;
import cn.iocoder.yudao.module.system.util.Jose4jTokenProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.xyy.common", "cn.iocoder.yudao.module", "com.xyy.saas.inquiry"}
    , exclude = {YudaoWebSocketAutoConfiguration.class})
@EnableConfigurationProperties(value = {Jose4jTokenProperties.class})
public class InquiryUserServerApplication {


    public static void main(String[] args) {
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "inquiry-user-server");
        // 设置文件名编码格式，-Dsun.jnu.encoding=UTF-8 设置无效
        System.setProperty("sun.jnu.encoding", "UTF-8");

        SpringApplication app = new SpringApplication(InquiryUserServerApplication.class);
        if (isActuatorStartUp()) {
            int cacheSize = 10240;
            log.info("================= actuator start up with buffer: {} =================", cacheSize);
            app.setApplicationStartup(new BufferingApplicationStartup(cacheSize));
        }
        app.run(args);

        log.info("sun.jnu.encoding: {}", System.getProperty("sun.jnu.encoding"));
    }

    private static boolean isActuatorStartUp() {
        return "true".equalsIgnoreCase(System.getProperty("spring.actuator.startup.enable"));
    }
}