<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.dict.DictDataMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <select id="selectByTenantIdDictTypeAndValues" resultType="cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO">
    SELECT * FROM system_dict_data
    WHERE tenant_id = #{tenantId} AND dict_type = #{dictType} AND `value` IN
    <foreach collection="valueList" item="value" open="(" close=")" separator=",">
      #{value}
    </foreach>
  </select>

  <insert id="insertOrUpdateBatch" parameterType="cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataSaveBatchReqVO">
    INSERT INTO system_dict_data (tenant_id, label, `value`, dict_type)
    VALUES
    <foreach collection="valueLabelMap" index="value" item="label" separator=",">
      (#{tenantId}, #{label}, #{value}, #{dictType})
    </foreach>
    ON DUPLICATE KEY UPDATE
      label = values(label),
      status = 0,
      deleted = 0,
      update_time = now();
  </insert>


</mapper>