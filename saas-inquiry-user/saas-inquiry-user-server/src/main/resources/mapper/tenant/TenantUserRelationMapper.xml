<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantUserRelationMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <select id="pageTenantUserRelation"
    resultType="cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationRespVO">
    select a.user_id,a.status,a.need_clock_in,c.status as accountStatus,b.*
    from system_tenant_user_relation a left join system_tenant b on a.tenant_id = b.id
    left join system_users c on c.id = a.user_id
    <where>
      a.deleted = false and a.user_id = #{pageReqVO.userId}
      <if test="pageReqVO.name != null and pageReqVO.name != '' ">
        AND (b.name like concat('%',#{pageReqVO.name},'%') or b.pref like concat('%',#{pageReqVO.name},'%'))
      </if>
      <if test="pageReqVO.contactMobile != null and pageReqVO.contactMobile != '' ">
        AND b.contact_mobile like concat('%',#{pageReqVO.contactMobile},'%')
      </if>
      <if test="pageReqVO.tenantIds != null and pageReqVO.tenantIds.size() > 0 ">
        and a.tenant_id in
        <foreach collection="pageReqVO.tenantIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="pageReqVO.roleIds != null and pageReqVO.roleIds.size() > 0 ">
        and exists (select 1 from system_user_role d where a.user_id = d.user_id and a.tenant_id = d.tenant_id
        and d.role_id in
        <foreach collection="pageReqVO.roleIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
        )
      </if>
    </where>
    order by a.id desc
  </select>


  <select id="getAvailableUserTenantRelationsByRoleCode"
    resultType="cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto">
    select a.*,c.head_tenant_id,c.type as tenantType, b.mobile
    from system_tenant_user_relation a
    left join system_tenant c on a.tenant_id = c.id
    left join system_users b on a.user_id = b.id
    <where>
      a.deleted = false
      and b.deleted = false
      and c.deleted = false
      and a.status = 0
      and b.status = 0
      and c.status = 0
      <if test="userId != null ">
        and b.id = #{userId}
      </if>
      <if test="roleId != null ">
        AND exists (select 1 from system_user_role c where b.id = c.user_id and a.tenant_id = c.tenant_id
        and c.role_id = #{roleId} and c.deleted = false )
      </if>

    </where>


  </select>
  <select id="getTenantUserRelationsByRoleCode"
    resultType="cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto">
    select a.*, b.mobile
    from system_tenant_user_relation a
    left join system_users b on a.user_id = b.id
    <where>
      a.deleted = false
      and b.deleted = false
      <if test="userId != null ">
        and b.id = #{userId}
      </if>
      <if test="tenantId != null ">
        and a.tenant_id = #{tenantId}
      </if>

      <if test="roleId != null ">
        AND exists (select 1 from system_user_role c where b.id = c.user_id and a.tenant_id = c.tenant_id
        and c.role_id = #{roleId} and c.deleted = false )
      </if>

    </where>

  </select>
</mapper>