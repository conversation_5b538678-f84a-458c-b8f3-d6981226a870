<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->


  <select id="selectUserPageStore"
    resultType="cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO">
    select a.id,a.username,a.nickname,a.mobile,a.sex,a.create_time,a.status as accountStatus
    ,b.status,b.id as tenantUserRelationId,b.need_clock_in,b.clock_in_status,b.tenant_id
    ,c.contact_user_id as tenantAdminUserId
    from system_users a
    left join system_tenant_user_relation b on a.id = b.user_id
    left join system_tenant c on b.tenant_id = c.id
    <where>
      <include refid="Base_Where"/>
      and b.deleted = false
      <if test="pageReqVO.roleIds != null and pageReqVO.roleIds.size() > 0 ">
        and exists(select 1 from system_user_role c where c.user_id = a.id and c.role_id in
        <foreach collection="pageReqVO.roleIds" open="(" item="id" separator="," close=")">
          #{id}
        </foreach>
        )
      </if>
      <if test="pageReqVO.status != null">
        AND b.status = #{pageReqVO.status}
      </if>

    </where>
    order by a.create_time desc
  </select>

  <select id="selectUserPageSystem"
    resultType="cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO">
    select a.id,a.username,a.nickname,a.mobile,a.sex,a.status as accountStatus,a.create_time
    <if test="pageReqVO.tenantId != null">
      ,b.id as tenantUserRelationId,b.status,c.contact_user_id as tenantAdminUserId
    </if>
    from system_users a
    <if test="pageReqVO.tenantId != null">
      left join system_tenant_user_relation b on a.id = b.user_id
      left join system_tenant c on b.tenant_id = c.id
    </if>
    <where>
      <include refid="Base_Where"/>
      <if test="pageReqVO.tenantId != null">
        and b.tenant_id = #{pageReqVO.tenantId} and b.deleted = false
      </if>
    </where>
    order by a.create_time desc
  </select>

  <sql id="Base_Where">
    a.deleted = false
    <if test="pageReqVO.createTime != null and pageReqVO.createTime.length > 0">
      AND a.create_time BETWEEN #{pageReqVO.createTime[0],javaType=java.time.LocalDateTime} AND
      #{pageReqVO.createTime[1],javaType=java.time.LocalDateTime}
    </if>
    <if test="pageReqVO.nickname != null and pageReqVO.nickname != '' ">
      AND a.nickname like concat('%',#{pageReqVO.nickname},'%')
    </if>
    <if test="pageReqVO.mobile != null and pageReqVO.mobile != ''">
      AND a.mobile like concat('%',#{pageReqVO.mobile},'%')
    </if>
    <if test="pageReqVO.userAccountStatus != null">
      AND a.status = #{pageReqVO.userAccountStatus}
    </if>
  </sql>

  <select id="selectUserTenantInfo"
    resultType="cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO">
    select user_id as id,count(distinct tenant_id) as tenantCount from system_tenant_user_relation
    where deleted = false and user_id in
    <foreach collection="userIds" open="(" item="id" separator="," close=")">
      #{id}
    </foreach>
    group by user_id
  </select>

  <select id="selectSimpleUserListStore"
    resultType="cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO">
    select a.id,
           a.username,
           a.nickname,
           a.mobile,
           a.sex,
           a.status,
           a.create_time,
           b.id as tenantUserRelationId
    from system_users a
           left join system_tenant_user_relation b on a.id = b.user_id
    where b.status = #{status}
      and b.deleted = false
  </select>

  <select id="getUserByMobileStore"
    resultType="cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO">
    select a.*, b.id as tenantUserRelationId
    from system_users a
           left join system_tenant_user_relation b on a.id = b.user_id
    where a.mobile = #{mobile}
      and a.status != 2
      and b.tenant_id = #{tenantId}
      and b.deleted = false limit 1
  </select>

  <select id="getTenantAvailableUserList"
    resultType="cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO">
    select a.* from system_users a
    left join system_tenant_user_relation b on a.id = b.user_id
    where a.status = 0 and b.status = 0 and a.deleted = false and b.deleted = false
    and user_id in
    <foreach collection="userIds" open="(" item="id" separator="," close=")">
      #{id}
    </foreach>

  </select>

</mapper>