package cn.iocoder.yudao.module.system.test;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.ApplicationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Controller权限扫描测试类
 * 扫描项目中所有Controller的接口权限
 */
@SpringBootTest
public class ControllerPermissionScannerTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void scanControllerPermissions() {
        System.out.println("=== 开始扫描Controller接口权限 ===\n");
        
        // 获取所有Controller Bean
        Map<String, Object> controllers = applicationContext.getBeansWithAnnotation(RestController.class);
        controllers.putAll(applicationContext.getBeansWithAnnotation(Controller.class));
        
        // 存储权限信息
        List<ControllerPermissionInfo> permissionInfos = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : controllers.entrySet()) {
            Object controller = entry.getValue();
            Class<?> controllerClass = controller.getClass();
            
            // 跳过代理类
            if (controllerClass.getName().contains("$$")) {
                continue;
            }
            
            scanControllerClass(controllerClass, permissionInfos);
        }
        
        // 按模块分组输出
        Map<String, List<ControllerPermissionInfo>> groupedByModule = permissionInfos.stream()
            .collect(Collectors.groupingBy(ControllerPermissionInfo::getModule));
        
        // 输出结果
        outputPermissionsByModule(groupedByModule);
        
        System.out.println("\n=== 扫描完成 ===");
        System.out.println("总计扫描到 " + permissionInfos.size() + " 个接口");
        System.out.println("涉及模块: " + groupedByModule.keySet());
    }
    
    private void scanControllerClass(Class<?> controllerClass, List<ControllerPermissionInfo> permissionInfos) {
        // 获取类级别的RequestMapping
        String classPath = getClassRequestMapping(controllerClass);
        String moduleName = extractModuleName(controllerClass.getName());
        
        // 扫描所有方法
        Method[] methods = controllerClass.getDeclaredMethods();
        for (Method method : methods) {
            ControllerPermissionInfo info = scanMethod(controllerClass, method, classPath, moduleName);
            if (info != null) {
                permissionInfos.add(info);
            }
        }
    }
    
    private ControllerPermissionInfo scanMethod(Class<?> controllerClass, Method method, String classPath, String moduleName) {
        // 检查是否有HTTP映射注解
        String httpMethod = getHttpMethod(method);
        if (httpMethod == null) {
            return null;
        }
        
        // 获取方法级别的路径
        String methodPath = getMethodRequestMapping(method);
        String fullPath = classPath + methodPath;
        
        // 获取PreAuthorize注解
        PreAuthorize preAuthorize = AnnotationUtils.findAnnotation(method, PreAuthorize.class);
        String permission = preAuthorize != null ? preAuthorize.value() : "无权限要求";
        
        // 获取Operation注解的summary
        String summary = getOperationSummary(method);
        
        return new ControllerPermissionInfo(
            moduleName,
            controllerClass.getSimpleName(),
            method.getName(),
            httpMethod,
            fullPath,
            permission,
            summary
        );
    }
    
    private String getClassRequestMapping(Class<?> controllerClass) {
        RequestMapping requestMapping = AnnotationUtils.findAnnotation(controllerClass, RequestMapping.class);
        if (requestMapping != null && requestMapping.value().length > 0) {
            return requestMapping.value()[0];
        }
        return "";
    }
    
    private String getMethodRequestMapping(Method method) {
        // 检查各种HTTP方法注解
        RequestMapping requestMapping = AnnotationUtils.findAnnotation(method, RequestMapping.class);
        if (requestMapping != null && requestMapping.value().length > 0) {
            return requestMapping.value()[0];
        }
        
        GetMapping getMapping = AnnotationUtils.findAnnotation(method, GetMapping.class);
        if (getMapping != null && getMapping.value().length > 0) {
            return getMapping.value()[0];
        }
        
        PostMapping postMapping = AnnotationUtils.findAnnotation(method, PostMapping.class);
        if (postMapping != null && postMapping.value().length > 0) {
            return postMapping.value()[0];
        }
        
        PutMapping putMapping = AnnotationUtils.findAnnotation(method, PutMapping.class);
        if (putMapping != null && putMapping.value().length > 0) {
            return putMapping.value()[0];
        }
        
        DeleteMapping deleteMapping = AnnotationUtils.findAnnotation(method, DeleteMapping.class);
        if (deleteMapping != null && deleteMapping.value().length > 0) {
            return deleteMapping.value()[0];
        }
        
        return "";
    }
    
    private String getHttpMethod(Method method) {
        if (AnnotationUtils.findAnnotation(method, GetMapping.class) != null) {
            return "GET";
        }
        if (AnnotationUtils.findAnnotation(method, PostMapping.class) != null) {
            return "POST";
        }
        if (AnnotationUtils.findAnnotation(method, PutMapping.class) != null) {
            return "PUT";
        }
        if (AnnotationUtils.findAnnotation(method, DeleteMapping.class) != null) {
            return "DELETE";
        }
        
        RequestMapping requestMapping = AnnotationUtils.findAnnotation(method, RequestMapping.class);
        if (requestMapping != null) {
            if (requestMapping.method().length > 0) {
                return requestMapping.method()[0].name();
            }
            return "GET"; // 默认GET
        }
        
        return null;
    }
    
    private String getOperationSummary(Method method) {
        try {
            // 使用反射获取Operation注解
            Class<?> operationClass = Class.forName("io.swagger.v3.oas.annotations.Operation");
            Object operation = method.getAnnotation(operationClass);
            if (operation != null) {
                Method summaryMethod = operationClass.getMethod("summary");
                return (String) summaryMethod.invoke(operation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }
    
    private String extractModuleName(String className) {
        if (className.contains("product")) {
            return "商品模块";
        } else if (className.contains("user") || className.contains("system")) {
            return "用户系统模块";
        } else if (className.contains("pharmacist")) {
            return "药师模块";
        } else if (className.contains("hospital")) {
            return "医院模块";
        } else if (className.contains("patient")) {
            return "患者模块";
        } else if (className.contains("transmitter")) {
            return "传输模块";
        } else {
            return "其他模块";
        }
    }
    
    private void outputPermissionsByModule(Map<String, List<ControllerPermissionInfo>> groupedByModule) {
        for (Map.Entry<String, List<ControllerPermissionInfo>> moduleEntry : groupedByModule.entrySet()) {
            String moduleName = moduleEntry.getKey();
            List<ControllerPermissionInfo> infos = moduleEntry.getValue();
            
            System.out.println("=== " + moduleName + " ===");
            
            // 按Controller分组
            Map<String, List<ControllerPermissionInfo>> groupedByController = infos.stream()
                .collect(Collectors.groupingBy(ControllerPermissionInfo::getControllerName));
            
            for (Map.Entry<String, List<ControllerPermissionInfo>> controllerEntry : groupedByController.entrySet()) {
                String controllerName = controllerEntry.getKey();
                List<ControllerPermissionInfo> controllerInfos = controllerEntry.getValue();
                
                System.out.println("\n【" + controllerName + "】");
                
                for (ControllerPermissionInfo info : controllerInfos) {
                    System.out.printf("  %-6s %-40s -> %s%n", 
                        info.getHttpMethod(), 
                        info.getFullPath(),
                        info.getPermission()
                    );
                    if (!info.getSummary().isEmpty()) {
                        System.out.printf("         描述: %s%n", info.getSummary());
                    }
                }
            }
            System.out.println();
        }
    }
    
    /**
     * 权限信息实体类
     */
    public static class ControllerPermissionInfo {
        private String module;
        private String controllerName;
        private String methodName;
        private String httpMethod;
        private String fullPath;
        private String permission;
        private String summary;
        
        public ControllerPermissionInfo(String module, String controllerName, String methodName, 
                                      String httpMethod, String fullPath, String permission, String summary) {
            this.module = module;
            this.controllerName = controllerName;
            this.methodName = methodName;
            this.httpMethod = httpMethod;
            this.fullPath = fullPath;
            this.permission = permission;
            this.summary = summary;
        }
        
        // Getters
        public String getModule() { return module; }
        public String getControllerName() { return controllerName; }
        public String getMethodName() { return methodName; }
        public String getHttpMethod() { return httpMethod; }
        public String getFullPath() { return fullPath; }
        public String getPermission() { return permission; }
        public String getSummary() { return summary; }
    }
}
