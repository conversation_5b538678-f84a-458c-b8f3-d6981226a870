<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-transmitter</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>saas-transmitter-api</artifactId>
  <packaging>jar</packaging>

  <dependencies>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pojo</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-common</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <optional>true</optional>
    </dependency>

    <!-- Dubbo -->
    <!-- <dependency> -->
    <!--   <groupId>org.apache.dubbo</groupId> -->
    <!--   <artifactId>dubbo</artifactId> -->
    <!-- </dependency> -->

    <!-- &lt;!&ndash; Validation &ndash;&gt; -->
    <!-- <dependency> -->
    <!--   <groupId>javax.validation</groupId> -->
    <!--   <artifactId>validation-api</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>org.hibernate.validator</groupId> -->
    <!--   <artifactId>hibernate-validator</artifactId> -->
    <!-- </dependency> -->

    <!-- &lt;!&ndash; Common &ndash;&gt; -->
    <!-- <dependency> -->
    <!--   <groupId>org.projectlombok</groupId> -->
    <!--   <artifactId>lombok</artifactId> -->
    <!-- </dependency> -->
  </dependencies>
</project> 