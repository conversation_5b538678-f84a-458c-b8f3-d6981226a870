package com.xyy.saas.transmitter.api.config.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 协议配置 DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransmissionConfigItemDTO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15565")
    private Integer id;

    @Schema(description = "协议配置包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31380")
    private Integer configPackageId;

    @Schema(description = "父节点id", example = "16154")
    private Integer parentItemId;

    @Schema(description = "配置类型（1-视图配置、2-逻辑配置、3-协议配置）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer dslType;

    @Schema(description = "节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer nodeType;

    @Schema(description = "接口编码", example = "3501")
    private String apiCode;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    private String description;

    @Schema(description = "配置值,yaml")
    private String configValue;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}