<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-transmitter</artifactId>
    <version>${revision}</version>
  </parent>


  <modelVersion>4.0.0</modelVersion>
  <artifactId>saas-transmitter-impl</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- 项目内部依赖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-transmitter-api</artifactId>
      <version>${project.version}</version>
    </dependency>


    <!-- 暂时注释 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-dsl</artifactId>
      <version>${xyy.soa.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-protection</artifactId>
    </dependency>
  </dependencies>

</project>