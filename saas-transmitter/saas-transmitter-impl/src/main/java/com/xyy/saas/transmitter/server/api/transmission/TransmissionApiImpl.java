package com.xyy.saas.transmitter.server.api.transmission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionRespDTO;
import com.xyy.saas.transmitter.server.service.transmission.TransmissionService;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 数据传输服务 API 实现类 实现数据传输相关的远程服务调用接口
 * <p>
 * 核心职责： 1. 接口适配：转换RPC请求为内部服务调用 2. 参数校验：验证接口调用参数 3. 结果封装：包装服务执行结果
 * <p>
 * 实现说明： 1. 使用 DubboService 提供远程服务能力 2. 委托内部服务实现具体业务逻辑 3. 统一的异常处理和日志记录
 *
 * <AUTHOR>
 */
@Service
// @DubboService
public class TransmissionApiImpl implements TransmissionApi {

    @Resource
    private TransmissionService transmissionService;

    /**
     * 校验协议配置有效性 委托内部服务执行配置校验
     *
     * @param transmissionConfigReqDTO 配置校验请求
     * @return 配置是否有效
     */
    @Override
    public boolean validProtocolConfig(TransmissionConfigReqDTO transmissionConfigReqDTO) {
        return transmissionService.validProtocolConfig(transmissionConfigReqDTO);
    }

    /**
     * 执行业务逻辑校验 委托内部服务执行业务校验
     *
     * @param transmissionReqDTO 业务校验请求
     * @return 校验结果
     */
    @Override
    public CommonResult<Boolean> validateBusinessLogic(TransmissionReqDTO transmissionReqDTO) {
        return transmissionService.validateBusinessLogic(transmissionReqDTO);
    }

    /**
     * 执行数据传输(多服务包配置相同节点-药监的模式) 多协议调用，循环请求 处理数据传输任务，支持同步和异步模式
     * <p>
     * 处理流程： 1. 参数校验：验证请求参数的完整性 2. 任务创建：构建传输任务实体 3. 任务执行：处理数据传输逻辑 4. 结果处理：处理传输结果和异常
     * <p>
     * 特性支持： 1. 同步/异步：支持两种传输模式 2. 重试机制：失败任务自动重试 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz              响应数据类型的Class对象
     * @param <T>                响应数据类型
     * @return 传输响应列表，包含处理结果和响应数据
     */
    @Override
    public <T> List<TransmissionRespDTO<T>> contractsInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz) {
        return transmissionService.contractsInvoke(transmissionReqDTO, clazz);
    }

    /**
     * 执行数据传输(节点唯一) 单一协议调用，如果匹配到多个服务包则取第一个服务包的节点调用 处理数据传输任务，支持同步和异步模式
     * <p>
     * 处理流程： 1. 参数校验：验证请求参数的完整性 2. 任务创建：构建传输任务实体 3. 任务执行：处理数据传输逻辑 4. 结果处理：处理传输结果和异常
     * <p>
     * 特性支持： 1. 同步/异步：支持两种传输模式 2. 重试机制：失败任务自动重试 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz              响应数据类型的Class对象
     * @param <T>                响应数据类型
     * @return 传输响应列表，包含处理结果和响应数据
     */
    @Override
    public <T> CommonResult<T> contractInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz) {
        return transmissionService.contractInvoke(transmissionReqDTO, clazz);
    }

}
