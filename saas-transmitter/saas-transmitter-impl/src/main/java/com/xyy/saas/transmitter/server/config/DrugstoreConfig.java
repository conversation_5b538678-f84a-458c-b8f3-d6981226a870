// package com.xyy.saas.transmitter.server.config;
//
// import cn.iocoder.yudao.framework.apilog.config.YudaoApiLogAutoConfiguration;
// import cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration;
// import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
// import cn.iocoder.yudao.module.infra.api.file.FileApi;
// import cn.iocoder.yudao.module.infra.api.logger.ApiAccessLogApi;
// import cn.iocoder.yudao.module.infra.api.logger.ApiErrorLogApi;
// import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
// import cn.iocoder.yudao.module.system.api.dict.DictTypeApi;
// import cn.iocoder.yudao.module.system.api.logger.OperateLogApi;
// import cn.iocoder.yudao.module.system.api.oauth2.OAuth2TokenApi;
// import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
// import cn.iocoder.yudao.module.system.api.permission.RoleApi;
// import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
// import cn.iocoder.yudao.module.system.api.tenant.TenantPackageApi;
// import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
// import cn.iocoder.yudao.module.system.api.tenant.TenantThirdAppApi;
// import cn.iocoder.yudao.module.system.api.tenant.TenantUserRelationApi;
// import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
// import org.apache.dubbo.config.annotation.DubboReference;
// import org.springframework.boot.autoconfigure.AutoConfiguration;
// import org.springframework.boot.autoconfigure.AutoConfigureBefore;
//
// /**
//  * @Desc 商户配置类
//  * <AUTHOR>  <EMAIL>
//  * @Date Created in 2024/9/9 下午2:27
//  */
// @AutoConfiguration(before = {YudaoTenantAutoConfiguration.class, YudaoApiLogAutoConfiguration.class})
// @AutoConfigureBefore({YudaoTenantAutoConfiguration.class, YudaoApiLogAutoConfiguration.class})
// public class DrugstoreConfig {
//
//     //@DubboReference(id = "tenant-api", application = "${dubbo.application.name}")
//     @DubboReference
//     private TenantApi tenantApi;
//
//     @DubboReference
//     private ApiAccessLogApi apiAccessLogApi;
//
//     @DubboReference
//     private ApiErrorLogApi apiErrorLogApi;
//
//     @DubboReference
//     private OAuth2TokenApi oAuth2TokenApi;
//
//     @DubboReference
//     private PermissionApi permissionApi;
//
//     @DubboReference
//     private RoleApi roleApi;
//
//     @DubboReference
//     private ConfigApi configApi;
//
//     @DubboReference
//     private FileApi fileApi;
//
//     @DubboReference
//     private AdminUserApi adminUserApi;
//
//     @DubboReference
//     private TenantUserRelationApi tenantUserRelationApi;
//
//     @DubboReference
//     private TenantPackageApi tenantPackageApi;
//
//     @DubboReference
//     private OperateLogApi operateLogApi;
//
//     @DubboReference
//     private DictDataApi dictDataApi;
//
//     @DubboReference
//     private DictTypeApi dictTypeApi;
//
//     @DubboReference
//     private TenantServicePackRelationApi tenantServicePackRelationApi;
//
//     @DubboReference
//     private TenantThirdAppApi tenantThirdAppApi;
//
// }
