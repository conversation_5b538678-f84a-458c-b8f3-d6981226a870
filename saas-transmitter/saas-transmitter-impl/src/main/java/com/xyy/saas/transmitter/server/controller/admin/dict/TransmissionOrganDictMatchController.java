package com.xyy.saas.transmitter.server.controller.admin.dict;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchGetReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchRespVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.transmitter.server.service.dict.TransmissionOrganDictMatchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 服务商数据字典配对")
@RestController
@RequestMapping("/transmitter/transmission-organ-dict-match")
@Validated
public class TransmissionOrganDictMatchController {

    @Resource
    private TransmissionOrganDictMatchService transmissionProviderDictMatchService;


    @GetMapping("/list-dict-match-organ")
    @Operation(summary = "查询三方字典匹配的机构列表")
    @Parameter(name = "dictType", description = "字典类型", required = true, example = "drug_use_frequency")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict-match:query')")
    public CommonResult<List<TransmissionOrganRespVO>> listDictMatchOrgan(@RequestParam(value = "dictType") String dictType) {
        return success(transmissionProviderDictMatchService.listDictMatchOrgan(dictType));
    }


    @GetMapping("/page")
    @Operation(summary = "获得服务商数据字典配对分页")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict-match:query')")
    public CommonResult<PageResult<TransmissionOrganDictMatchRespVO>> getTransmissionOrganDictMatchPage(@Valid TransmissionOrganDictMatchPageReqVO pageReqVO) {
        PageResult<TransmissionOrganDictMatchRespVO> pageResult = transmissionProviderDictMatchService.getTransmissionOrganDictMatchPage(pageReqVO);
        return success(pageResult);
    }


    @PostMapping("/operate-match")
    @Operation(summary = "服务商数据字典配对 or 无法配对")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict-match:create')")
    public CommonResult<Boolean> createDictMatch(@Valid @RequestBody TransmissionOrganDictMatchSaveReqVO createReqVO) {
        transmissionProviderDictMatchService.createTransmissionOrganDictMatch(createReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得服务商数据字典详情")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict-match:query')")
    public CommonResult<TransmissionOrganDictMatchRespVO> getTransmissionProviderDictMatch(TransmissionOrganDictMatchGetReqVO getReqVO) {
        TransmissionOrganDictMatchRespVO transmissionProviderDictMatch = transmissionProviderDictMatchService.getTransmissionOrganDictMatch(getReqVO);
        return success(transmissionProviderDictMatch);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除服务商数据字典配对")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict-match:delete')")
    public CommonResult<Boolean> deleteTransmissionProviderDictMatch(@RequestParam("id") Long id) {
        transmissionProviderDictMatchService.deleteTransmissionOrganDictMatch(id);
        return success(true);
    }


}