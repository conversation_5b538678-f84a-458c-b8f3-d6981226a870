package com.xyy.saas.transmitter.server.controller.admin.dict.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.enums.dict.DictMatchEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertFalse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 服务商数据字典配对新增/修改 Request VO")
@Data
public class TransmissionOrganDictMatchSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4371")
    private Long id;

    @Schema(description = "服务商ID不能为空", requiredMode = Schema.RequiredMode.REQUIRED, example = "4327")
    @NotNull(message = "服务商ID不能为空")
    private Integer organId;

    @Schema(description = "字典类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "字典类型不能为空")
    private String dictType;

    @Schema(description = "saas字典id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26981")
    @NotNull(message = "saas字典id不能为空")
    private Long dictId;

    @Schema(description = "服务商字典id", example = "2115")
    private Long organDictId;

    @Schema(description = "配对状态：0未配对 1已配对 2无法配对", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "配对状态不能为空")
    private Integer status;

    @AssertFalse(message = "三方字典不能为空")
    @JsonIgnore
    public boolean isOrganDictIdValid() {
        return DictMatchEnum.isMatch(status) && organDictId == null; // 匹配时候必须传三方字典项
    }

}