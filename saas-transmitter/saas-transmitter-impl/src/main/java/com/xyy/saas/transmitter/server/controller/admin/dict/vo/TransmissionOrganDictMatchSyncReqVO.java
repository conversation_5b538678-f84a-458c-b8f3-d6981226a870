package com.xyy.saas.transmitter.server.controller.admin.dict.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 服务商数据字典配对同步 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransmissionOrganDictMatchSyncReqVO extends PageParam {

    @Schema(description = "医药行业行政机构ID", example = "4327")
    @NotNull
    private Integer organId;

    @Schema(description = "字典类型", example = "1")
    @NotEmpty
    private String dictType;

}