package com.xyy.saas.transmitter.server.controller.admin.dict.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 服务商字典分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionOrganDictPageReqVO extends PageParam {

    @Schema(description = "服务提供方id", example = "13973")
    private Integer organId;

    @Schema(description = "字典类型", example = "1")
    private String dictType;

    @Schema(description = "字典名称", example = "李四")
    private String dictName;

    @Schema(description = "父编码 空无父编码 ", example = "23921")
    private String parentValue;

    @Schema(description = "是否末级 节点 ", example = "23921")
    private Integer endNode;

    @Schema(description = "字典标签")
    private String label;

    @Schema(description = "字典值")
    private String value;

    private List<String> values;

    @Schema(description = "字典外码")
    private String outerValue;

    @Schema(description = "状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}