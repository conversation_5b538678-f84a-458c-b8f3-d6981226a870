package com.xyy.saas.transmitter.server.controller.admin.dict.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 服务商字典 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransmissionOrganDictRespVO {

    @Schema(description = "主键ID", example = "20041")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "服务提供方id", example = "13973")
    @ExcelProperty("服务提供方id")
    private Integer organId;

    @Schema(description = "服务提供方名称", example = "13973")
    private String organName;

    @Schema(description = "字典类型", example = "1")
    @ExcelProperty("字典类型")
    private String dictType;

    @Schema(description = "字典名称", example = "李四")
    @ExcelProperty("字典名称")
    private String dictName;

    @Schema(description = "父编码 空无父编码 ", example = "23921")
    @ExcelProperty("父值 空无父值 ")
    private String parentValue;

    /**
     * 是否末级节点
     */
    @Schema(description = "是否末级节点", example = "23921")
    @ExcelProperty("是否末级节点")
    private Integer endNode;

    @Schema(description = "字典标签")
    @ExcelProperty("字典标签")
    private String label;

    @Schema(description = "字典值")
    @ExcelProperty("字典值")
    private String value;

    @Schema(description = "字典外码")
    @ExcelProperty("字典外码")
    private String outerValue;

    @Schema(description = "状态（0正常 1停用）", example = "1")
    @ExcelProperty("状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}