package com.xyy.saas.transmitter.server.controller.admin.organ.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 医药行业行政机构分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionOrganPageReqVO extends PageParam {

    @Schema(description = "机构id", example = "1")
    private Integer id;

    @Schema(description = "机构id列表", example = "[1,2,3]")
    private List<Integer> ids;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "2")
    private Integer organType;

    @Schema(description = "名称", example = "李四")
    private String name;

    @Schema(description = "基础配置")
    private String basicConfig;

    @Schema(description = "认证配置(包含密钥、token等)")
    private String auth;

    @Schema(description = "行政机构logo")
    private String logo;

    @Schema(description = "省份编码", example = "330000")
    private String provinceCode;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市编码", example = "330100")
    private String cityCode;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区域编码", example = "330106")
    private String areaCode;

    @Schema(description = "区域")
    private String area;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "2")
    private List<Integer> organTypeList;

}