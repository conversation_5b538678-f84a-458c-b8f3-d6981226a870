package com.xyy.saas.transmitter.server.controller.admin.organ.vo;

import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganNetworkConfigDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import java.util.List;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 医药行业行政机构 Response VO")
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransmissionOrganRespVO extends BaseDto {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15847")
    @ExcelProperty("主键ID")
    private Integer id;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer organType;

    @Schema(description = "机构类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "医保")
    @ExcelProperty("机构类型")
    private String organTypeDesc;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "网络配置")
    @ExcelProperty("网络配置")
    private List<TransmissionOrganNetworkConfigDTO> networkConfig;

    @Schema(description = "基础配置")
    @ExcelProperty("基础配置")
    private String basicConfig;

    @Schema(description = "认证配置(包含密钥、token等)")
    @ExcelProperty("认证配置(包含密钥、token等)")
    private String auth;

    @Schema(description = "行政机构logo")
    @ExcelProperty("行政机构logo")
    private String logo;

    @Schema(description = "省份编码", example = "330000")
    private String provinceCode;

    @Schema(description = "省份")
    @ExcelProperty("省份")
    private String province;

    @Schema(description = "城市编码", example = "330100")
    private String cityCode;

    @Schema(description = "城市")
    @ExcelProperty("城市")
    private String city;

    @Schema(description = "区域编码", example = "330106")
    private String areaCode;

    @Schema(description = "区域")
    @ExcelProperty("区域")
    private String area;

    @Schema(description = "租户数")
    @ExcelProperty("租户数")
    private Long tenantCount;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}