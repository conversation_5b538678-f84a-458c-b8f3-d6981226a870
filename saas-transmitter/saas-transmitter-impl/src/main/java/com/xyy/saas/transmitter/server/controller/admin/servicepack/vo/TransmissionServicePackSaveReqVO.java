package com.xyy.saas.transmitter.server.controller.admin.servicepack.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 服务包新增/修改 Request VO")
@Data
public class TransmissionServicePackSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21563")
    private Integer id;

    @Schema(description = "服务包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "服务包名称不能为空")
    private String name;

    @Schema(description = "医药行业行政机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12781")
    @NotNull(message = "医药行业行政机构ID不能为空")
    private Integer organId;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "1")
    @NotNull(message = "机构类型不能为空")
    private Integer organType;

    @Schema(description = "省份编码", example = "330000")
    private String provinceCode;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市编码", example = "330100")
    private String cityCode;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区域编码", example = "330106")
    private String areaCode;

    @Schema(description = "区域")
    private String area;

    @Schema(description = "协议配置包id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8583")
    @NotNull(message = "协议配置包id不能为空")
    private Integer configPackageId;

    @Schema(description = "动态库资源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dllResource;

    @Schema(description = "动态库版本", example = "1.0.0")
    private String dllVersion;

    @Schema(description = "小票模板资源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ticketResource;

    @Schema(description = "账单模板资源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billResource;

    @Schema(description = "拓展资源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String extResource;

    @Schema(description = "接口文档", requiredMode = Schema.RequiredMode.REQUIRED)
    private String apiDoc;

    @Schema(description = "版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122；）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long version;

    @Schema(description = "环境：0-测试；1-灰度；2-上线；", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "环境：0-测试；1-灰度；2-上线；不能为空")
    private Integer env;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否禁用不能为空")
    private Boolean disable;

}