package com.xyy.saas.transmitter.server.controller.admin.task.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 数据传输-记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionTaskRecordPageReqVO extends PageParam {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "主键IDs")
    private List<Long> ids;

    @Schema(description = "上游任务ID")
    @ExcelProperty("上游任务ID")
    private Long upstreamTaskId;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "门店ids")
    private List<Long> tenantIds;

    @Schema(description = "业务编号", example = "BIZ202502240001")
    private String businessNo;

    @Schema(description = "三方服务商ID", example = "13508")
    private Integer organId;

    @Schema(description = "服务包ID", example = "13508")
    private Integer servicePackId;

    @Schema(description = "协议配置节点ID", example = "26608")
    private Integer configItemId;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "2")
    private Integer organType;

    @Schema(description = "业务类型（比如:1-药监-日结存、2-药监-商品信息...）", example = "1")
    private Integer nodeType;

    @Schema(description = "接口编码")
    private String apiCode;

    @Schema(description = "原始参数（json格式）")
    private String originalParams;

    @Schema(description = "请求参数(JSON格式)")
    private String requestParams;

    @Schema(description = "响应结果(JSON格式)")
    private String responseResult;

    @Schema(description = "请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)", example = "2")
    private Integer requestStatus;

    @Schema(description = "请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)", example = "2")
    private List<Integer> requestStatusList;

    @Schema(description = "允许重试", example = "true")
    private Boolean allowRetry;

    @Schema(description = "重试次数", example = "21066")
    private Integer retryCount;

    @Schema(description = "最大重试次数", example = "7915")
    private Integer maxRetryCount;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "预计请求时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expectedTime;

    @Schema(description = "实际请求时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] actualTime;

    @Schema(description = "完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] completeTime;

    @Schema(description = "优先级(0-10，越大优先级越高)")
    private Integer priority;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "姓名", example = "张三")
    private String fullName;

    @Schema(description = "身份证号", example = "330106199001011234")
    private String idCard;

}