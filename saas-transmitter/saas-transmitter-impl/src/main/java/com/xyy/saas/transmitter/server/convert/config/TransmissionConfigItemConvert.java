package com.xyy.saas.transmitter.server.convert.config;

import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface TransmissionConfigItemConvert {

    TransmissionConfigItemConvert INSTANCE = Mappers.getMapper(TransmissionConfigItemConvert.class);

    /**
     * DO 转 DTO
     */
    TransmissionConfigItemDTO convert2DTO(TransmissionConfigItemDO bean);

    /**
     * DO 列表转 DTO 列表
     */
    List<TransmissionConfigItemDTO> convert2DTOList(List<TransmissionConfigItemDO> list);

    /**
     * DTO 转 DO
     */
    TransmissionConfigItemDO convert2DO(TransmissionConfigItemDTO bean);

    /**
     * DTO 列表转 DO 列表
     */
    List<TransmissionConfigItemDO> convert2DOList(List<TransmissionConfigItemDTO> list);

    /**
     * 复制配置项 DO
     */
    default TransmissionConfigItemDO copyConfigItem(TransmissionConfigItemDO original, Integer newPackageId) {
        if (original == null) {
            return null;
        }
        TransmissionConfigItemDO result = convert(original);
        // 设置新的关联关系和基础字段
        result.setId(null);
        result.setConfigPackageId(newPackageId);
        result.setCreateTime(null);
        result.setUpdateTime(null);
        return result;
    }

    /**
     * DO 转换为 DO（用于复制）
     */
    TransmissionConfigItemDO convert(TransmissionConfigItemDO bean);

    /**
     * DO 转换为 DO（用于复制）
     */
    TransmissionConfigItemDO convert(TransmissionConfigItemSaveReqVO reqVO);
}
