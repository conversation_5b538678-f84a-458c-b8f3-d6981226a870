package com.xyy.saas.transmitter.server.convert.task;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.transmitter.api.task.dto.TransmissionTaskRecordDTO;
import com.xyy.saas.transmitter.server.dal.dataobject.task.TransmissionTaskRecordDO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import java.util.Map;

import java.util.List;

@Mapper
public interface TransmissionTaskRecordConvert {

    TransmissionTaskRecordConvert INSTANCE = Mappers.getMapper(TransmissionTaskRecordConvert.class);

    TransmissionTaskRecordDO convert(TransmissionTaskRecordSaveReqVO bean);

    TransmissionTaskRecordRespVO convert(TransmissionTaskRecordDO bean);

    List<TransmissionTaskRecordRespVO> convertList(List<TransmissionTaskRecordDO> list);

    List<TransmissionTaskRecordSaveReqVO> convertSaveReqVOList(List<TransmissionTaskRecordDO> list);

    PageResult<TransmissionTaskRecordRespVO> convertPage(PageResult<TransmissionTaskRecordDO> page);

    TransmissionTaskRecordDTO convert2DTO(TransmissionTaskRecordSaveReqVO bean);

    // 填充任务记录的详细信息
    default void fillTaskRecordDetails(TransmissionTaskRecordRespVO taskRecord,
            Map<Long, TenantDto> tenantMap,
            Map<Integer, TransmissionServicePackDTO> servicePackMap,
            Map<Integer, TransmissionOrganDTO> organMap) {
        // 填充租户信息
        TenantDto tenantDto = tenantMap.get(taskRecord.getTenantId());
        if (tenantDto != null) {
            taskRecord.setTenantName(tenantDto.getName());
            taskRecord.setTenantDto(tenantDto);
        }

        // 填充服务包信息
        TransmissionServicePackDTO servicePackDTO = servicePackMap.get(taskRecord.getServicePackId());
        if (servicePackDTO != null) {
            taskRecord.setServicePackDTO(servicePackDTO);
        }

        // 填充机构信息
        TransmissionOrganDTO organDTO = organMap.get(taskRecord.getOrganId());
        if (organDTO != null) {
            taskRecord.setOrganName(organDTO.getName());
            taskRecord.setOrganArea(organDTO.getProvince() + organDTO.getCity() + organDTO.getArea());
            if (taskRecord.getServicePackDTO() != null) {
                taskRecord.getServicePackDTO().setOrgan(organDTO);
            }
        }
    }
}