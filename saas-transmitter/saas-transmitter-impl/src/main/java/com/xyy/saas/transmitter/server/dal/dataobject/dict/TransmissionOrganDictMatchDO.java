package com.xyy.saas.transmitter.server.dal.dataobject.dict;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 服务商数据字典配对 DO
 *
 * <AUTHOR>
 */
@TableName("saas_transmission_organ_dict_match")
@KeySequence("saas_transmission_organ_dict_match_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransmissionOrganDictMatchDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 医药行业行政机构ID
     */
    private Integer organId;
    /**
     * 字典类型
     */
    private String dictType;
    /**
     * saas字典id
     */
    private Long dictId;
    /**
     * 服务商字典id
     */
    private Long organDictId;
    /**
     * 配对状态：0未配对 1已配对
     */
    private Integer status;

}