package com.xyy.saas.transmitter.server.dal.dataobject.task;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 数据传输-记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_transmission_task_record")
// @KeySequence("saas_transmission_task_record_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransmissionTaskRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 上游任务ID
     */
    private Long upstreamTaskId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 业务编号
     */
    private String businessNo;
    /**
     * 服务包ID
     */
    private Integer servicePackId;
    /**
     * 协议配置节点ID
     */
    private Integer configItemId;
    /**
     * 医药行业行政机构ID
     */
    private Integer organId;
    /**
     * 机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）
     */
    private Integer organType;
    /**
     * 业务类型（比如:1-药监-日结存、2-药监-商品信息...）
     */
    private Integer nodeType;
    /**
     * 接口编码
     */
    private String apiCode;
    /**
     * 姓名
     */
    private String fullName;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 原始参数（json格式）
     */
    private String originalParams;
    /**
     * 请求参数(JSON格式)
     */
    private String requestParams;
    /**
     * 响应结果(JSON格式)
     */
    private String responseResult;
    /**
     * 请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)
     */
    private Integer requestStatus;
    /**
     * 允许重试
     */
    private Boolean allowRetry;
    /**
     * 重试次数
     */
    private Integer retryCount;
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 预计请求时间
     */
    private LocalDateTime expectedTime;
    /**
     * 实际请求时间
     */
    private LocalDateTime actualTime;
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    /**
     * 优先级(0-10，越大优先级越高)
     */
    private Integer priority;

}