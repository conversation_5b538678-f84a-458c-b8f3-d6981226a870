package com.xyy.saas.transmitter.server.dal.mysql.dict;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchPageReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictMatchDO;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 服务商数据字典配对 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionOrganDictMatchMapper extends BaseMapperX<TransmissionOrganDictMatchDO> {

    default PageResult<TransmissionOrganDictMatchDO> selectPage(TransmissionOrganDictMatchPageReqVO reqVO) {
        return selectPage(reqVO, getQueryWrapper(reqVO));
    }

    default List<TransmissionOrganDictMatchDO> selectByCondition(TransmissionOrganDictMatchPageReqVO reqVO) {
        return selectList(getQueryWrapper(reqVO));

    }

    private static LambdaQueryWrapperX<TransmissionOrganDictMatchDO> getQueryWrapper(TransmissionOrganDictMatchPageReqVO reqVO) {
        return new LambdaQueryWrapperX<TransmissionOrganDictMatchDO>()
            .eqIfPresent(TransmissionOrganDictMatchDO::getOrganId, reqVO.getOrganId())
            .eqIfPresent(TransmissionOrganDictMatchDO::getDictType, reqVO.getDictType())
            .inIfPresent(TransmissionOrganDictMatchDO::getDictId, reqVO.getDictIds())
            .eqIfPresent(TransmissionOrganDictMatchDO::getDictId, reqVO.getDictId())
            .inIfPresent(TransmissionOrganDictMatchDO::getOrganDictId, reqVO.getOrganDictIds())
            .eqIfPresent(TransmissionOrganDictMatchDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(TransmissionOrganDictMatchDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TransmissionOrganDictMatchDO::getId);
    }


    default void updateMatch(List<Long> ids, Long organDictId, Integer status, Long updater) {
        update(new LambdaUpdateWrapper<TransmissionOrganDictMatchDO>()
            .set(TransmissionOrganDictMatchDO::getOrganDictId, organDictId)
            .set(TransmissionOrganDictMatchDO::getStatus, status)
            .set(BaseDO::getUpdater, updater).set(BaseDO::getUpdateTime, new Date())
            .in(TransmissionOrganDictMatchDO::getId, ids));


    }
}