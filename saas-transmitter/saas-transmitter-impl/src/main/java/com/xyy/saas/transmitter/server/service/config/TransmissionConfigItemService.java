package com.xyy.saas.transmitter.server.service.config;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigItemDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 协议配置 Service 接口
 *
 * <AUTHOR>
 */
public interface TransmissionConfigItemService {

    /**
     * 创建或更新协议配置 如果ID存在则更新，不存在则新增
     *
     * @param reqVO 创建/更新信息
     * @return 编号
     */
    Integer createOrUpdateTransmissionConfigItem(@Valid TransmissionConfigItemSaveReqVO reqVO);

    /**
     * 删除协议配置
     *
     * @param id 编号
     */
    void deleteTransmissionConfigItem(Integer id);

    /**
     * 获得协议配置
     *
     * @param id 编号
     * @return 协议配置
     */
    TransmissionConfigItemDO getTransmissionConfigItem(Integer id);

    /**
     * 获得协议配置分页
     *
     * @param pageReqVO 分页查询
     * @return 协议配置分页
     */
    PageResult<TransmissionConfigItemDO> getTransmissionConfigItemPage(TransmissionConfigItemPageReqVO pageReqVO);

    /**
     * 获取配置项的完整配置（包含所有父配置合并后的结果）
     *
     * @param configItemId 配置项ID
     * @return 合并后的完整配置
     */
    Map<String, Object> getMergedConfigValue(Integer configItemId);

    /**
     * 合并父配置
     *
     * @param configItem 配置项
     */
    void setMergedConfigValue(TransmissionConfigItemDTO configItem);

    /**
     * 获得配置列表不分页
     *
     * @param pageReqVO 查询入参
     */
    List<TransmissionConfigItemDO> getTransmissionConfigItemList(TransmissionConfigItemPageReqVO pageReqVO);

    /**
     * 根据业务节点获取配置Map 查询并组织配置信息
     *
     * @param configPackageIds       配置包ID列表
     * @param nodeType               节点类型
     * @param validateProtocolConfig 是否校验协议配置
     * @return 配置包ID到配置项列表的映射
     */
    Map<Integer, List<TransmissionConfigItemDTO>> getItemMapByNodeType(List<Integer> configPackageIds,
        NodeTypeEnum nodeType,
        boolean validateProtocolConfig);

    /**
     * 复制配置项 将源配置包下的所有配置项复制到新配置包下
     *
     * @param sourcePackageId 源配置包ID
     * @param targetPackageId 目标配置包ID
     */
    void copyConfigItems(Integer sourcePackageId, Integer targetPackageId);


    /**
     * 查询配置包下的功能数
     *
     * @param configPackIds 配置包ids
     * @param disable       是否禁用
     * @return
     */
    Map<Integer, Long> selectItemCountByPackId(List<Integer> configPackIds, Boolean disable);
}