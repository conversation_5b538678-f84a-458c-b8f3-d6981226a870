package com.xyy.saas.transmitter.server.service.config;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_ITEM_CIRCULAR_DEPENDENCY;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_ITEM_DUPLICATE;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_ITEM_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_ITEM_RELATION_PARENT;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_PACKAGE_ONLINE_NOT_ALLOW_MODIFY;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_PROD;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xyy.saas.inquiry.enums.transmitter.DslTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.ServiceEnvEnum;
import com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.convert.config.TransmissionConfigItemConvert;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigItemDO;
import com.xyy.saas.transmitter.server.dal.dataobject.servicepack.TransmissionServicePackDO;
import com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigItemMapper;
import com.xyy.saas.transmitter.server.dal.mysql.servicepack.TransmissionServicePackMapper;
import com.xyy.saas.transmitter.server.service.servicepack.TransmissionServicePackService;
import com.xyy.saas.transmitter.server.util.TransmissionConfigUtils;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

/**
 * 协议配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransmissionConfigItemServiceImpl implements TransmissionConfigItemService {

    @Resource
    private TransmissionConfigItemMapper configItemMapper;

    @Resource
    private TransmissionServicePackMapper servicePackMapper;

    @Resource
    @Lazy
    private TransmissionServicePackService transmissionServicePackService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createOrUpdateTransmissionConfigItem(TransmissionConfigItemSaveReqVO reqVO) {
        validateDuplicateConfig(reqVO);

        // 校验是否存在上线的配置包，或者 如果是禁用，判断已经关联服务包
        validateTransmissionServicePack(reqVO.getConfigPackageId());

        // 检查是否存在循环依赖
        if (reqVO.getParentItemId() != null) {
            checkCircularDependency(reqVO.getId(), reqVO.getParentItemId(), new HashSet<>());
        }

        TransmissionConfigItemDO configItem = TransmissionConfigItemConvert.INSTANCE.convert(reqVO);

        // 设置配置（合并父配置）
        setApiCode(configItem);

        // 更新子配置的apicode
        updateChildrenApiCodes(configItem);

        // 根据是否存在进行保存或更新
        return saveOrUpdateConfigItem(reqVO, configItem);
    }

    private void validateTransmissionServicePack(Integer configPackageId) {
        // 编辑提示：该配置包关联的服务包已上线,不可操作
        TransmissionServicePackPageReqVO reqVO = TransmissionServicePackPageReqVO.builder().configPackageId(configPackageId).env(ServiceEnvEnum.PROD.getCode()).disable(false).build();
        TenantTransmissionServicePackRespDTO servicePackRespDTO = transmissionServicePackService.selectOneServicePack(reqVO);
        if (servicePackRespDTO != null) {
            throw exception(TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_PROD);
        }
    }

    /**
     * 获取配置项的完整配置（包含所有父配置合并后的结果）
     *
     * @param configItemId 配置项ID
     * @return 合并后的完整配置
     */
    @Override
    public Map<String, Object> getMergedConfigValue(Integer configItemId) {
        // 参数校验
        if (configItemId == null) {
            return new LinkedHashMap<>(); // 返回空Map而不是null
        }

        // 获取当前配置项
        TransmissionConfigItemDO configItem = configItemMapper.selectById(configItemId);
        if (configItem == null) {
            throw exception(TRANSMISSION_CONFIG_ITEM_NOT_EXISTS);
        }

        // 获取所有父配置
        List<String> parentConfigs = new ArrayList<>();
        Integer currentParentId = configItem.getParentItemId();

        // 递归获取所有父配置
        while (currentParentId != null) {
            TransmissionConfigItemDO parentConfig = configItemMapper.selectById(currentParentId);
            if (parentConfig == null) {
                break;
            }
            parentConfigs.add(parentConfig.getConfigValue());
            currentParentId = parentConfig.getParentItemId();
        }

        // 合并所有配置
        return TransmissionConfigUtils.mergeYamlWithParents(
            configItem.getConfigValue(),
            parentConfigs);
    }

    /**
     * 合并父配置
     *
     * @param configItem 配置项
     */
    @Override
    public void setMergedConfigValue(TransmissionConfigItemDTO configItem) {

        if (configItem == null) {
            throw exception(TRANSMISSION_CONFIG_ITEM_NOT_EXISTS);
        }

        // 获取所有父配置
        List<String> parentConfigs = new ArrayList<>();
        Integer currentParentId = configItem.getParentItemId();

        // 递归获取所有父配置
        while (currentParentId != null) {
            TransmissionConfigItemDO parentConfig = configItemMapper.selectById(currentParentId);
            if (parentConfig == null) {
                break;
            }
            parentConfigs.add(parentConfig.getConfigValue());
            currentParentId = parentConfig.getParentItemId();
        }

        // 合并所有配置
        String configValue = TransmissionConfigUtils.mapToYaml(
            TransmissionConfigUtils.mergeYamlWithParents(
                configItem.getConfigValue(),
                parentConfigs));
        configItem.setConfigValue(configValue);
    }

    private void setApiCode(TransmissionConfigItemDO configItem) {
        // 获取合并后的配置
        Map<String, Object> mergedConfig = getMergedConfigValue(configItem.getId());

        // 处理协议配置填充接口编号
        if (DslTypeEnum.PROTOCOL.getCode().equals(configItem.getDslType())) {
            Optional.of(TransmissionConfigUtils.extractApiCodes(mergedConfig))
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .ifPresent(configItem::setApiCode);
        }
    }

    /**
     * 更新子配置的apiCode 需要判断所有子配置的配置包和当前配置的配置包是否均为未上线状态，已上线状态的不允许修改
     */
    private void updateChildrenApiCodes(TransmissionConfigItemDO parentItem) {
        // 1. 获取所有层级的子配置
        Set<TransmissionConfigItemDO> allChildren = new HashSet<>();
        Set<Integer> allPackageIds = new HashSet<>();
        allPackageIds.add(parentItem.getConfigPackageId());

        // 递归获取所有子配置
        getAllChildren(parentItem.getId(), allChildren, allPackageIds);

        if (CollectionUtils.isEmpty(allChildren)) {
            return;
        }

        // 2. 检查所有相关配置包状态
        // List<TransmissionConfigPackageDO> packages =
        // configPackageMapper.selectBatchIds(allPackageIds);

        // 3. 检查是否存在线上环境的服务包引用
        List<TransmissionServicePackDO> servicePackList = servicePackMapper.selectList(
            new LambdaQueryWrapperX<TransmissionServicePackDO>()
                .in(TransmissionServicePackDO::getConfigPackageId, allPackageIds)
                .eq(TransmissionServicePackDO::getEnv, ServiceEnvEnum.PROD.getCode()) // 使用枚举检查生产环境
                .eq(TransmissionServicePackDO::getDeleted, false));

        if (!CollectionUtils.isEmpty(servicePackList)) {
            throw exception(TRANSMISSION_CONFIG_PACKAGE_ONLINE_NOT_ALLOW_MODIFY);
        }

        // 4. 更新所有子配置
        for (TransmissionConfigItemDO child : allChildren) {
            // 获取子配置的完整配置（包含父配置）
            Map<String, Object> childMergedConfig = getMergedConfigValue(child.getId());

            // 更新子配置的apiCode
            if (DslTypeEnum.PROTOCOL.getCode().equals(child.getDslType())) {
                String newApiCodes = TransmissionConfigUtils.extractApiCodes(childMergedConfig);
                child.setApiCode(newApiCodes);
                configItemMapper.updateById(child);
            }
        }
    }

    /**
     * 递归获取所有层级的子配置
     *
     * @param parentId      父配置ID
     * @param allChildren   存储所有子配置的集合
     * @param allPackageIds 存储所有相关配置包ID的集合
     */
    private void getAllChildren(Integer parentId, Set<TransmissionConfigItemDO> allChildren,
        Set<Integer> allPackageIds) {
        List<TransmissionConfigItemDO> children = configItemMapper.selectList(
            new LambdaQueryWrapperX<TransmissionConfigItemDO>()
                .eq(TransmissionConfigItemDO::getParentItemId, parentId));

        if (CollectionUtils.isEmpty(children)) {
            return;
        }

        for (TransmissionConfigItemDO child : children) {
            if (allChildren.add(child)) { // 如果是新的子配置
                allPackageIds.add(child.getConfigPackageId());
                // 递归获取该子配置的子配置
                getAllChildren(child.getId(), allChildren, allPackageIds);
            }
        }
    }

    private Integer saveOrUpdateConfigItem(TransmissionConfigItemSaveReqVO reqVO, TransmissionConfigItemDO configItem) {
        TransmissionConfigItemDO existingItem = Optional.ofNullable(reqVO.getId())
            .map(configItemMapper::selectById)
            .orElse(null);

        if (existingItem != null) {
            configItemMapper.updateById(configItem);
            configItemMapper.update(new UpdateWrapper<TransmissionConfigItemDO>().set("parent_item_id", reqVO.getParentItemId()).eq("id", existingItem.getId()));
            return existingItem.getId();
        } else {
            configItemMapper.insert(configItem);
            return configItem.getId();
        }
    }

    private void validateDuplicateConfig(TransmissionConfigItemSaveReqVO reqVO) {
        if (isConfigDuplicate(reqVO)) {
            throw exception(TRANSMISSION_CONFIG_ITEM_DUPLICATE);
        }
    }

    private boolean isConfigDuplicate(TransmissionConfigItemSaveReqVO reqVO) {
        TransmissionConfigItemDO duplicate = configItemMapper.selectOne(
            new LambdaQueryWrapperX<TransmissionConfigItemDO>()
                .eq(TransmissionConfigItemDO::getConfigPackageId, reqVO.getConfigPackageId())
                .eq(TransmissionConfigItemDO::getDescription, reqVO.getDescription())
                .eq(TransmissionConfigItemDO::getNodeType, reqVO.getNodeType())
                .eq(TransmissionConfigItemDO::getDslType, reqVO.getDslType())
                .neIfPresent(TransmissionConfigItemDO::getId, reqVO.getId())
                .last("LIMIT 1")); // 只需一条即可判断重复

        return duplicate != null;
    }

    /**
     * 检查配置项是否存在循环依赖
     *
     * @param currentId  当前配置项ID（新建时为null）
     * @param parentId   父配置项ID
     * @param visitedIds 已访问的配置项ID集合
     */
    private void checkCircularDependency(Integer currentId, Integer parentId, Set<Integer> visitedIds) {
        // 如果当前节点已经访问过，说明存在循环依赖
        if (!visitedIds.add(parentId)) {
            throw exception(TRANSMISSION_CONFIG_ITEM_CIRCULAR_DEPENDENCY);
        }

        // 获取父配置项
        TransmissionConfigItemDO parent = configItemMapper.selectById(parentId);
        if (parent == null) {
            return; // 父配置项不存在，直接返回
        }

        // 如果是更新操作，检查父配置项是否指向当前配置项
        if (currentId != null && currentId.equals(parent.getParentItemId())) {
            throw exception(TRANSMISSION_CONFIG_ITEM_CIRCULAR_DEPENDENCY);
        }

        // 如果父配置项还有父配置项，继续检查
        if (parent.getParentItemId() != null) {
            checkCircularDependency(currentId, parent.getParentItemId(), visitedIds);
        }
    }

    @Override
    public void deleteTransmissionConfigItem(Integer id) {
        validateTransmissionConfigItemExists(id);
        configItemMapper.deleteById(id);
    }

    private void validateTransmissionConfigItemExists(Integer id) {
        if (configItemMapper.selectById(id) == null) {
            throw exception(TRANSMISSION_CONFIG_ITEM_NOT_EXISTS);
        }
        // 判断当前配置是否是其他父节点
        if (configItemMapper.selectItemCount(TransmissionConfigItemPageReqVO.builder().parentItemId(id).disable(false).build()) > 0) {
            throw exception(TRANSMISSION_CONFIG_ITEM_RELATION_PARENT);
        }
    }

    @Override
    public TransmissionConfigItemDO getTransmissionConfigItem(Integer id) {
        return configItemMapper.selectById(id);
    }

    @Override
    public PageResult<TransmissionConfigItemDO> getTransmissionConfigItemPage(
        TransmissionConfigItemPageReqVO pageReqVO) {
        return configItemMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TransmissionConfigItemDO> getTransmissionConfigItemList(TransmissionConfigItemPageReqVO pageReqVO) {
        return configItemMapper.selectList(pageReqVO);
    }

    /**
     * 获取配置项Map 查询并组织配置项信息
     * <p>
     * 处理流程： 1. 构建查询条件：组装查询参数 2. 批量查询：获取所有配置项 3. 分组处理：按配置包分组 4. 数据过滤：根据节点类型过滤
     * <p>
     * 优化说明： - 批量查询提高性能 - 分组处理便于使用 - 按需过滤减少数据量
     *
     * @param configPackageIds 配置包ID列表
     * @param nodeType         节点类型
     * @return 配置包ID到配置项列表的映射
     */
    @Override
    public Map<Integer, List<TransmissionConfigItemDTO>> getItemMapByNodeType(List<Integer> configPackageIds,
        NodeTypeEnum nodeType,
        boolean validateProtocolConfig) {
        List<TransmissionConfigItemDO> configItems = getTransmissionConfigItemList(
            TransmissionConfigItemPageReqVO.builder()
                .configPackageIds(configPackageIds)
                .nodeType(nodeType.getCode())
                .disable(false)
                .build());

        if (CollectionUtils.isEmpty(configItems)) {
            return new HashMap<>();
        }

        if (validateProtocolConfig && !hasProtocolConfig(configItems)) {
            return new HashMap<>();
        }

        // 合并父配置项并按 configPackageId 分组
        List<TransmissionConfigItemDTO> configItemDTOS = TransmissionConfigItemConvert.INSTANCE.convert2DTOList(configItems);
        return configItemDTOS.stream()
            .peek(this::setMergedConfigValue) // 使用 peek 来处理合并
            .collect(Collectors.groupingBy(TransmissionConfigItemDTO::getConfigPackageId));
    }

    /**
     * 检查是否包含协议配置
     */
    private boolean hasProtocolConfig(List<TransmissionConfigItemDO> configItems) {
        return configItems.stream()
            .anyMatch(item -> DslTypeEnum.PROTOCOL.getCode().equals(item.getDslType()));
    }

    /**
     * 复制配置项 将源配置包下的所有配置项复制到新配置包下
     *
     * @param sourcePackageId 源配置包ID
     * @param targetPackageId 目标配置包ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyConfigItems(Integer sourcePackageId, Integer targetPackageId) {
        // 1. 查询原配置项
        List<TransmissionConfigItemDO> originalItems = configItemMapper.selectList(
            new LambdaQueryWrapperX<TransmissionConfigItemDO>()
                .eq(TransmissionConfigItemDO::getConfigPackageId, sourcePackageId));

        // 2. 复制配置项
        if (!CollectionUtils.isEmpty(originalItems)) {
            for (TransmissionConfigItemDO originalItem : originalItems) {
                TransmissionConfigItemDO newItem = TransmissionConfigItemConvert.INSTANCE
                    .copyConfigItem(originalItem, targetPackageId);
                configItemMapper.insert(newItem);
            }
        }
    }

    @Override
    public Map<Integer, Long> selectItemCountByPackId(List<Integer> configPackIds, Boolean disable) {
        return configItemMapper.selectItemCountByPackId(configPackIds, disable).stream()
            .collect(Collectors.toMap(CommonGroupStatisticsDto::getIntKey, CommonGroupStatisticsDto::getCountValue));
    }
}