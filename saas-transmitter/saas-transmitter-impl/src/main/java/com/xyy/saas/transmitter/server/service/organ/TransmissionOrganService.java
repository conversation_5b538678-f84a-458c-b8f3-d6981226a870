package com.xyy.saas.transmitter.server.service.organ;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationDto;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import jakarta.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import java.util.List;
import java.util.Map;

/**
 * 医药行业行政机构 Service 接口
 *
 * <AUTHOR>
 */
public interface TransmissionOrganService {

    /**
     * 创建医药行业行政机构
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createTransmissionOrgan(@Valid TransmissionOrganSaveReqVO createReqVO);

    /**
     * 更新医药行业行政机构
     *
     * @param updateReqVO 更新信息
     */
    void updateTransmissionOrgan(@Valid TransmissionOrganSaveReqVO updateReqVO);

    /**
     * 删除医药行业行政机构
     *
     * @param id 编号
     */
    void deleteTransmissionOrgan(Integer id);

    /**
     * 获得医药行业行政机构
     *
     * @param id 编号
     * @return 医药行业行政机构
     */
    TransmissionOrganDO getTransmissionOrgan(Integer id);

    /**
     * 获得医药行业行政机构列表
     *
     * @param ids
     * @return
     */
    List<TransmissionOrganDO> getTransmissionOrgans(List<Integer> ids);

    /**
     * 获得医药行业行政机构分页
     *
     * @param pageReqVO 分页查询
     * @return 医药行业行政机构分页
     */
    PageResult<TransmissionOrganRespVO> getTransmissionOrganPage(TransmissionOrganPageReqVO pageReqVO);

    /**
     * 获取租户对接的机构信息及网络配置
     *
     * @param tenantServicePacks 租户开通服务包信息
     * @return 机构ID到机构网络配置code的映射
     */
    Map<Integer, TransmissionOrganDO> getTenantOrganMap(List<TenantServicePackRelationDto> tenantServicePacks);

    /**
     * 查询问诊渠道
     *
     * @param pageReqVO
     * @return
     */
    List<TransmissionOrganRespVO> getInquiryBizChannelType(TransmissionOrganPageReqVO pageReqVO);
}