package com.xyy.saas.transmitter.server.service.servicepack;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_PACKAGE_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_ORGAN_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_SERVICE_PACKAGE_PROD;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_SERVICE_PACK_DISABLE_FAIL;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_SERVICE_PACK_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_SERVICE_PACK_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_SERVICE_PACK_TRANSMISSION_ORGAN_TYPE_NOT_MATCH;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.ServiceEnvEnum;
import com.xyy.saas.inquiry.util.UserUtil;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigPackageDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackRespVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackSaveReqVO;
import com.xyy.saas.transmitter.server.convert.config.TransmissionConfigPackageConvert;
import com.xyy.saas.transmitter.server.convert.organ.TransmissionOrganConvert;
import com.xyy.saas.transmitter.server.convert.servicepack.TransmissionServicePackConvert;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO;
import com.xyy.saas.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.transmitter.server.dal.dataobject.servicepack.TransmissionServicePackDO;
import com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigPackageMapper;
import com.xyy.saas.transmitter.server.dal.mysql.organ.TransmissionOrganMapper;
import com.xyy.saas.transmitter.server.dal.mysql.servicepack.TransmissionServicePackMapper;
import com.xyy.saas.transmitter.server.service.config.TransmissionConfigPackageService;
import com.xyy.saas.transmitter.server.service.organ.TransmissionOrganService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 服务包 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransmissionServicePackServiceImpl implements TransmissionServicePackService {

    @Resource
    private TransmissionServicePackMapper servicePackMapper;

    @Resource
    private TransmissionOrganMapper organMapper;

    @Resource
    private TransmissionConfigPackageMapper configPackageMapper;

    @Resource
    @Lazy
    private TransmissionOrganService organService;

    @Resource
    private TransmissionConfigPackageService configPackageService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private TenantServicePackRelationApi tenantServicePackRelationApi;

    @Override
    public Integer createTransmissionServicePack(TransmissionServicePackSaveReqVO createReqVO) {
        // 设置版本号
        createReqVO.setVersion(generateCurrentVersion());

        // 校验是否存在相同名称和版本的服务包
        validateServicepackExists(null, createReqVO.getName(), createReqVO.getVersion());

        // 校验并获取机构和配置包信息
        TransmissionServicePackDO servicePack = TransmissionServicePackConvert.INSTANCE.convert(createReqVO);
        validateAndSetOrganType(servicePack);

        // 从机构信息中获取地区信息
        TransmissionOrganDO organ = validateOrganExists(createReqVO.getOrganId());
        TransmissionServicePackConvert.INSTANCE.copyAreaInfo(servicePack, organ);

        // 插入
        servicePackMapper.insert(servicePack);
        // 返回
        return servicePack.getId();
    }

    @Override
    public void updateTransmissionServicePack(TransmissionServicePackSaveReqVO updateReqVO) {
        // 设置版本号
        updateReqVO.setVersion(generateCurrentVersion());

        // 校验服务包是否存在
        validateTransmissionServicePackExists(updateReqVO.getId());

        // 校验是否存在相同名称和版本的服务包
        validateServicepackExists(updateReqVO.getId(), updateReqVO.getName(), updateReqVO.getVersion());

        // 校验并获取机构和配置包信息
        TransmissionServicePackDO updateObj = TransmissionServicePackConvert.INSTANCE.convert(updateReqVO);
        validateAndSetOrganType(updateObj);

        // 从机构信息中获取地区信息
        TransmissionOrganDO organ = validateOrganExists(updateReqVO.getOrganId());
        TransmissionServicePackConvert.INSTANCE.copyAreaInfo(updateObj, organ);

        // 如果修改状态为禁用（disable为true），则调用API查询多少个门店正在使用
        if (updateObj.getDisable()) {
            Map<Integer, Long> tenantCountMap = tenantServicePackRelationApi.selectCountByServicePacks(Collections.singletonList(updateReqVO.getId()), TenantServicePackRelationStatusEnum.OPEN.getCode());

            // 如果使用门店数大于0则不能禁用
            if (tenantCountMap.getOrDefault(updateReqVO.getId(), 0L) > 0) {
                throw exception(TRANSMISSION_SERVICE_PACK_DISABLE_FAIL, tenantCountMap.get(updateReqVO.getId()));
            }
        }

        // 更新
        servicePackMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransmissionServicePack(Integer id) {
        // 校验存在
        validateTransmissionServicePackExists(id);
        // 删除
        servicePackMapper.deleteById(id);
    }

    private void validateTransmissionServicePackExists(Integer id) {
        TransmissionServicePackDO servicePackDO = servicePackMapper.selectById(id);
        if (servicePackDO == null) {
            throw exception(TRANSMISSION_SERVICE_PACK_NOT_EXISTS);
        }
        // 上线状态不可编辑和删除
        if (Objects.equals(servicePackDO.getEnv(), ServiceEnvEnum.PROD.getCode())) {
            throw exception(TRANSMISSION_SERVICE_PACKAGE_PROD);
        }
    }

    @Override
    public TransmissionServicePackRespVO getTransmissionServicePack(Integer id) {
        TransmissionServicePackRespVO servicepackRespVO = TransmissionServicePackConvert.INSTANCE.convert(servicePackMapper.selectById(id));

        //设置协议包信息
        servicepackRespVO.setConfigPackage(TransmissionConfigPackageConvert.INSTANCE.convert2RespVO(configPackageMapper.selectById(servicepackRespVO.getConfigPackageId())));

        //设置机构信息
        servicepackRespVO.setOrgan(TransmissionOrganConvert.INSTANCE.convert(organMapper.selectById(servicepackRespVO.getOrganId())));

        return servicepackRespVO;
    }

    @Override
    public PageResult<TransmissionServicePackRespVO> getTransmissionServicePackPage(TransmissionServicePackPageReqVO pageReqVO) {

        IPage<TransmissionServicePackDO> packDOIPage = servicePackMapper.selectTransmissionServicePackPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);

        PageResult<TransmissionServicePackRespVO> pageResult = TransmissionServicePackConvert.INSTANCE.convertPage(packDOIPage);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        // 补充用户信息
        UserUtil.fillUserInfo(pageResult.getList(), adminUserApi::getUserNameMap);

        //调用api查询使用该服务包的门店数
        List<Integer> servicePackIds = pageResult.getList().stream().map(TransmissionServicePackRespVO::getId).toList();
        Map<Integer, Long> tenantCountMap = tenantServicePackRelationApi.selectCountByServicePacks(servicePackIds, TenantServicePackRelationStatusEnum.OPEN.getCode());

        // 获取配置包 + 三方服务商
        Map<Integer, TransmissionOrganDO> organDOMap = organService.getTransmissionOrgans(pageResult.getList().stream().map(TransmissionServicePackRespVO::getOrganId).distinct().collect(Collectors.toList()))
            .stream().collect(Collectors.toMap(TransmissionOrganDO::getId, Function.identity()));

        Map<Integer, TransmissionConfigPackageDO> configPackageDOMap = configPackageService.getTransmissionConfigPackages(
                pageResult.getList().stream().map(TransmissionServicePackRespVO::getConfigPackageId).distinct().collect(Collectors.toList()))
            .stream().collect(Collectors.toMap(TransmissionConfigPackageDO::getId, Function.identity()));

        //回显信息
        pageResult.getList().forEach(item -> {
            item.setTenantCount(tenantCountMap.getOrDefault(item.getId(), 0L));
            item.setOrgan(TransmissionOrganConvert.INSTANCE.convert(organDOMap.get(item.getOrganId())));
            item.setOrganName(organDOMap.getOrDefault(item.getOrganId(), new TransmissionOrganDO()).getName());
            item.setConfigPackage(TransmissionConfigPackageConvert.INSTANCE.convert2RespVO(configPackageDOMap.get(item.getConfigPackageId())));
            item.setConfigPackageName(configPackageDOMap.getOrDefault(item.getConfigPackageId(), new TransmissionConfigPackageDO()).getName());
        });

        return pageResult;
    }

    @Override
    public List<TenantTransmissionServicePackRespDTO> selectList(TransmissionServicePackPageReqVO reqVO) {

        return TransmissionServicePackConvert.INSTANCE.convert(servicePackMapper.selectList(reqVO));
    }


    @Override
    public TenantTransmissionServicePackRespDTO selectOneServicePack(TransmissionServicePackPageReqVO reqVO) {
        TransmissionServicePackDO servicePackDO = servicePackMapper.selectOneByCondition(reqVO);
        return TransmissionServicePackConvert.INSTANCE.convertRespDto(servicePackDO);
    }

    @Override
    public Long selectCountServicePack(TransmissionServicePackPageReqVO reqVO) {
        return servicePackMapper.selectCountServicePack(reqVO);
    }

    /**
     * 查询服务包配置 根据租户和业务信息查询相关的服务包配置
     * <p>
     * 查询流程： 1. 参数校验：验证必要参数的有效性 2. 查询关系：获取租户绑定的服务包关系 3. 获取配置：查询服务包的详细配置 4. 数据过滤：根据业务类型过滤配置
     * <p>
     * 异常处理： - 查询失败返回空列表 - 记录详细的错误日志 - 保证查询过程的稳定性
     *
     * @param tenantId               租户ID
     * @param organType              机构类型
     * @param nodeType               节点类型
     * @param servicePackId          服务包ID
     * @param validateProtocolConfig 是否需要验证协议配置存在
     * @return 服务包配置列表
     */
    @Override
    public List<TransmissionServicePackDTO> queryTenantServicePackByNode(Long tenantId, Integer organType,
        NodeTypeEnum nodeType, Integer servicePackId, boolean validateProtocolConfig) {

        // 1. 获取租户绑定的有效服务包
        List<TransmissionServicePackDTO> servicePacks = getTenantServicePacks(tenantId, servicePackId, organType);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(servicePacks)) {
            return new ArrayList<>();
        }

        // 2. 过滤并组装服务包配置信息
        return filterAndAssembleServicePackConfigs(servicePacks, nodeType, validateProtocolConfig);
    }

    /**
     * 校验服务包是否存在
     *
     * @param id      服务包ID
     * @param name    服务包名称
     * @param version 服务包版本
     */
    private void validateServicepackExists(Integer id, String name, Long version) {
        TransmissionServicePackDO existingServicePack = servicePackMapper
            .selectByNameAndVersion(name, version);
        if (existingServicePack != null && !existingServicePack.getId().equals(id)) {
            throw exception(TRANSMISSION_SERVICE_PACK_EXISTS);
        }
    }

    /**
     * 生成当前版本号（年月日小时）
     */
    private Long generateCurrentVersion() {
        return Long.parseLong(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHH")));
    }

    private TransmissionOrganDO validateOrganExists(Integer organId) {
        TransmissionOrganDO organ = organMapper.selectById(organId);
        if (organ == null) {
            throw exception(TRANSMISSION_ORGAN_NOT_EXISTS);
        }
        return organ;
    }

    private TransmissionConfigPackageDO validateConfigPackageExists(Integer configPackageId) {
        TransmissionConfigPackageDO configPackage = configPackageMapper.selectById(configPackageId);
        if (configPackage == null) {
            throw exception(TRANSMISSION_CONFIG_PACKAGE_NOT_EXISTS);
        }
        return configPackage;
    }

    /**
     * 校验机构和配置包信息并设置机构类型
     *
     * @param servicepack 服务包对象
     */
    private void validateAndSetOrganType(TransmissionServicePackDO servicepack) {
        // 1. 校验机构和配置包是否存在
        TransmissionOrganDO organ = validateOrganExists(servicepack.getOrganId());
        TransmissionConfigPackageDO configPackage = validateConfigPackageExists(servicepack.getConfigPackageId());

        // 2. 校验机构类型和配置包类型是否匹配
        if (!organ.getOrganType().equals(configPackage.getOrganType())) {
            throw exception(TRANSMISSION_SERVICE_PACK_TRANSMISSION_ORGAN_TYPE_NOT_MATCH);
        }

        // 3. 设置机构类型
        servicepack.setOrganType(organ.getOrganType());
    }

    /**
     * 获取租户关联的服务包 查询并组装完整的服务包信息
     * <p>
     * 处理流程： 1. 获取有效服务包：查询未禁用的服务包 2. 关联机构信息：获取并关联机构数据 3. 数据转换：转换为DTO对象 4. 数据过滤：过滤无效的数据
     * <p>
     * 数据校验： - 服务包状态校验 - 机构信息完整性校验 - 关联关系有效性校验
     *
     * @param tenantId      租户id
     * @param servicePackId 服务包ID（可为空）
     * @param organType     机构类型
     * @return 有效的服务包列表
     */
    private List<TransmissionServicePackDTO> getTenantServicePacks(Long tenantId, Integer servicePackId, Integer organType) {

        // 1. 获取租户和服务包关联关系
        TenantServicePackRelationReqDto reqDto = TenantServicePackRelationReqDto.builder()
            .tenantId(tenantId)
            .servicePackId(servicePackId)
            .organType(organType)
            .status(TenantServicePackRelationStatusEnum.OPEN.getCode())
            .build();
        List<TenantServicePackRelationDto> relationDtos = tenantServicePackRelationApi.selectByCondition(reqDto);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(relationDtos)) {
            log.warn("[getTenantServicePacks][租户({})未绑定服务包]", tenantId);
            return new ArrayList<>();
        }

        // 2. 获取租户开通的有效服务包
        List<Integer> servicePackIds = relationDtos.stream()
            .map(TenantServicePackRelationDto::getServicePackId)
            .toList();

        List<TenantTransmissionServicePackRespDTO> servicePacks = selectList(
            TransmissionServicePackPageReqVO.builder()
                .ids(servicePackIds)
                .organType(organType)
                .disable(false)
                .build());
        if (CollectionUtils.isEmpty(servicePacks)) {
            log.warn("[getValidServicePacksWithRelations][租户绑定的服务包({})不存在或已禁用]", servicePackIds);
            return new ArrayList<>();
        }

        // 3. 获取并关联有效机构
        Map<Integer, TransmissionOrganDO> organMap = organService.getTenantOrganMap(relationDtos);
        if (organMap.isEmpty()) {
            return new ArrayList<>();
        }

        // 4. 转换为DTO并设置机构信息
        return servicePacks.stream()
            .map(pack -> {
                TransmissionServicePackDTO dto = TransmissionServicePackConvert.INSTANCE.convertRespDTO2DTO(pack);
                if (dto == null) {
                    return null;
                }
                TransmissionOrganDO organ = organMap.get(pack.getOrganId());
                if (organ != null) {
                    dto.setOrgan(TransmissionOrganConvert.INSTANCE.convert2DTO(organ));
                }
                return dto;
            })
            .filter(Objects::nonNull)
            .filter(dto -> dto.getOrgan() != null)
            .toList();
    }

    /**
     * 过滤和组装服务包配置 处理流程: 1. 获取有效配置包 2. 获取节点配置项 3. 获取公共配置项 4. 组装完整配置
     *
     * @param servicePacks           服务包列表
     * @param nodeType               节点类型
     * @param validateProtocolConfig 是否需要验证协议配置
     * @return 组装后的服务包列表
     */
    private List<TransmissionServicePackDTO> filterAndAssembleServicePackConfigs(
        List<TransmissionServicePackDTO> servicePacks, NodeTypeEnum nodeType, boolean validateProtocolConfig) {

        // 1. 获取配置包ID列表
        List<Integer> configPackageIds = servicePacks.stream()
            .map(TransmissionServicePackDTO::getConfigPackageId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configPackageIds)) {
            log.warn("[filterAndAssembleServicePackConfigs][配置包ID列表为空]");
            return new ArrayList<>();
        }

        // 2. 获取完整配置包
        Map<Integer, TransmissionConfigPackageDTO> configPackageMap = configPackageService.getConfigPackageMap(configPackageIds, nodeType, validateProtocolConfig);
        if (configPackageMap.isEmpty()) {
            log.warn("[filterAndAssembleServicePackConfigs][未找到有效的配置包] configPackageIds:{}", configPackageIds);
            return new ArrayList<>();
        }

        // 3. 设置配置包并过滤有效的服务包
        return servicePacks.stream()
            .peek(pack -> pack.setConfigPackage(configPackageMap.get(pack.getConfigPackageId())))
            .filter(pack -> pack.getConfigPackage() != null) // 合并条件检查
            .collect(Collectors.toList());
    }

}