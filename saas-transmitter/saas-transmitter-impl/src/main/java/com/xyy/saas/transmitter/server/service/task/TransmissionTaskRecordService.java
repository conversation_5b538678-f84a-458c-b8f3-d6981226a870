package com.xyy.saas.transmitter.server.service.task;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 数据传输-记录 Service 接口
 *
 * <AUTHOR>
 */
public interface TransmissionTaskRecordService {

    /**
     * 创建 或 更新数据传输-记录
     *
     * @param mainTask        主任务信息
     * @param downstreamTasks 下游任务信息
     */
    void createOrUpdateTask(TransmissionTaskRecordSaveReqVO mainTask,
        List<TransmissionTaskRecordSaveReqVO> downstreamTasks);

    /**
     * 创建数据传输-记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransmissionTaskRecord(@Valid TransmissionTaskRecordSaveReqVO createReqVO);

    /**
     * 更新数据传输-记录
     *
     * @param updateReqVO 更新信息
     */
    void updateTransmissionTaskRecord(TransmissionTaskRecordSaveReqVO updateReqVO);

    /**
     * 删除数据传输-记录
     *
     * @param id 编号
     */
    void deleteTransmissionTaskRecord(Long id);

    /**
     * 获得数据传输-记录
     *
     * @param id 编号
     * @return 数据传输-记录
     */
    TransmissionTaskRecordRespVO getTransmissionTaskRecord(Long id);

    /**
     * 获得数据传输-记录分页
     *
     * @param pageReqVO 分页查询
     * @return 数据传输-记录分页
     */
    PageResult<TransmissionTaskRecordRespVO> getTransmissionTaskRecordPage(TransmissionTaskRecordPageReqVO pageReqVO);

    PageResult<TransmissionTaskRecordRespVO> getTaskRecordPage(TransmissionTaskRecordPageReqVO pageReqVO);

    /**
     * 获取传输任务记录列表
     *
     * @param reqVO 查询条件
     * @return 任务记录列表
     */
    List<TransmissionTaskRecordRespVO> getTransmissionTaskRecordList(TransmissionTaskRecordPageReqVO reqVO);

}