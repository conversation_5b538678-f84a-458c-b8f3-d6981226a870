package com.xyy.saas.transmitter.server.service.transmission.processor.his;

import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import org.springframework.stereotype.Component;

/**
 * HIS对接基础处理器
 * 提供 HIS对接通用的处理逻辑
 * 
 * 核心功能：
 * 1. 通用参数处理
 * 2. 基础校验逻辑
 * 3.  HIS对接特定配置处理
 */
@Component
public abstract class HisLogicConfigProcessor extends LogicConfigProcessor {
    @Override
    public OrganTypeEnum getOrganType() {
        return OrganTypeEnum.HIS;
    }

}