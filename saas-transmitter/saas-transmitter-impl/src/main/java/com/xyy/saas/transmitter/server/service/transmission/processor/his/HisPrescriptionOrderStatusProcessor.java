package com.xyy.saas.transmitter.server.service.transmission.processor.his;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.FilterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.LogicValidationConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreValidationConfig;
import org.springframework.stereotype.Component;

/**
 *  HIS对接订单状态处理器
 * 处理 HIS对接 系统预约订单状态变更相关的业务逻辑
 * 
 * 处理职责：
 * 1. 参数校验：验证订单状态变更参数
 * 2. 数据处理：处理状态变更
 * 3. 结果转换：转换为统一格式
 */
@Component
public class HisPrescriptionOrderStatusProcessor extends HisLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.HIS_PRESCRIPTION_ORDER_STATUS;
    }

    @Override
    protected void validateParameters(TransmissionReqDTO transmissionReqDTO, PreValidationConfig config) {
        super.validateParameters(transmissionReqDTO, config);
        // 添加订单状态变更特有的参数验证逻辑
    }

    @Override
    protected void fillPreProcessParameters(TransmissionReqDTO transmissionReqDTO, PreParameterConfig config) {
        super.fillPreProcessParameters(transmissionReqDTO, config);
        // 添加订单状态变更特有的参数填充逻辑
    }

    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);
        // 添加订单状态变更特有的后置参数处理逻辑
    }

    @Override
    protected boolean preFilter(TransmissionReqDTO transmissionReqDTO, FilterConfig filterConfig) {
        // 添加订单状态变更特有的前置过滤逻辑
        return super.preFilter(transmissionReqDTO, filterConfig);
    }

    @Override
    protected void processLogicConfig(TransmissionReqDTO transmissionReqDTO, TransmissionTaskRecordSaveReqVO task, 
            LogicValidationConfig config, TransmissionServicePackDTO servicePack) {
        super.processLogicConfig(transmissionReqDTO, task, config, servicePack);
        // 添加订单状态变更特有的业务处理逻辑
    }
} 