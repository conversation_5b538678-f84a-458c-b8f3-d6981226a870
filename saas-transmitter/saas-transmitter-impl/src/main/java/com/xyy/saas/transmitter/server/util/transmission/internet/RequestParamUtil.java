// package com.xyy.saas.transmitter.server.util.transmission.internet;
//
// import cn.hutool.core.collection.CollUtil;
// import cn.hutool.core.util.XmlUtil;
// import cn.hutool.json.JSONUtil;
// import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
// import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
// import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
// import com.xyy.saas.inquiry.pojo.transmitter.internet.OutPatientCaseTransmitterDTO;
// import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
// import com.xyy.saas.transmitter.server.service.transmission.processor.internet.InternetSupervisionLogicConfigProcessor.AuxConstant;
// import java.time.format.DateTimeFormatter;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
//
// /**
//  * @Author:chenxiaoyi
//  * @Date:2025/02/27 15:48
//  */
// public class RequestParamUtil {
//
//     /**
//      * 上传签名原文，签名对象为 电子处方的全部内容及关键 标识信息，标识信息包括但 不限于：开方医生姓名及编 号、审方药师姓名及编号、 患者姓名及身份证件号码、 处方流水号、开方时间、诊 断信息、药品信息等 需是原文，而非BASE64编 码后的原文
//      * <CA>
//      * <MedicalRecordNo>String-必填</MedicalRecordNo> 处方流水号
//      * <DoctorCode>String-必填</DoctorCode>//医生编号
//      * <DoctorName>String-必填</DoctorName>//医生姓名
//      * <TrialPharmCode>String-必填</TrialPharmCode> //审方药师编码
//      * <TrialPharmName>String-必填 </TrialPharmCode> //审方药师姓名
//      * <PatientName>String-必填</PatientName>//患者姓名
//      * <PatientSFZH>String-必填</PatientSFZH>//患者身份证号
//      * <RecipeDate></recipeDate>//处方时间
//      * <DiagnosisList>//诊断信息
//      * <Diagnosis>
//      * <diagnosisCode> </diagnosisCode>
//      * <diagnosisName></diagnosisName>
//      * </Diagnosis>
//      * </DiagnosisList>
//      * <DrugList>//药品信息
//      * <Drug>
//      * <hospitalDrugCode></hospitalDrugCode>
//      * <drugCommonName></drugCommonName>
//      * <price></price>
//      * <deliverNumUnit></deliverNumUnit>
//      * <money></money>
//      * </Drug>
//      * </DrugList>
//      * </CA>
//      *
//      * @param data     入参原对象
//      * @param aux      扩展对象
//      * @param isDoctor 是否是医生
//      * @return
//      */
//     public static String getCqCaXmlParam(PrescriptionTransmitterDTO pt, Map<String, Object> aux, boolean isDoctor) {
//         // 门诊病例
//         OutPatientCaseTransmitterDTO outPatientCaseTransmitterDTO = (OutPatientCaseTransmitterDTO) aux.get(AuxConstant.CLINICAL_CASE);
//         // 处方详情
//         List<InquiryPrescriptionDetailRespDTO> detailRespDTOS = (List<InquiryPrescriptionDetailRespDTO>) aux.get(AuxConstant.PRESCRIPTION_DETAIL);
//         // 医生信息
//         InquiryDoctorDto inquiryDoctorDto = (InquiryDoctorDto) aux.get(AuxConstant.DOCTOR_INFO);
//         // 药师信息
//         InquiryPharmacistDto inquiryPharmacistDto = (InquiryPharmacistDto) aux.get(AuxConstant.PHARMACIST_INFO);
//
//         Map<String, Object> map = new HashMap<>();
//
//         map.put("MedicalRecordNo", pt.getPref());
//         map.put("DoctorCode", inquiryDoctorDto.getDoctorHospitalPref());
//         map.put("DoctorName", inquiryDoctorDto.getName());
//         if (!isDoctor) {
//             map.put("TrialPharmCode", inquiryPharmacistDto.getPref());
//             map.put("TrialPharmName", inquiryPharmacistDto.getName());
//         }
//         map.put("PatientName", pt.getFullName());
//         map.put("PatientSFZH", pt.getIdCard());
//         map.put("RecipeDate", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(pt.getOutPrescriptionTime()));
//
//         if (outPatientCaseTransmitterDTO != null && CollUtil.isNotEmpty(outPatientCaseTransmitterDTO.getDiagnosis())) {
//             map.put("DiagnosisList", outPatientCaseTransmitterDTO.getDiagnosis().stream().map(d -> new HashMap<String, Object>() {{
//                 put("Diagnosis", new HashMap<String, Object>() {{
//                     put("diagnosisCode", d.getDiagnosisCode());
//                     put("diagnosisName", d.getDiagnosisName());
//                 }});
//             }}).toList());
//         }
//
//         map.put("DrugList", detailRespDTOS.stream().map(d -> new HashMap<String, Object>() {{
//             put("Drug", new HashMap<String, Object>() {{
//                 put("hospitalDrugCode", d.getProductPref());
//                 put("drugCommonName", d.getCommonName());
//                 put("price", d.getProductPrice());
//                 put("deliverNumUnit", d.getPackageUnit());
//                 put("money", d.getActualAmount());
//             }});
//         }}).toList());
//
//         String xmlStr = XmlUtil.mapToXmlStr(JSONUtil.parseObj(map), "CA");
//         return xmlStr.replaceFirst("<\\?xml[^>]+\\?>", "").trim();
//     }
//
// }
