package com.xyy.saas.transmitter.server.util.transmission.internet;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/24 19:46
 */
@Slf4j
public class RequestSignUtil {

    /**
     * 重庆互联网医院药监Sign签名
     *
     * @param key       key
     * @param secret    密钥
     * @param timestamp 时间戳
     * @param requuid   请求id
     * @return sign
     */
    public static String sign4CqSupervision(String key, String secret, String timestamp, String requuid) {
        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
            String textToSign = timestamp + requuid;
            return Base64.encodeBase64String(hmacSha256.doFinal(textToSign.getBytes(StandardCharsets.UTF_8)));
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("sign4CqSupervision error,key:{},secret:{},timestamp:{},requestId:{}", key, secret, timestamp, requuid, e);
        }
        return null;
    }

    public static void main(String[] args) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {

        // long timestamp = System.currentTimeMillis();
        // String uuid = UUID.randomUUID().toString();
        // String textToSign = timestamp + uuid;
        //
        // Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        // byte[] keyBytes = "19964619acb067cd".getBytes("UTF-8");
        // hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
        // String sign = Base64.encodeBase64String(hmacSha256.doFinal(textToSign.getBytes("UTF-8")));
        //
        // System.out.println(timestamp);
        // System.out.println(uuid);
        // System.out.println(sign);

        // String string = UUID.randomUUID().toString();
        // System.out.println(string);
        System.out.println(sign4CqSupervision("1b22fdf03b00501c", "19964619acb067cd", "1600838247241", "0108cd74-4f14-4be8-bc3f-c15f6ac2ccb5"));
    }

}
