#医药行业行政机构表
DROP TABLE IF EXISTS saas_transmission_organ;
create table saas_transmission_organ
(
  id             int auto_increment primary key comment '主键ID',
  organ_type     int           default 1                 not null comment '机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）',
  name           varchar(100)  default 1                 not null comment '名称',
  network_config text                                    null comment '网络配置',
  basic_config   varchar(2000) default ''                null comment '基础配置',
  logo           varchar(50)   default ''                null comment '行政机构logo',
  province_code  varchar(10)   default ''                null comment '省份code',
  province       varchar(10)   default ''                null comment '省份',
  city_code      varchar(10)   default ''                null comment '城市code',
  city           varchar(10)   default ''                null comment '城市',
  area_code      varchar(20)   default ''                null comment '区域code',
  area           varchar(20)   default ''                null comment '区域',
  remark         varchar(255)  default ''                null comment '备注',
  disable        bit           default b'0'              not null comment '是否禁用',
  creator        varchar(64)   default ''                not null comment '创建者',
  create_time    datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64)   default ''                null comment '更新者',
  update_time    datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit           default b'0'              not null comment '是否删除'
) comment '医药行业行政机构表';

#协议配置包表
DROP TABLE IF EXISTS saas_transmission_config_package;
create table saas_transmission_config_package
(
  id                int auto_increment primary key comment '主键ID',
  organ_type        int          default 1                 not null comment '机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）',
  provider_name     varchar(30)  default ''                not null comment '服务提供商名称',
  parent_package_id int          default null               comment '父配置包id',
  name              varchar(50)  default ''                not null comment '配置包名称',
  version           bigint       default 0                 not null comment '版本号（实际存储：**********；页面展示：服务提供商名称+机构类型名称+日期+小时，比如：创智医保20250122）',
  description       varchar(500) default ''                not null comment '描述',
  disable           bit          default b'0'              not null comment '是否禁用',
  creator           varchar(64)  default ''                not null comment '创建者',
  create_time       datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater           varchar(64)  default ''                null comment '更新者',
  update_time       datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted           bit          default b'0'              not null comment '是否删除'
) comment '配置包表';

#协议配置表
DROP TABLE IF EXISTS saas_transmission_config_item;
create table saas_transmission_config_item
(
  id                int auto_increment primary key comment '主键ID',
  config_package_id int          default 0                 not null comment '协议配置包ID',
  parent_item_id    int          default null               comment '父节点id',
  dsl_type          tinyint      default 1                 not null comment '配置类型（1-视图配置、2-逻辑配置、3-协议配置）',
  node_type         int          default 1                 not null comment '节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）',
  api_code          varchar(100) default ''                not null comment '接口代码',
  description       varchar(500) default ''                not null comment '描述',
  config_value      text                                   null comment '配置值,yaml',
  disable           bit          default b'0'              not null comment '是否禁用',
  creator           varchar(64)  default ''                not null comment '创建者',
  create_time       datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater           varchar(64)  default ''                null comment '更新者',
  update_time       datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted           bit          default b'0'              not null comment '是否删除'
) comment '配置表';

#服务包表
DROP TABLE IF EXISTS saas_transmission_service_pack;
create table saas_transmission_service_pack
(
  id                int auto_increment primary key comment '主键ID',
  name              varchar(50)  default 0                 not null comment '服务包名称',
  organ_id          int          default 0                 not null comment '医药行业行政机构ID',
  organ_type        int          default 1                 not null comment '机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）',
  province_code     varchar(10)  default ''                null comment '省份code',
  province          varchar(10)  default ''                null comment '省份',
  city_code         varchar(10)  default ''                null comment '城市code',
  city              varchar(10)  default ''                null comment '城市',
  area_code         varchar(20)  default ''                null comment '区域code',
  area              varchar(20)  default ''                null comment '区域',
  config_package_id int          default 0                 not null comment '协议配置包id',
  dll_resource      varchar(50)  default ''                null comment '动态库资源',
  dll_version       varchar(20)  default ''                null comment '动态库版本',
  ticket_resource   varchar(500) default ''                null comment '小票模板资源',
  bill_resource     varchar(500) default ''                null comment '账单模板资源',
  ext_resource      varchar(500) default ''                null comment '拓展资源',
  api_doc           varchar(500) default ''                null comment '接口文档',
  version           bigint       default 0                 not null comment '版本号（实际存储：**********；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122；）',
  env               tinyint      default 0                 not null comment '环境：0-测试；1-灰度；2-上线；',
  disable           bit          default b'0'              not null comment '是否禁用',
  creator           varchar(64)  default ''                not null comment '创建者',
  create_time       datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater           varchar(64)  default ''                null comment '更新者',
  update_time       datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted           bit          default b'0'              not null comment '是否删除'
) comment '服务包表';
create unique index uk_name_version on saas_transmission_service_pack (name, version);

#租户当前服务包版本表
DROP TABLE IF EXISTS saas_tenant_transmission_service_pack_relation;
create table saas_tenant_transmission_service_pack_relation
(
  id                   int auto_increment primary key comment '主键ID',
  tenant_id            bigint      default 0                 not null comment '租户ID',
  service_pack_id      int         default 0                 not null comment '服务包ID',
  catalog_id           bigint                                null comment '目录id',
  organ_id             int         default 0                 not null comment '医药行业行政机构ID',
  organ_type           int         default 1                 not null comment '服务机构类型（1-医保、2-药监、3-互联网医院监管、99-其他）',
  service_pack_version bigint      default 0                 not null comment '版本号（实际存储：**********；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122）',
  status               tinyint(2)  default 0                 not null comment '状态 0未开通 1开通',
  ext                  text                                  null comment '扩展信息',
  creator              varchar(64) default ''                not null comment '创建者',
  create_time          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater              varchar(64) default ''                null comment '更新者',
  update_time          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted              bit         default b'0'              not null comment '是否删除'
) comment '门店-开通服务包关系表';
create unique index uk_tenant_package_id on saas_tenant_transmission_service_pack_relation (tenant_id, service_pack_id);


#传输记录表，定时任务自动清理，按照天=30|Size=10W
DROP TABLE IF EXISTS saas_transmission_task_record;
create table saas_transmission_task_record
(
  id               bigint auto_increment primary key comment '主键ID',
  upstream_task_id bigint       default 0                 not null comment '上游任务id',
  tenant_id        bigint       default 0                 not null comment '租户ID',
  business_no      varchar(64)  default ''                not null comment '业务编号',
  service_pack_id  int          default 0                 not null comment '服务包ID',
  config_item_id   int          default 0                 not null comment '协议配置节点ID',
  organ_id         int          default 0                 not null comment '医药行业行政机构ID',
  organ_type       int          default 1                 not null comment '机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）',
  node_type        int          default 0                 not null comment '业务类型（比如:1-药监-日结存、2-药监-商品信息...）',
  api_code         varchar(100) default ''                not null comment '接口编码',
  full_name        varchar(32)  default ''                null comment '姓名',
  id_card          varchar(20)  default ''                null comment '身份证号',
  original_params  text                                   null comment '原始参数（json格式）',
  request_params   text                                   null comment '请求参数(JSON格式)',
  response_result  text                                   null comment '响应结果(JSON格式)',
  request_status   tinyint      default 0                 not null comment '请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)',
  allow_retry      bit          default b'0'              not null comment '允许重试',
  retry_count      int          default 0                 not null comment '重试次数',
  max_retry_count  int          default 3                 not null comment '最大重试次数',
  error_message    varchar(500)                           null comment '错误信息',
  expected_time    datetime                               null comment '预计请求时间',
  actual_time      datetime                               null comment '实际请求时间',
  complete_time    datetime                               null comment '完成时间',
  priority         int          default 0                 not null comment '优先级(0-10，越大优先级越高)',
  creator          varchar(64)  default ''                not null comment '创建者',
  create_time      datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater          varchar(64)  default ''                null comment '更新者',
  update_time      datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted          bit          default b'0'              not null comment '是否删除'
) comment '数据传输-记录表';

DROP TABLE IF EXISTS saas_transmission_organ_dict;
create table saas_transmission_organ_dict
(
  id           bigint auto_increment comment '主键ID' primary key,
  organ_id     int          default 0                 not null comment '医药行业行政机构ID',
  dict_type    varchar(50)  default ''                not null comment '字典类型',
  dict_name    varchar(100) default ''                not null comment '字典名称',
  parent_value varchar(100) default ''                not null comment '父值 空无父值',
  end_node     tinyint(2)   default 0                 not null comment '是否末级节点',
#   inner_code  varchar(100) default ''                not null comment '字典内码',
#   attr        varchar(500) default ''                null comment '扩展属性',
  label        varchar(100) default ''                not null comment '字典标签',
  value        varchar(100) default ''                not null comment '字典值',
  outer_value  varchar(100) default ''                not null comment '字典外码',
  status       tinyint(2)   default 0                 not null comment '状态（0正常 1停用）',

  remark       varchar(500) default ''                null comment '备注',
  sort         int          default 0                 not null comment '排序',

  creator      varchar(64)  default ''                not null comment '创建者',
  create_time  datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater      varchar(64)  default ''                null comment '更新者',
  update_time  datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted      bit          default b'0'              not null comment '是否删除'
) comment '服务商字典表';
create index idx_organ_category on saas_transmission_organ_dict (organ_id, dict_type);

DROP TABLE IF EXISTS saas_transmission_organ_dict_match;
create table saas_transmission_organ_dict_match
(
  id            bigint auto_increment comment '主键ID' primary key,
  organ_id      int         default 0                 not null comment '医药行业行政机构ID',
  dict_type     varchar(50) default ''                not null comment '字典类型',
  dict_id       bigint      default 0                 not null comment 'saas字典id',
  organ_dict_id bigint                                null comment '服务商字典id',
  status        tinyint(2)  default 0                 not null comment '配对状态：0未配对 1已配对 2无法配对',

  creator       varchar(64) default ''                not null comment '创建者',
  create_time   datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater       varchar(64) default ''                null comment '更新者',
  update_time   datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted       bit         default b'0'              not null comment '是否删除'
) comment '服务商数据字典配对表';
create index idx_organ_category on saas_transmission_organ_dict_match (organ_id, dict_type, dict_id);