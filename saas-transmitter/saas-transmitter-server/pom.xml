<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-transmitter</artifactId>
    <version>${revision}</version>
  </parent>


  <modelVersion>4.0.0</modelVersion>
  <artifactId>saas-transmitter-server</artifactId>

  <dependencies>
    <!-- 确保依赖声明在最前面，这样重写yudao的类才能先加载并覆盖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-multi-env</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>


    <!-- 项目内部依赖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-transmitter-impl</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-api</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-hospital-api</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pharmacist-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-drugstore-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-patient-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-signature-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-im-api</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.common</groupId>
      <artifactId>xyy-common-dubbo-client</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${maven-springboot-plugin.version}</version>
        <configuration>
          <mainClass>com.xyy.saas.transmitter.server.TransmitterServerApplication</mainClass>
          <!-- 禁止替换主构建产物: *.jar -->
          <attach>false</attach>
          <!-- 为 Fat Jar 指定分类器（可选） *-boot.jar -->
          <classifier>boot</classifier>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>