package com.xyy.saas.transmitter.server;

import cn.iocoder.yudao.framework.web.config.YudaoWebAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;

@EnableDubbo(scanBasePackages = {"com.xyy.saas.transmitter"})
@SpringBootApplication(scanBasePackages = {"com.xyy.common", "cn.iocoder.yudao.module", "com.xyy.saas.inquiry", "com.xyy.saas.transmitter"})
// @EnableDiscoveryClient
@Slf4j
@Import({YudaoWebAutoConfiguration.class})
public class TransmitterServerApplication {

    public static void main(String[] args) {
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "saas-transmitter-server");

        SpringApplication.run(TransmitterServerApplication.class, args);
    }

}
