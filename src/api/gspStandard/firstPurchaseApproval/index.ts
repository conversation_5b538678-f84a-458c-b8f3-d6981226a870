import request from '@/config/axios'
// 首营商品
export const FirstPurchaseApprovalApi = {
  // 查询查询首营商品分页信息
  getListPage: async (params: any) => {
    return await request.get({ url: `/bpm/approve/product-first-approve/page`, params })
  },
  // 审批前的密码校验
  getValidatePassword: async (params: any) => {
    return request.post({
      url: '/bpm/business-relation/validate',
      data: params
    })
  }
}
