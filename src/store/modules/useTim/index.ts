import { defineStore } from 'pinia'
import { store } from '@/store/index'
import Tim from './Tim'

export const useTimStore = defineStore(
  'tim',
  () => {
    // 初始化tim实例
    const tim = ref()
    function initTim() {
      tim.value = new Tim()
      console.log('tim|tim实例初始化:', tim.value)
    }

    //  聊天实例
    const chat = ref<any>(null)

    function setChat(value) {
      chat.value = value
    }

    //  当前聊天信息
    const chatInfo = reactive({
      accountId: '',
      isLogin: false,
      inquiryPref:"",
      inquiryStatus: -1, // 问诊状态
      total:0,
      pageSize:1,
      pageNum:50,
      loadingStatus:"more",
    })
    // 系统消息数据
    const systemMessage=ref({})
    /**
     * @description
     */
    function setSystemMessage(value){
      systemMessage.value = value
    }

    // 聊天消息列表
    const messageArr = ref<Array<{ [key: string]: any }>>([])

    /**
     * @description 更新聊天列表
     */
    function setMessageArr(value:{ [key: string]: any }, type = 'push') {
      if (type == 'push') {
        messageArr.value.push(value)
      } else {
        messageArr.value.unshift(value)
      }
    }

    return {
      chat,
      setChat,
      chatInfo,
      messageArr,
      tim,
      initTim,
      setMessageArr,
      systemMessage,
      setSystemMessage
    }
  },
  {
    persist: true
  }
)

export function useTimStoreHook() {
  return useTimStore(store)
}
