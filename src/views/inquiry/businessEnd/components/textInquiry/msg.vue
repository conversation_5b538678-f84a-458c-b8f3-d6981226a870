<template>
    <!-- 医生消息 -->
     <div>
    <div v-if="msg.isDoctor == 1" class="msg">
        <!-- 头像 -->
        <div v-if="msg.sex == 0" class="avatar" style="width: 40px;">
            <img src="@/assets/inquiry/help_doctor.png" alt=""/>
        </div>
        <el-popover v-else placement="right-end" popper-class="el-popover-self"  trigger="click">
            <div class="avatar_content">
                <div class="avatar_title">医生信息</div>
                <div class="avatar_header">
                    <div class="avatar" style="width: 40px;">
                        <el-image
                            style="width: 100%; height: 100%; border-radius: 50%;"
                            v-if="doctorInfo.headPortrait != null && doctorInfo.headPortrait != undefined && doctorInfo.headPortrait != ''"
                            :src="doctorInfo.headPortrait"
                            alt="">
                            <template #error>
                                <img v-if="msg.sex == 1" src="@/assets/inquiry/doctor_nan.png" alt=""/>
                                <img v-else src="@/assets/inquiry/doctor_nv.png" alt=""/>
                            </template>
                        </el-image>
                        <img v-else-if="msg.sex == 1" src="@/assets/inquiry/doctor_nan.png" alt=""/>
                        <img v-else src="@/assets/inquiry/doctor_nv.png" alt=""/>
                    </div>
                    <div style="flex: 1; display: flex; align-items: center;">
                        <p style="margin-right: 6px;">{{ doctorInfo.doctorName }}</p>
                        <p>{{ doctorInfo.department }}</p>
                         <el-divider direction="vertical" />
                        <p>{{ doctorInfo.job }}</p>
                    </div>
                </div>
                 <div class="dockerInfo_box_evaluate">
                    <div class="dockerInfo_box_evaluate_left dockerInfo_box_evaluate_box">
                        <div class="user_evaluate">{{ doctorInfo.reception }}</div>
                        <div class="user_detail">接待量</div>
                    </div>
                    <div class="dockerInfo_box_evaluate_center dockerInfo_box_evaluate_box">
                        <div class="user_evaluate">{{ doctorInfo.reply }}</div>
                        <div class="user_detail">回复率</div>
                    </div>
                    <div class="dockerInfo_box_evaluate_right dockerInfo_box_evaluate_box">
                        <div class="user_evaluate">{{ doctorInfo.good }}</div>
                        <div class="user_detail">好评率</div>
                    </div>
                </div>
                <div class="desc">
                    <div class="title_green"><p></p><p>个人简介</p></div>
                    <div>{{ doctorInfo.brief }}</div>
                </div>
                <div class="desc">
                    <div class="title_green"><p></p><p>擅长专业</p></div>
                    <div>{{ doctorInfo.proficient }}</div>
                </div>
            </div>
            <template #reference>
                <div  class="inquiry_list_btn">
                    <div class="avatar" style="width: 40px;">
                        <el-image
                            style="width: 100%; height: 100%; border-radius: 50%;"
                            v-if="doctorInfo.headPortrait != null && doctorInfo.headPortrait != undefined && doctorInfo.headPortrait != ''"
                            :src="doctorInfo.headPortrait"
                            alt="">
                            <template #error>
                                <img v-if="msg.sex == 1" src="@/assets/inquiry/doctor_nan.png" alt=""/>
                                <img v-else src="@/assets/inquiry/doctor_nv.png" alt=""/>
                            </template>
                        </el-image>
                        <img v-else-if="msg.sex == 1" src="@/assets/inquiry/doctor_nan.png" alt=""/>
                        <img v-else src="@/assets/inquiry/doctor_nv.png" alt=""/>
                    </div>
                </div>
            </template>
        </el-popover>

        <!-- 消息 -->
        <div class="content_left">
            <div class="msg_name">{{ msg.name }}&nbsp; {{ msg.date }}</div>
            <div v-if="msg.type == 'text'" class="msg_text">{{ msg.text }}</div>
            <div v-if="msg.type == 'card'" class="left_msg_card">
                <span>处方已开具</span>
                <span @click="checkPrescription">查看处方</span>
            </div>
            <div v-if="msg.type == 'card_err'" class="left_msg_card"
                style="background: #FFB411;  background-image: url('../../../../../assets/inquiry/prescription_bgc_clone.png');">
                <span>医生已取消开方</span>
                <span @click="checkPrescriptionErr">查看处方</span>
            </div>
            <div v-if="msg.type == 'img'">
                <div style="width: 150px; height: 150px;">
                    <el-image
                        style="width: 100%; height: 100%; border-radius: 0px 10px 10px 10px;"
                        ref="previewImage"
                        :preview-src-list="msgUrlList"
                        :src="msg.text"
                        @click.capture="handlePreviewImage()">
                        <template #error>
                            <div class="image-slot"
                                style="background: #383838; display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;">
                                <img src="@/assets/inquiry/img_error.png" alt=""
                                    style="width: 84px; height: 74px; border-radius: 0px 10px 10px 10px;"/>
                            </div>
                        </template>
                        <template #placeholder>
                            <div class="image-slot"
                                style="background: #383838; display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;">
                            </div>
                        </template>
                    </el-image>
                </div>
            </div>
            <div v-if="msg.type == 'audio'" class="msg_text audio_content" @click="playAudio">
                <div class="wifi_symbol">
                    <img v-if="isPlaying" src="@/assets/inquiry/icon-player.gif" alt=""/>
                    <img v-else src="@/assets/inquiry/icon-player.png" alt=""/>
                </div>
                <audio ref="audioPlayer" :src="msg.text" controls id="warningAudio" hidden="true" @ended="onAudioEnded">
                </audio>
                <span class="audio_size">"{{ msg.second }}</span>
            </div>
             <div v-if="msg.type == 'ilnessCard'" class="msg_card" style="cursor: pointer;" >
                <div style="width: 300px;">
                    <div class="card_hd" style="border-radius: 0 10px 0 0" @click="openCard">
                        <img src="@/assets/inquiry/usercard.png" alt=""/>
                        <span>{{westPrescription.title}}--西成药处方</span>
                        <img src="@/assets/inquiry/right.png"  alt="" style="width: 14px;height: 14px"/>
                    </div>
                    <div class="card_bd" style="line-height: 30px;">
                        <div class="card_bd_info">
                            就诊患者：<span>{{westPrescription.patientName}}&nbsp;{{ westPrescription.patientSex == 1 ? '男' : '女' }} &nbsp;{{westPrescription.patientAge}}</span>
                        </div>
                        <div class="card_bd_info">
                            问诊类型：<span>{{ westPrescription.inquiryType  }}</span>
                        </div>
                        <div class="card_bd_info " >
                            <div class="text-ellipsis">开方时间：{{ illnessDetail.illnessDetail  }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 患者消息 -->
    <div v-else-if="msg.isDoctor == 0" class="msg" style="cursor: pointer;" @click="openUseDrugDialog">
        <!-- 消息 -->
        <div class="content_right">
            <div class="msg_name" style="text-align: end;">{{ msg.date }}&nbsp;{{ msg.name }}</div>
            <div v-if="msg.type == 'text'" class="msg_text">
                <span v-loading="msg.status == 'loading'" class="msg_text_loading"></span>
                <span v-if="msg.status == 'fail'" class="msg_error" @click="resendMsgFn">
                    <img style="width: 100%; height: 100%;" src="@/assets/inquiry/reset_error.png" alt=""/>
                </span>
                <span>
                    {{ msg.text }}
                </span>
            </div>
            <div v-if="msg.type == 'card'" class="msg_card" @click="openUseDrugDialog=true">
                <div style="width: 300px;">
                    <div class="card_hd">
                        <img src="@/assets/inquiry/medicine_card.png" alt=""/>
                        <span>用药申请</span>
                    </div>
                    <div class="card_bd">
                        <div class="card_bd_info">
                            <span>{{ inquiryInfo.patientName }}</span>
                            <span class="border_line"></span>
                            <span>{{ inquiryInfo.patientAge }}</span>
                            <span class="border_line"></span>
                            <span>{{ inquiryInfo.patientSex == 1 ? '男' : '女' }}</span>
                        </div>
                        <div class="card_bd_title"><span>疾病描述</span><span>{{ (inquiryInfo.lastDiagnosis.mainSuit && inquiryInfo.lastDiagnosis.mainSuit.length > 0) ? inquiryInfo.lastDiagnosis.mainSuit : '无' }}</span></div>
                        <div class="card_bd_title"><span>诊断</span><span>{{ inquiryInfo.lastDiagnosis.diagnosis.length > 0 ? inquiryInfo.lastDiagnosis.diagnosis : '无' }}</span></div>
                        <div class="card_bd_title"><span>过敏史</span><span>{{ (inquiryInfo.lastDiagnosis.allergySymptomExplain == '' || inquiryInfo.lastDiagnosis.allergySymptomExplain == null) ? '无' :  inquiryInfo.lastDiagnosis.allergySymptomExplain }}</span></div>
                        <div class="card_bd_title"><span>肝、肾功能异常</span><span>{{getLiverText(inquiryInfo.lastDiagnosis.liverAndRenalFunctionValue)}}</span></div>
                        <div class="card_bd_title" v-if="msg.sex != 1 "><span>妊娠或哺乳期</span><span>{{getPregnancyText(inquiryInfo.lastDiagnosis.pregnancyAndLactationValue)}}</span></div>
                        <div class="card_bd_title"><span>用药类型</span><span>{{ inquiryInfo.lastDiagnosis.medicineType == 1 ? '中药' : '西药' }}</span></div>
                        <div class="card_bd_title" v-if="inquiryInfo.lastDiagnosis.prescriptionDetail == 0"><span>预购药品</span><span>无</span></div>
                        <div class="card_bd_title" v-else><span>预购药品</span><span>{{ inquiryInfo.lastDiagnosis.prescriptionDetail ? inquiryInfo.lastDiagnosis.prescriptionDetail.map(e => e.commonName).join(',') : '' }}<span v-if="inquiryInfo.lastDiagnosis.prescriptionDetail.length > 0">共{{ inquiryInfo.lastDiagnosis.prescriptionDetail.length}}种药品</span></span></div>
                    </div>
                </div>
            </div>
            <div v-if="msg.type == 'ilnessCard'" class="msg_card" style="cursor: pointer;" @click="openCard">
                <div style="width: 300px;">
                    <div class="card_hd">
                        <img src="@/assets/inquiry/usercard.png" alt=""/>
                        <span>病情资料</span>
                        <img src="@/assets/inquiry/right.png"  alt="" style="width: 14px;height: 14px"/>
                    </div>
                    <div class="card_bd" style="line-height: 30px;">
                        <div class="card_bd_info">
                            患者信息：<span>{{ illnessDetail.patientSex == 1 ? '男' : '女' }}</span>
                        </div>
                        <div class="card_bd_info">
                            问诊类型：<span>{{ illnessDetail.inquiryType  }}</span>
                        </div>
                        <div class="card_bd_info " >
                            <div class="text-ellipsis">病情描述：{{ illnessDetail.illnessDetail  }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="msg.type == 'img'" class="msg_image">
                <div v-loading="msg.status == 'loading'" class="msg_loading" style="display: inline-block;"></div>
                <span v-if="msg.status == 'fail'" class="msg_error" @click="resendMsgFn">
                    <img style="width: 100%; height: 100%;" src="@/assets/inquiry/reset_error.png" alt=""/>
                </span>
                <div style="width: 150px; height: 150px; display: inline-block;">
                    <el-image
                        style="width: 100%; height: 100%; border-radius: 10px 0px 10px 10px;"
                        ref="previewImage"
                        :preview-src-list="msgUrlList"
                        :src="msg.text"
                        @click.capture="handlePreviewImage()">
                        <template #error>
                            <div class="image-slot"
                                style="background: #383838; display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;">
                                <img src="@/assets/inquiry/img_error.png" alt=""
                                    style="width: 84px; height: 74px; border-radius: 10px 0px 10px 10px;"/>
                            </div>
                        </template>
                    </el-image>
                </div>
            </div>
        </div>
        <!-- 头像 -->
        <div class="avatar">
            <img v-if="msg.sex == 1" src="@/assets/inquiry/patient_nan.png" alt=""/>
            <img v-else src="@/assets/inquiry/patient_nv.png" alt=""/>
        </div>
    </div>
    <div v-else-if="msg.isDoctor == 2" class="msg">
       <div class="middelle-msg" v-if="msg.type == 'smallInfo'">医生小助已进线，预问诊便于医生更好了解您的病情</div>
       <div class="middelle-msg" v-if="msg.type == 'dockerInfo'">
            <div class="dockerInfo_box">
                <div class="dockerInfo_box_header">
                    <div class="dockerInfo_box_header_left">
                        <span class="dockerInfo_box_header_left_status">已实名</span>
                        <div>{{ doctorInfo.time }}</div>
                    </div>
                    <div class="dockerInfo_box_header_right">
                        <span>职业症编号：</span>
                        <span>{{ doctorInfo.practicingLicenseNumber }}</span>
                    </div>
                </div>
                <div class="dockerInfo_box_info">
                     <img src="@/assets/inquiry/success.png" class="dockerInfo_box_info_left" alt=""/>
                     <div class="dockerInfo_box_info_right">
                        <div class="dockerInfo_box_info_right_top">
                            <span class="dockerInfo_box_info_right_top_name">{{ doctorInfo.doctorName }}</span>
                            <span>{{ doctorInfo.job }}</span>
                        </div>
                        <div class="dockerInfo_box_info_right_bottom">
                            <span style="margin-right: 8px;">{{ doctorInfo.hospital }}</span>
                            <span>{{ doctorInfo.department }}</span>
                        </div>
                     </div>
                </div>
                <div class="dockerInfo_box_describe">
                    {{ doctorInfo.brief }}
                </div>
                <div class="dockerInfo_box_evaluate">
                    <div class="dockerInfo_box_evaluate_left dockerInfo_box_evaluate_box">
                        <div class="user_evaluate">{{ doctorInfo.seniority }}</div>
                        <div class="user_detail">从业时间</div>
                    </div>
                    <div class="dockerInfo_box_evaluate_center dockerInfo_box_evaluate_box">
                        <div class="user_evaluate">{{ doctorInfo.service }}</div>
                        <div class="user_detail">服务次数</div>
                    </div>
                    <div class="dockerInfo_box_evaluate_right dockerInfo_box_evaluate_box">
                        <div class="user_evaluate">{{ doctorInfo.goodComment }}</div>
                        <div class="user_detail">好评率</div>
                    </div>
                </div>
            </div>
       </div>
    </div>
     <Dialog
      title="病情资料"
      v-model="cardVisible"
      width="800px"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="cardVisibleFn"
    >
    <div style="padding: 10px;border: 1px solid #ccc;">
        <el-form
            label-position="right"
            label-width="auto"
            style="max-width: 600px"
        >
            <el-form-item label="问诊患者" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="问诊药店" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="过敏史" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="肝、肾功能异常" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="是否妊娠期、哺乳期" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="是否复诊" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="慢病病情需要" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="病情描述" label-position="right">
                李文文&nbsp;女&nbsp;29岁&nbsp;
            </el-form-item>
            <el-form-item label="线下就诊处方病历" label-position="right">
                <el-image style="width: 100px; height: 100px" :src="url" :preview-src-list="srcList" fit="cover" />
            </el-form-item>
        </el-form>
    </div>
      <template #footer>
        <el-button  type="primary" @click="cardVisibleFn">关 闭</el-button>
      </template>
    </Dialog>
    <Dialog
      title="用药申请单"
      v-model="openUseDrugDialog"
      width="800px"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="closeUseDrugDialogFn"
    >
    <div style="padding: 10px;border: 1px solid #ccc;">
        <el-form
            label-position="right"
            label-width="auto"
        >
            <div style="padding-bottom: 10px; border-bottom: 1px solid #ddd; width: 100%;">
                <el-form-item label="患者信息" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
                <el-form-item label="身份证号" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
                <el-form-item label="申请时间" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
            </div>
            <div style="padding-bottom: 10px; border-bottom: 1px solid #ddd; width: 100%;">
                <el-form-item label="过敏史" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
                <el-form-item label="肝、肾功能异常" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
                <el-form-item label="是否妊娠期" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
                <el-form-item label="是否复诊" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
                <el-form-item label="病情描述" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
                <el-form-item label="临床诊断" label-position="right">
                    李文文&nbsp;女&nbsp;29岁&nbsp;
                </el-form-item>
            </div>
            <div class="drug_all">
               <div style="font-weight: 500;font-size: 20px;color: #222222;">RP</div>
                <div class="durg_box" v-for="item in 6" :key="item">
                    <div class="drug_box_header">
                        <span class="drug_title">1、阿莫西林胶囊</span>
                        <span class="drug_gg">(0.5g*10粒/盒)</span>
                    </div>
                    <div class="drug_box_body">
                        <div >数量：1</div>
                        <div >用法：1</div>
                    </div>
                </div>
            </div>
        </el-form>
    </div>
      <template #footer>
        <el-button  type="primary" @click="closeUseDrugDialogFn">关 闭</el-button>
      </template>
    </Dialog>
    </div>
</template>

<script setup>
import { ref } from 'vue'
const props = defineProps({
  msg: {
    type: Object,
    default: () => ({})
  },
  doctorInfo: {
    type: Object,
    default: () => ({})
  },
  inquiryInfo: {
    type: Object,
    default: () => ({})
  },
  illnessDetail: {
    type: Object,
    default: () => ({})
  },
  msgImages: {
    type: Object,
    default: () => ({})
  },
  doctorInfo:{
     type: Object,
    default: () => ({})
  },
  westPrescription:{
     type: Object,
    default: () => ({})
  }
})


const url = 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'
const srcList = [
  'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
  'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg',
  'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
  'https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg',
  'https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
  'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg',
  'https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg',
]

const emit = defineEmits([
  'checkPrescription',
  'checkPrescriptionErr',
  'resendMs',
  'commandStack-changed',
  'input',
  'change',
  'canvas-viewbox-changed',
  // eventName.name
  'element-click'
])


const openUseDrugDialog = ref(false) ;

const previewImage = ref();
const audioPlayer = ref();
const cardVisible = ref(false)
const isPlaying = ref(false)
const msgUrlList = computed(() => {
    return msgImages.value.map(e => e)
})
const cardVisibleFn = ()=>{
  cardVisible.value = false ;
}
const openCard = ()=>{
  cardVisible.value = true ;
}
const closeUseDrugDialogFn  = ()=>{
    openUseDrugDialog.value = false;
}
const handlePreviewImage = ()=>{
    const index = msgUrlList.value.indexOf(msg.value.text);
    const imageViewerChild = previewImage.value.$children[0]
    imageViewerChild && imageViewerChild.reset()
    if (index != -1) {
        imageViewerChild && (imageViewerChild.index = index)
    }
}
const getPregnancyText =(val)=> {
    let text = '无'
    if (val == 1) text = '妊娠期'
    if (val == 2) text = '哺乳期'
    return text;
}

const getLiverText =(val)=> {
    let text = '无'
    if (val == 1) text = '肝功能异常'
    if (val == 2) text = '肾功能异常'
    if (val == 3) text = '肝功能异常 肾功能异常'
    return text;
}
const checkPrescription = async() =>{
     emit('checkPrescription', msg.value);
}
const checkPrescriptionErr = async() =>{
     emit('checkPrescriptionErr', msg.value);
}

const onAudioEnded =() =>{
     isPlaying.value = false;
}
const resendMsgFn =() =>{
     emit('resendMs', msg.value);
}

const playAudio =() =>{
    const audio = audioPlayer.value;
    if (isPlaying.value) {
        audio.pause();
    } else {
        audio.play();
    }
    isPlaying.value = !isPlaying.value;
}
</script>
<style scoped lang="scss">
.drug_all {
    max-height: 300px;
    overflow-y: scroll;
}
.durg_box {
    margin: 10px;
    .drug_box_header {
        font-family: PingFangSC-Medium;
        font-weight: 700;
        font-size: 16px;
        color: #222222;
        align-items: center;
    }
    .drug_gg {
        margin-left: 6px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
    }
    .drug_box_body {
        margin-top: 6px;
        background: #F5F5F5;
        border-radius: 2px;
        padding: 10px;
        line-height: 20px;
    }
}
.el-popover-self {
    width: 300px!important;
}
::v-deep  {
    .inquiry_list_btn {
        cursor: pointer;
    }
}
.dockerInfo_box {
    width: 80%;
    padding: 10px 16px;
    background-color: white;
    border-radius: 8px;
    .dockerInfo_box_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        .dockerInfo_box_header_left {
            display: flex;
            .dockerInfo_box_header_left_status {
                background-color: #F4F4F4;
                font-size: 12px;
                border: 2px;
                margin-right: 6px;
            }
        }
        .dockerInfo_box_header_right {
            font-weight: 400;
            font-size: 12px;
            color: #222222;
            letter-spacing: 0;
            text-align: right;
            line-height: 14px;
        }
    }
    .dockerInfo_box_info {
        display: flex;
        align-items: center;
        .dockerInfo_box_info_left {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .dockerInfo_box_info_right {
            span {
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: 14px;
                color: #222222;
                line-height: 16px;
            }
            .dockerInfo_box_info_right_top {
                margin-bottom: 8px;
            }
            .dockerInfo_box_info_right_top_name {
                font-family: PingFangSC-Medium;
                font-weight: 500;
                font-size: 20px;
                color: #222222;
                line-height: 22px;
                margin-right: 6px;
            }
        }
    }
    .dockerInfo_box_describe {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #7C7C7C;
        letter-spacing: 0;
        margin: 10px 0;
    }

}
.dockerInfo_box_evaluate {
    padding: 10px;
    display: flex;
    justify-content: space-around;
    background: #F4F4F4;
    .dockerInfo_box_evaluate_center {
        border: 1px solid #E8E8E8;
        border-top: none;
        border-bottom: none;
        padding: 0px 20px;
        width: 30%;
    }
    .user_evaluate {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        letter-spacing: 0;
        text-align: center;
        line-height: 20px;
    }
    .user_detail {
        margin-top: 8px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        letter-spacing: 0;
        text-align: center;
        line-height: 16px;
    }
}
.middelle-msg {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 6px 0;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
}
.msg {
    font-family: PingFangSC-Regular;
    display: flex;
    margin: 20px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 25px;
    img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }
}

.content_left {
    flex: 1;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 2px;
}

.msg_name {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 14px;
    margin-bottom: 8px;
}

.content_left .msg_text {
    display: inline-block;
    padding: 12px 16px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #111734;
    background: #FFFFFF;
    border-radius: 0 10px 10px 10px;
    word-break: break-all;
}

.content_right {
    flex: 1;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 2px;
    text-align: end;
    margin-left: 20px;
}

.content_right .msg_text {
    display: inline-block;
    padding: 12px 16px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #fff;
    /* background: #00B955; */
      background-color: var(--el-color-primary);
    border-radius: 10px 0px 10px 10px;
    text-align: start;
    word-break: break-all;
    position: relative;

    .msg_text_loading {
        position: absolute;
        left: -26px;
        bottom: 0;

        ::v-deep .el-loading-mask {
            background-color: rgba(255, 255, 255, 0);
        }

        ::v-deep .circular {
            width: 20px !important;
            height: 20px !important;
        }
    }

    .msg_error {
        width: 20px;
        height: 20px;
        position: absolute;
        left: -26px;
        bottom: 0;
    }
    .msg_error:hover {
        cursor: pointer;
    }
}

.content_right .msg_card {
    display: inline-block;
}

.card_hd {
    display: flex;
    align-items: center;
    padding-left: 15px;
    width: 300px;
    height: 48px;
    background-image: linear-gradient(180deg, rgba(0, 185, 85, 0.3) 0%, rgba(255, 255, 255, 0.5) 78%);
    border-radius: 10px 0 0 0;
    border-bottom: .5px solid #E7E7E7;

    img {
        width: 18px;
        height: 18px;
        margin-right: 10px;
    }
}

.card_bd {
    text-align: start;
    background-color: #fff;
    padding: 12px;
    box-sizing: border-box;
    border-radius: 0 0 10px 10px;
}

.card_bd_info {
    font-weight: 500;
    font-size: 16px;
    color: #111734;
    display: flex;
}

.card_bd_title {
    display: flex;
    margin-top: 14px;
}

.card_bd_title span:nth-child(1) {
    width: 60px;
    font-weight: 400;
    font-size: 14px;
    color: #9AA0B5;
}

.card_bd_title span:nth-child(2) {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #111734;
    word-break: break-all;
}

.msg_image {
    position: relative;
    text-align: end;

    .msg_loading {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 170px;
        bottom: 0;
    }

    .msg_error {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 170px;
        bottom: 0;
    }
    .msg_error:hover {
        cursor: pointer;
    }
}

.border_line {
    display: inline-block;
    height: 14px;
    width: 1px;
    background-color: #E7E7E7;
    margin-top: 1px;
    margin-left: 10px;
    margin-right: 10px;
}

.left_msg_card {
    width: 243px;
    height: 72px;
    padding: 0 14px;
    background: #00B955;
    border-radius: 0 10px 10px 10px;
    background-image: url('../../../../../../assets/inquiry/prescription_bgc.png');
    background-position: center bottom;
    background-size: 86px 61px;
    background-repeat: no-repeat;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.left_msg_card span:nth-child(1) {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 18px;
    color: #FFFFFF;
    letter-spacing: 0.58px;
}

.left_msg_card span:nth-child(2) {
    display: inline-block;
    width: 63.5px;
    height: 22px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12.5px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 10px;
    color: #FFFFFF;
    text-align: center;
    line-height: 22px
}

.left_msg_card span:nth-child(2):hover {
    cursor: pointer;
}

.audio_content {
    padding: 4px 25px !important;
    position: relative;
}

.audio_size {
    position: absolute;
    right: -18px;
    bottom: 7px;
    color: #a6a4a4;
}

.wifi_symbol {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
        height: 28px;
    }
}

.msg_loading {
    ::v-deep .el-loading-mask {
        background-color: rgba(255, 255, 255, 0);
    }

    ::v-deep .circular {
        width: 30px !important;
        height: 30px !important;
    }
}

.avatar_content {
    width: 400px;
    padding: 0px 10px 5px 10px;
    height: 320px;
    overflow-y: auto;
    .avatar_title {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
        letter-spacing: 0;
        line-height: 16px;
        margin-bottom: 6px;
    }
    .avatar_header {
        display: flex;
        justify-content: start;
        align-items: center;
        margin-bottom: 6px;
        div:nth-child(2) {
            margin-left: 10px;
            p:nth-child(1) {
                font-family: PingFangSC-Medium;
                font-weight: 500;
                font-size: 18px;
                color: #101434;
                span {
                    font-family: PingFangSC-Regular;
                    font-weight: 400;
                    font-size: 14px;
                    color: #101434;
                    margin-left: 8px;
                }
            }
            p:nth-child(2) {
                margin-top: 2px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: 14px;
                color: #101434;
            }
        }
    }
    .desc {
        margin-top: 10px;
        min-height: 80px;
        div:nth-child(2) {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #666883;
        }
    }
    .title_green {
    display: flex;
    align-items: center;
        p:nth-child(1) {
            width: 3px;
            height: 14px;
            background: #00B955;
            border-radius: 2px;
        }
        p:nth-child(2){
        margin-left: 5px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
        }
    }
}
.text-ellipsis{
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
}
// 美化滚动条
::-webkit-scrollbar {
  width: 4px;
  height: 10px;
}

::-webkit-scrollbar-track {
  width: 6px;
  background: #d6d8dc;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144,147,153,.5);
  background-clip: padding-box;
  min-height: 28px;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
  transition: background-color .3s;
  cursor: pointer;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144,147,153,.3);
}
</style>

<style lang="scss">
.el-popover-self {
    width: auto!important;
}
</style>
