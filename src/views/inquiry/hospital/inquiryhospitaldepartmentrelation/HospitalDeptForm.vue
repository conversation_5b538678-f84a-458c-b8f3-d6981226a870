<template>
  <Dialog v-model="dialogVisible" title="医院科室维护">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-width="80px">
      <el-form-item label="医院" prop="hospitalPref">
        <HospitalSelect  :pref="formData.hospitalPref" @success="(args) => formData.hospitalPref = args"/>
      </el-form-item>
      <el-form-item label="科室信息">
        <el-card class="cardHeight">
          <template #header>
            全选/全不选:
            <el-switch
              v-model="treeNodeAll"
              active-text="是"
              inactive-text="否"
              inline-prompt
              @change="handleCheckedTreeNodeAll"
            />
            全部展开/折叠:
            <el-switch
              v-model="menuExpand"
              active-text="展开"
              inactive-text="折叠"
              inline-prompt
              @change="handleCheckedTreeExpand"
            />
          </template>
          <el-tree
            ref="treeRef"
            :data="menuOptions"
            :props="hospDeptProps"
            empty-text="加载中，请稍候"
            node-key="id"
            show-checkbox
            @check="handleCheckChange"
          />
        </el-card>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { handleTree } from "@/utils/tree";
import {
  InquiryHospitalDepartmentRelationApi
} from "@/api/inquiry/hospital/inquiryhospitaldepartmentrelation";
import {InquiryHospitalDepartmentApi} from "@/api/inquiry/hospital/inquiryhospitaldepartment";
import HospitalSelect from "@/views/inquiry/hospital/HospitalSelect.vue";
import { ref, reactive, nextTick, watch } from 'vue';

defineOptions({ name: 'HospitalDeptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = reactive({
  id: 0,
  name: undefined,
  hospitalPref: undefined,
  code: undefined,
  menuIds: [],
  checked: []
})
const formRef = ref() // 表单 Ref
const menuOptions = ref<any[]>([]) // 菜单树形结构
const menuExpand = ref(false) // 展开/折叠

const checkList = ref([]) // 使用ref包装数组，使其成为响应式
const treeRef = ref() // 菜单树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选
const hospDeptProps = {
  children: 'children',
  label: 'deptName',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: false // 用于 cascader 组件：在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
}

/** 打开弹窗 */
const open = async (hospital : string) => {
  try {
    dialogVisible.value = true
    resetForm()
    console.log("hospital", hospital)
    formData.hospitalPref = hospital
    
    // 加载 Menu 列表。注意，必须放在前面，不然下面 setChecked 没数据节点
    formLoading.value = true
    const deptList = await InquiryHospitalDepartmentApi.getInquiryHospitalDepartmentAllList()
    menuOptions.value = handleTree(deptList, "id", "deptParentId")
    
    // 等待树组件渲染完成
    await nextTick()
    
    if (formData.hospitalPref) {
      await queryHospitalDept()
    }
  } catch (error) {
    console.error("打开弹窗失败:", error)
    message.error("加载科室列表失败")
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 递归获取所有子节点ID
const getAllChildrenIds = (node) => {
  if (!node) return []
  
  let ids = []
  if (node.id) {
    ids.push(node.id)
  }
  if (node.children && node.children.length) {
    node.children.forEach(child => {
      ids = ids.concat(getAllChildrenIds(child))
    })
  }
  return ids
}

// 处理节点选中状态变化
const handleCheckChange = (data, checked) => {
  try {
    // 如果是已选科室且尝试取消选中，则阻止操作
    if (!checked.checked && checkList.value.includes(data.id)) {
      nextTick(() => {
        treeRef.value.setChecked(data.id, true, true) // 使用true启用级联选择
      })
      message.warning('已选择科室无法取消')
      return
    }
  } catch (error) {
    console.error("处理节点选中状态变化失败:", error)
  }
}

const queryHospitalDept = async () => {
  if (!formData.hospitalPref) {
    return
  }
  
  try {
    const data = await InquiryHospitalDepartmentRelationApi.getInquiryHospitalDepartmentListByhospitalPref(formData.hospitalPref)
    
    // 清空之前的选择
    formData.menuIds = []
    checkList.value = []
    
    // 获取已选科室ID
    data.forEach(hosp => {
      const deptId = hosp.deptId
      formData.menuIds.push(deptId)
      checkList.value.push(deptId)
    })
    
    // 等待DOM更新后设置选中状态
    await nextTick()
    
    if (treeRef.value) {
      // 先清空所有选中状态
      treeRef.value.setCheckedKeys([])
      
      // 为已选择的科室设置选中状态
      formData.menuIds.forEach((menuId) => {
        try {
          // 使用true作为第三个参数，启用级联选择
          treeRef.value.setChecked(menuId, true, true)
        } catch (error) {
          console.error(`设置节点 ${menuId} 选中状态失败:`, error)
        }
      })
    }
  } catch (error) {
    console.error("加载医院科室关系失败:", error)
    message.error("加载医院科室关系失败")
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  if (!treeRef.value) {
    message.error("科室树未加载完成，请稍后再试")
    return
  }
  
  // 获取所有选中节点
  let subIds = treeRef.value.getCheckedKeys(false)
  
  // 确保所有已选科室都包含在提交的数据中
  let canSub = true
  checkList.value.forEach(id => {
    if (!subIds.includes(id)) {
      canSub = false
    }
  })
  
  if (!canSub) {
    message.warning("已选择科室无法取消")
    return
  }
  
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      hospitalPref: formData.hospitalPref,
      deptIds: subIds
    }
    await InquiryHospitalDepartmentRelationApi.editHospitalDeptInfo(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error("保存医院科室关系失败:", error)
    message.error("保存失败")
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  menuExpand.value = false
  // 重置表单
  formData.id = 0
  formData.hospitalId = ""
  formData.name = ""
  formData.code = ""
  formData.menuIds = []
  checkList.value = []
  
  // 清空树选择
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([])
  }
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

/** 全选/全不选 */
const handleCheckedTreeNodeAll = () => {
  if (!treeRef.value) return
  
  if (treeNodeAll.value) {
    // 全选
    const allIds = []
    const collectIds = (nodes) => {
      if (!nodes) return
      nodes.forEach(node => {
        allIds.push(node.id)
        if (node.children && node.children.length) {
          collectIds(node.children)
        }
      })
    }
    collectIds(menuOptions.value)
    treeRef.value.setCheckedKeys(allIds)
  } else {
    // 全不选，但保留已选科室
    treeRef.value.setCheckedKeys(checkList.value)
  }
}

/** 展开/折叠全部 */
const handleCheckedTreeExpand = () => {
  if (!treeRef.value?.store?.nodesMap) return
  
  const nodes = treeRef.value.store.nodesMap
  for (let nodeId in nodes) {
    if (nodes[nodeId].expanded === menuExpand.value) {
      continue
    }
    nodes[nodeId].expanded = menuExpand.value
  }
}
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}

/* 为已选科室添加特殊样式 */
:deep(.el-tree-node) {
  &.is-checked {
    .el-tree-node__label {
      color: #409EFF;
    }
  }
}
</style>
