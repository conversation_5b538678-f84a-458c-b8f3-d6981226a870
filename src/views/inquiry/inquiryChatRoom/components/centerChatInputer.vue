<template>
  <div class="chat-input-context">
    <div class="toolbar">
      <el-upload
        ref="uploadRef"
        accept="image/*"
        :http-request="httpRequest"
        :limit="1"
        auto-upload
        :show-file-list="false"
        :on-exceed="handleExceed"
      >
        <template #trigger>
          <img class="tool" src="@/assets/inquiry/doctor/icon-pic.png" />
        </template>
      </el-upload>

      <!-- <img class="tool" src="@/assets/inquiry/doctor/icon-quick-phrases.png" /> -->
    </div>
    <div class="inputer">
      <el-input
        ref="refInput"
        v-model="state.inputModel"
        type="textarea"
        resize="none"
        maxlength="500"
        placeholder="请在此处输入文字内容,限500字以内"
        @keyup.enter="onSendTextMessage"
      />
    </div>
    <div class="handler">
      <span class="input-tip">按Enter发送消息，按Shift+Enter换行</span>
      <el-button type="primary" size="large" @click="onSendTextMessage">发送</el-button>
    </div>
  </div>
</template>

<script setup>
import { genFileId } from 'element-plus'
import { useTimStore } from '@/store/modules/useTim'
import * as FileApi from '@/api/infra/file'

const message = useMessage() // 消息弹窗

const state = reactive({
  curInquiry: null, // 当前问诊
  curChatInfo: null, // 当前会话
  inputModel: '' /// 输入框内容
})

const reset = () => {
  state.curInquiry = null
  state.curChatInfo = null
  state.inputModel = ''
}

const init = ({ inquiry, chat }) => {
  reset()
  state.curInquiry = inquiry
  state.curChatInfo = chat
}

// 发送文本消息
const onSendTextMessage = async () => {
  if (!state.inputModel.length) {
    message.warning('请输入回复内容~')
    return
  }
  await useTimStore().tim.onSendTextMessage({
    content: state.inputModel,
    toUserId: state.curChatInfo.friendAccount_platform
  })
  nextTick(() => {
    state.inputModel = ''
  })
}

// 发送图片消息
const onSendImageMessage = async ({ url }) => {
  await useTimStore().tim.onSendImgMessage({
    url: url,
    toUserId: state.curChatInfo.friendAccount_platform
  })
}

// 上传图片
const uploadRef = ref()
// 当文件超出限制时，执行的钩子函数
const handleExceed = (files) => {
  uploadRef.value?.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value?.handleStart(file)
  uploadRef.value.submit()
}
// 覆盖默认的 Xhr 行为，允许自行实现上传文件的请求
const httpRequest = async (options) => {
  try {
    const res = await FileApi.updateFile({ file: options.file })
    if (res.code === 0) {
      // options.onSuccess(res) // 调用 Element Plus 回调
      onSendImageMessage({
        url: res.data
      })
    } else {
      throw new Error(res.msg || '上传失败')
    }
  } catch (err) {
    // options.onError(err)
    message.error('图片发送失败，请稍后重试')
  }
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.chat-input-context {
  width: 100%;
  height: 200px;
  border-top: 1px solid #d9dee3;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;

  .toolbar {
    flex-shrink: 0;
    width: 100%;
    padding: 10px 16px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tool {
      width: 20px;
      height: 20px;
      margin-right: 16px;
      cursor: pointer;
    }
  }

  .inputer {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;

    :deep(.el-textarea) {
      flex-grow: 1;
      width: 100%;

      .el-textarea__inner {
        width: 100%;
        height: 100%;
        padding: 8px 16px;
        border: none;
        box-shadow: none;
        background-color: #f0f2f5;
        color: #222222;
        font-size: 15px;

        &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          background: #f0f2f5;
        }
      }
    }
  }

  .handler {
    flex-shrink: 0;
    width: 100%;
    padding: 10px 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .input-tip {
      margin-right: 16px;
      color: #999999;
      font-size: 13px;
    }
  }
}
</style>
