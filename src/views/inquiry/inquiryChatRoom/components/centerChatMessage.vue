<template>
  <div :class="['msg', computedMsgDirection]">
    <template v-if="props.msg.type == 'doctorInfo'">
      <MsgDoctorInfoCard :cloudCustomData="props.msg.cloudCustomData" />
    </template>
    <template v-else-if="props.msg.type == 'evaluate'">
      <MsgEstimationCard
        :cloudCustomData="props.msg.cloudCustomData"
        :curChatInfo="props.curChatInfo"
      />
    </template>
    <template v-else-if="props.msg.type == 'bubbleNotify'">
      <span class="tip">{{ msg.text }}</span>
    </template>
    <template v-else>
      <div class="msg-context">
        <!-- 头像 -->
        <template v-if="computedMsgDirection == 'left' && props.friendInfo.pref">
          <el-popover placement="right-start" :width="360">
            <template #reference>
              <img class="cxt-avatar" :src="computedMsgAvatarName.avatar" />
            </template>
            <template #default>
              <!-- 患者 -->
              <!-- <PopoverPatientCard /> -->
              <!-- 医生 -->
              <PopoverDoctorCard :doctor-info="props.friendInfo" />
              <!-- 药师 -->
              <!-- <PopoverPharmacistCard /> -->
            </template>
          </el-popover>
        </template>
        <template v-else>
          <img class="cxt-avatar" :src="computedMsgAvatarName.avatar" />
        </template>
        <!-- 消息体 -->
        <div class="cxt-msg">
          <div class="cxt-msg-origin">
            <span class="origin-from">{{ computedMsgAvatarName.name }}</span>
            <span class="origin-time">{{
              dayjs(props.msg.time).format('YYYY-MM-DD HH:mm:ss')
            }}</span>
          </div>
          <div class="cxt-msg-content" @click="onClickMessage($event, props.msg.type, props.msg)">
            <!-- 用药申请 -->
            <template v-if="props.msg.type == 'drugInfo'">
              <MsgMedicationRequestCard :cloudCustomData="props.msg.cloudCustomData" />
            </template>
            <!-- 门诊病历 -->
            <template v-if="props.msg.type == 'clinicalCase'">
              <MsgClinicalCaseCard :cloudCustomData="props.msg.cloudCustomData" />
            </template>
            <!-- 处方消息 -->
            <template v-else-if="props.msg.type == 'prescriptionInfo'">
              <MsgPrescriptionInfoCard :cloudCustomData="props.msg.cloudCustomData" />
            </template>
            <!-- 文本消息 -->
            <template v-else-if="props.msg.type == 'text'">
              <div class="general-msg">{{ msg.text }}</div>
            </template>
            <!-- 图片消息 -->
            <template v-else-if="props.msg.type == 'img'">
              <div class="img-msg">
                <el-image
                  :src="props.msg.cloudCustomData.img"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[props.msg.cloudCustomData.img]"
                  show-progress
                  :initial-index="0"
                  fit="cover"
                />
              </div>
            </template>
            <!-- 语音消息 -->
            <template v-else-if="props.msg.type == 'audio'">
              <div class="audio-msg"
                ><img class="icon-audio" :src="LeftAudio" />{{
                  formatSecondsTime(msg.text || props.msg.cloudCustomData.duration)
                }}</div
              >
            </template>
            <!-- 视频消息 -->
            <template v-else-if="props.msg.type == 'video'">
              <div class="video-msg"
                >通话时长 {{ formatSecondsTime(msg.text || props.msg.cloudCustomData.duration)
                }}<img class="icon-video" :src="VideoStatus"
              /></div>
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, inject } from 'vue'
import dayjs from 'dayjs'
import { InquiryWorkstationApi } from '@/api/inquiry/inquiryWorkstation/index'

import MsgDoctorInfoCard from './messageCard/msgDoctorInfoCard.vue' // 医生信息卡片
import MsgPrescriptionInfoCard from './messageCard/msgPrescriptionInfoCard.vue' // 处方信息卡片
import MsgMedicationRequestCard from './messageCard/msgMedicationRequestCard.vue' // 用药申请卡片
import MsgEstimationCard from './messageCard/msgEstimationCard.vue' // 会话评价
import MsgClinicalCaseCard from './messageCard/msgClinicalCaseCard.vue' // 门诊病历

import PopoverPatientCard from './popoverCard/popoverPatientCard.vue' // 患者信息悬浮卡片
import PopoverDoctorCard from './popoverCard/popoverDoctorCard.vue' // 医生信息悬浮卡片
import PopoverPharmacistCard from './popoverCard/popoverPharmacistCard.vue' // 药师信息悬浮卡片

import LeftAudio from '@/assets/inquiry/voice_icon4.png'
import LeftAudioActive from '@/assets/inquiry/voice_icon4_active.gif'
import RightAudio from '@/assets/inquiry/voice_icon3.png'
import RightAudioActive from '@/assets/inquiry/voice_icon3_active.gif'
import VideoStatus from '@/assets/inquiry/status_video.png'

const message = useMessage() // 消息弹窗

const currentChatRole = inject('currentChatRole')

const emit = defineEmits(['click-message', 'update-friend-info'])

const props = defineProps({
  msg: {
    type: Object,
    default: () => {}
  },
  curChatInfo: {
    type: [Object],
    default: () => {}
  },
  friendInfo: {
    type: [Object],
    default: () => {}
  }
})

// 消息方向:left,center,right
const computedMsgDirection = computed(() => {
  // doctorInfo:医生卡片
  // evaluate:评价卡片
  // bubbleNotify:系统消息
  if (['doctorInfo', 'evaluate', 'bubbleNotify'].includes(props.msg.type)) {
    return 'center'
  }

  if (props.msg.userType == 'friend') {
    return 'left'
  } else if (props.msg.userType == 'self') {
    return 'right'
  } else {
    return ''
  }
})

// 消息头像和名称
const computedMsgAvatarName = computed(() => {
  let avatar = ''
  let name = ''

  if (currentChatRole == 'patient') {
    // 患者
    switch (computedMsgDirection.value) {
      case 'left': // 医生或药师，todo：药师
        avatar = props.msg.friendAvatar
        name = props.msg.fromUserName
        break
      case 'right': // 患者
        avatar = props.msg.selfAvatar
        name = props.msg.fromUserName
        break
    }
  } else if (currentChatRole == 'doctor') {
    // 医生
    switch (computedMsgDirection.value) {
      case 'left': // 患者
        break
      case 'right': // 医生
        break
    }
  } else if (currentChatRole == 'pharmacist') {
    // 药师
    switch (computedMsgDirection.value) {
      case 'left': // 患者
        break
      case 'right': // 药师
        break
    }
  }

  return { avatar, name }
})

// 序列化秒数
const formatSecondsTime = (seconds) => {
  // 确保输入是数字
  if (isNaN(seconds) || seconds < 0) {
    return '00:00'
  }

  // 计算小时、分钟和秒
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  // 格式化时间为两位数
  const formatTimeCb = (time) => String(time).padStart(2, '0')

  // 判断是否需要显示小时
  if (hours > 0) {
    return `${formatTimeCb(hours)}:${formatTimeCb(minutes)}:${formatTimeCb(secs)}`
  } else {
    return `${formatTimeCb(minutes)}:${formatTimeCb(secs)}`
  }
}

// 获取事件冒泡路径上的DOM
const getEventPath = (e) => {
  if (e.composedPath) {
    return e.composedPath() || []
  } else {
    function getEventPathElement(target) {
      const path = []
      while (target) {
        path.push(target)
        target = target.parentElement
      }
      return path
    }
    return e.path || (e.target && getEventPathElement(e.target)) || []
  }
}

// 消息点击
const onClickMessage = (e, msgType, msg) => {
  const elEventPath = getEventPath(e) // 获取冒泡路径上的DOM
  const isPHeadClicked = elEventPath.some((el) => el.classList && el.classList.contains('p-head')) // 检查路径中是否包含 p-head 元素，即点击头部

  switch (msgType) {
    case 'drugInfo': // 用药申请卡片
    case 'prescriptionInfo': // 处方卡片
    case 'clinicalCase': // 门诊病历
      emit('click-message', { e, msgType, msg }) // 在父组件中响应子组件事件，避免子组件重复创建
      break
    default:
      break
  }
}
</script>

<style lang="scss" scoped>
.msg {
  width: 100%;
  padding: 16px 16px;
  overflow: hidden;
  display: flex;

  &.left {
    justify-content: flex-start;
  }

  &.center {
    justify-content: center;
  }

  &.right {
    justify-content: flex-end;
  }

  // 系统提示消息
  .tip {
    line-height: 1;
    border-radius: 20px;
    background-color: #fff;
    padding: 7px 20px;
    color: #666666;
    font-size: 13px;
  }

  // 消息体
  .msg-context {
    width: 60%;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;

    .cxt-avatar {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }

    .cxt-msg {
      flex-grow: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      .cxt-msg-origin {
        line-height: 1;
        width: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;

        .origin-from {
          color: #666666;
          font-size: 13px;
        }

        .origin-time {
          color: #666666;
          font-size: 13px;
        }
      }

      .cxt-msg-content {
        width: 100%;
        overflow: hidden;
        margin: 8px 0 0;
        display: flex;

        // 普通消息
        .general-msg {
          line-height: 1.1;
          padding: 14px;
          border-radius: 8px;
          word-break: break-all;
          color: #222222;
          font-size: 16px;
        }

        // 图片消息
        .img-msg {
          line-height: 1.1;
          padding: 14px;
          border-radius: 8px;
          word-break: break-all;
          color: #222222;
          font-size: 16px;

          .el-image {
            width: 160px;
            height: 120px;
          }
        }

        // 语音消息
        .audio-msg {
          line-height: 1.1;
          padding: 14px;
          border-radius: 8px;
          word-break: break-all;
          color: #222222;
          font-size: 16px;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .icon-audio {
            margin-right: 8px;
            width: 20px;
            height: 20px;
          }
        }

        // 视屏消息
        .video-msg {
          line-height: 1.1;
          padding: 14px;
          border-radius: 8px;
          word-break: break-all;
          color: #222222;
          font-size: 16px;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .icon-video {
            margin-left: 8px;
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  &.left {
    .msg-context {
      .cxt-avatar {
        order: 1;
        margin-right: 8px;
        cursor: pointer;
      }

      .cxt-msg {
        order: 2;
        align-items: flex-start;

        .cxt-msg-origin {
          justify-content: flex-start;

          .origin-from {
            order: 1;
            margin-right: 8px;
          }

          .origin-time {
            order: 2;
          }
        }

        .cxt-msg-content {
          justify-content: flex-start;

          .general-msg {
            background-color: #fff;
          }

          .img-msg {
            background-color: #fff;
          }

          .audio-msg {
            background-color: #fff;
          }

          .video-msg {
            background-color: #fff;
          }
        }
      }
    }
  }

  &.right {
    .msg-context {
      .cxt-avatar {
        order: 2;
        margin-left: 8px;
      }

      .cxt-msg {
        order: 1;
        align-items: flex-end;

        .cxt-msg-origin {
          justify-content: flex-end;

          .origin-from {
            order: 2;
            margin-left: 8px;
          }

          .origin-time {
            order: 1;
          }
        }

        .cxt-msg-content {
          justify-content: flex-end;

          .general-msg {
            background-color: #95ec69;
          }

          .img-msg {
            background-color: #95ec69;
          }

          .audio-msg {
            background-color: #95ec69;
          }

          .video-msg {
            background-color: #95ec69;
          }
        }
      }
    }
  }
}
</style>
