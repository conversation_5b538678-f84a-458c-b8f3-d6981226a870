<template>
  <el-dialog
    class="dialog-prescription-info"
    width="800px"
    append-to-body
    destroy-on-close
    :show-close="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    v-model="state.dialogVisible"
    v-if="state.dialogVisible"
  >
    <div class="dialog-body">
      <div class="dialog-title">
        <span class="title-txt">处方详情</span>
        <el-icon class="icon-close" :size="18" @click="close">
          <Close />
        </el-icon>
      </div>
      <div class="dialog-content">
        <div class="content-box"> </div>
      </div>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary">打印</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Close } from '@element-plus/icons-vue'

const state = reactive({
  dialogVisible: false
})

const init = () => {
  state.dialogVisible = true
}

const close = () => {
  state.dialogVisible = false
}

defineExpose({
  init
})
</script>

<style lang="scss">
.dialog-prescription-info {
  padding: 0;
  border-radius: 8px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    width: 100%;
    padding: 20px;

    .dialog-body {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      .dialog-title {
        width: 100%;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-txt {
          flex-grow: 1;
          color: #222222;
          font-size: 18px;
          font-weight: 600;
        }

        .icon-close {
          flex-shrink: 0;
          cursor: pointer;
          font-weight: bold;

          &:hover {
            opacity: 0.6;
          }
        }
      }

      .dialog-content {
        width: 100%;
        margin-bottom: 20px;

        .content-box {
          width: 100%;
          height: 620px;
          overflow-x: hidden;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: stretch;
          border: 1px solid #dcdfe6;
          padding: 16px;
          box-sizing: border-box;
        }
      }

      .dialog-footer {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .el-button {
          padding: 9px 18px;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 500;

          &:hover {
            opacity: 0.6;
          }

          &:active {
            opacity: 1;
          }

          + .el-button {
            margin-left: 16px;
          }
        }
      }
    }
  }
}
</style>
