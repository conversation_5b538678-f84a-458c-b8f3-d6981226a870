<template>
  <div class="doctor-info-review">
    <div class="head">
      <span class="head-txt"
        ><span class="tag">已实名</span
        >{{ dayjs(props.cloudCustomData.doctorInfo.startPracticeTime).format('YYYY-MM-DD') }}</span
      >
      <span class="head-txt"
        >执业证编号：{{
          professionalNoBuilder(props.cloudCustomData.doctorInfo.professionalNo, 4, 3)
        }}</span
      >
    </div>
    <div class="title">
      <img class="avatar" :src="props.cloudCustomData.doctorInfo.photo" />
      <div class="ttl-local">
        <span class="ttl"
          >{{ props.cloudCustomData.doctorInfo.name }}&nbsp;&nbsp;<span class="ttl-txt">{{
            props.cloudCustomData.doctorInfo.titleName
          }}</span></span
        >
        <!-- <span class="local">荷叶健康互联网医院&nbsp;&nbsp;消化内科</span> -->
      </div>
    </div>
    <!-- 
    <div class="profile">
      简介：心内科学部主任、主任医师。教授、硕士研究生导师。发表国家级论文20余篇，发表SCI论文30篇，从事心血管内科临床工作17年，临床专长冠心病及心血管危重症的诊断和治疗,尤其擅长经桡动脉介入治疗和复杂冠脉病变心内科学部主任、主任医师。教授、硕士研究生导师。发表国家级论文20余篇，发表SCI论文30篇
    </div>
    -->
    <div class="statistics">
      <div class="st-item"
        ><span>{{ props.cloudCustomData.doctorInfo.practiceTime }}年</span
        ><span>从业时间</span></div
      >
      <div class="st-item"
        ><span>{{ props.cloudCustomData.doctorInfo.receptionNum }}</span
        ><span>服务次数</span></div
      >
      <div class="st-item"
        ><span>{{ props.cloudCustomData.doctorInfo.goodCommentRate + '%' }}</span
        ><span>好评率</span></div
      >
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
const props = defineProps({
  cloudCustomData: {
    type: Object,
    default: () => {}
  }
})

// 执业编号
const professionalNoBuilder = (str, frontKeep, backKeep) => {
  if (!str || str.length <= frontKeep + backKeep) {
    return str // 如果字符串长度小于等于前后保留的总长度，则直接返回原字符串
  }

  // 获取需要替换的部分
  var middlePart = '*'.repeat(str.length - frontKeep - backKeep)

  // 拼接结果
  return str.slice(0, frontKeep) + middlePart + str.slice(-backKeep)
}
</script>

<style lang="scss" scoped>
// 医生信息消息
.doctor-info-review {
  width: 80%;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;

  .head {
    width: 100%;
    line-height: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .head-txt {
      color: #222222;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;

      .tag {
        padding: 2px 2px;
        margin-right: 4px;
        background-color: #e9e9ec;
        border-radius: 2px;
        color: #444444;
        font-size: 12px;
      }
    }
  }

  .title {
    width: 100%;
    margin-top: 12px;
    display: flex;
    justify-content: flex-start;
    align-items: stretch;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-right: 12px;
    }

    .ttl-local {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: flex-start;

      .ttl {
        line-height: 1;
        display: flex;
        justify-content: flex-start;
        align-items: baseline;
        color: #222222;
        font-size: 18px;
        font-weight: 600;

        .ttl-txt {
          font-size: 15px;
          font-weight: normal;
        }
      }

      .local {
        line-height: 1;
        color: #222222;
        font-size: 15px;
      }
    }
  }

  .profile {
    line-height: 1.4;
    margin-top: 12px;
    word-break: break-all;
    color: #666666;
    font-size: 13px;
  }

  .statistics {
    width: 100%;
    margin-top: 12px;
    padding: 12px 0;
    background-color: #f4f4f4;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .st-item {
      flex: 1;
      border-right: 1px solid #d8d8d8;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;

      &:last-child {
        border-right: none;
      }

      span {
        line-height: 1;

        &:first-child {
          color: #222222;
          font-size: 18px;
          font-weight: 600;
        }

        &:last-child {
          margin-top: 8px;
          color: #222222;
          font-size: 15px;
        }
      }
    }
  }
}
</style>
