<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item label="问诊日期" prop="createTime">
        <el-date-picker
v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px"
          :disabled-date="fnDisableDate" @calendar-change="dateChange" @clear="clear"/>
      </el-form-item>
      <el-form-item label="问诊类型" prop="inquiryBizType">
        <el-select v-model="queryParams.inquiryBizType" placeholder="请选择问诊类型" clearable class="!w-240px">
          <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.INQUIRY_BIZ_TYPE)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="问诊方式" prop="inquiryWayType">
        <el-select v-model="queryParams.inquiryWayType" placeholder="请选择问诊方式" clearable class="!w-240px">
          <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.INQUIRY_WAY_TYPE)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="问诊状态" prop="inquiryStatus">
        <el-select v-model="queryParams.inquiryStatus" placeholder="请选择问诊状态" clearable class="!w-240px">
          <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.INQUIRY_STATUS)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="问诊渠道" prop="bizChannelType">
        <el-select v-model="queryParams.bizChannelType" placeholder="请选择问诊渠道" @visible-change="getData" clearable style="min-width: 240px;">
          <el-option
v-loading="resourceLoading"
            v-for="item in dataResourceList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户端渠道" prop="clientChannelType">
        <el-select
          v-model="queryParams.clientChannelType"
          placeholder="请选择客户端渠道"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLIENT_CHANNEL_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="问诊单号" prop="pref">
        <el-input v-model="queryParams.pref" placeholder="请输入问诊单号" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="接诊医生" prop="doctorId" v-if="isSystemTenant()">
        <DoctorSelect :pref="queryParams.doctorPref" @success="(args) => queryParams.doctorPref = args" />
      </el-form-item>
       <el-form-item label="门店" prop="tenantId" v-if="shouldShowTenantField()">
        <TenantSelect  ref="tenantSelect" :tenant-id="queryParams.tenantId" @success="(args) => queryParams.tenantId = args" v-if="isSystemTenant()"/>
        <ServerTenantSelect ref="tenantSelect" :tenant-id="queryParams.tenantId" @success="(args) => queryParams.tenantId = args" v-else/>
      </el-form-item>
      <el-form-item
label="患者姓名" v-show="isSystemTenant() ? queryParams.tenantId != undefined : true"
        prop="patientGuid">
        <PatientSelect
:pref="queryParams.patientPref" :tenant-id="queryParams.tenantId"
          @success="(args) => queryParams.patientPref = args" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <!--        <el-button-->
        <!--          type="primary"-->
        <!--          plain-->
        <!--          @click="openForm('create')"-->
        <!--          v-hasPermi="['patient:inquiry-record:create']"-->
        <!--        >-->
        <!--          <Icon icon="ep:plus" class="mr-5px" /> 新增-->
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          type="success"-->
        <!--          plain-->
        <!--          @click="handleExport"-->
        <!--          :loading="exportLoading"-->
        <!--          v-hasPermi="['patient:inquiry-record:export']"-->
        <!--        >-->
        <!--          <Icon icon="ep:download" class="mr-5px" /> 导出-->
        <!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column type="index" width="50" />
      <el-table-column label="门店" align="center" prop="tenantName" min-width="120" v-if="shouldShowTenantField()" />
      <el-table-column label="问诊单号" align="center" prop="pref" />
      <el-table-column label="患者姓名" align="center" prop="patientName" />
      <el-table-column label="患者性别" align="center" prop="patientSex">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.patientSex" />
        </template>
      </el-table-column>
      <el-table-column label="患者手机号" align="center" prop="patientMobile" min-width="120" />
      <el-table-column label="互联网医院" align="center" prop="hospitalName" min-width="120" />
      <el-table-column label="接诊医生" align="center" prop="doctorName" />
      <el-table-column label="问诊日期" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="接诊时间" align="center" prop="startTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="完诊时间" align="center" prop="endTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="问诊状态" align="center" prop="inquiryStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INQUIRY_STATUS" :value="scope.row.inquiryStatus" />
        </template>
      </el-table-column>
      <el-table-column label="问诊方式" align="center" prop="inquiryWayType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INQUIRY_WAY_TYPE" :value="scope.row.inquiryWayType" />
        </template>
      </el-table-column>
      <el-table-column label="问诊类型" align="center" prop="inquiryBizType" min-width="110">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INQUIRY_BIZ_TYPE" :value="scope.row.inquiryBizType" />
        </template>
      </el-table-column>
      <el-table-column label="问诊渠道" align="center" prop="bizChannelType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BIZ_CHANNEL_TYPE" :value="scope.row.bizChannelType" />
        </template>
      </el-table-column>
      <el-table-column label="客户端渠道" align="center" prop="clientChannelType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLIENT_CHANNEL_TYPE" :value="scope.row.clientChannelType" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
link type="primary" @click="openDetail(scope.row.id)"
            v-hasPermi="['patient:inquiry-record:query']">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
:total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InquiryRecordForm ref="formRef" @success="getList" />
  <InquiryRecordDetail ref="detailRef" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InquiryRecordApi, InquiryRecordVO } from '@/api/inquiry/inquirydata'
import InquiryRecordForm from './InquiryRecordForm.vue'
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";
import DoctorSelect from "@/views/inquiry/doctor/DoctorSelect.vue";
import TenantSelect from "@/views/system/tenant/TenantSelect.vue";
import PatientSelect from "@/views/inquiry/inquirydata/PatientSelect.vue";
import {isSystemTenant, getTenantId, getTenantType} from "@/utils/auth";
import * as TenantApi from '@/api/system/tenant'
import { StoreTypeEnum } from "@/utils/constants";
import InquiryRecordDetail from './InquiryRecordDetail.vue'
import { getDataResource} from '@/api/transmission/thirdMedicationRecords/index'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import ServerTenantSelect from "@/views/system/store/components/ServerTenantSelect.vue";
/** 问诊记录 列表 */
defineOptions({ name: 'InquiryRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const { wsCache } = useCache()
const isChainHeadquarters = ref(false) // 是否为连锁总部
const loading = ref(true) // 列表的加载中
const list = ref<InquiryRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

const dataResourceList=ref([])
const organTypeList=[4,6]
const resourceLoading=ref(false)
const getData=async(value)=>{
  
   if(value&&!dataResourceList.value.length){
    try {
      resourceLoading.value=true
       dataResourceList.value= await getDataResource({organTypeList})
    } catch (e) {
      console.log(e);
      
    }finally{
      resourceLoading.value=false
    }
   }
}
// 限制日期选择范围（只能选择两个月内）------start
let firstMonth=new Date()
let firstMonthChange=false
let MonthsAgo=0
let MonthsLater=0
const dateChange=(val)=>{
 
  firstMonth=new Date(val[0])//new Date创建一个副本，不然两者同变化

  firstMonthChange=true
  let month=1;

  if(val[0].getMonth()-month<=0){
  
      firstMonth.setFullYear(val[0].getFullYear() - 1);
      MonthsAgo=firstMonth.setMonth(val[0].getMonth()+12-month,1)//月前
     
      firstMonth.setFullYear(val[0].getFullYear())//将更改的年数变回来
      
  }else{
    
    MonthsAgo=firstMonth.setMonth(val[0].getMonth()-month,1)//月前
   
  }
MonthsLater=firstMonth.setMonth(val[0].getMonth()+month+1,0)//月后
  if(val[1]!==null)
  {
    firstMonthChange =false
  }
}

const fnDisableDate = (currDate) => {
if(firstMonthChange){

  return currDate.getTime()<MonthsAgo || currDate.getTime()>MonthsLater

}else{
  return false;
}
}
const clear=()=>{
  firstMonthChange =false
}
// 限制日期选择范围（只能选择两个月内）------end




const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pref: undefined,
  guid: undefined,
  tenantId: undefined,
  patientPref: undefined,
  patientName: undefined,
  patientAge: undefined,
  patientSex: undefined,
  patientMobile: undefined,
  hospitalId: undefined,
  hospitalName: undefined,
  deptCode: undefined,
  deptName: undefined,
  preTempId: undefined,
  doctorPref: undefined,
  inquiryStatus: undefined,
  cancelReason: undefined,
  inquiryWayType: undefined,
  inquiryBizType: undefined,
  clientChannelType: undefined,
  clinetOsType: undefined,
  bizChannelType: undefined,
  medicineType: undefined,
  autoInquiry: undefined,
  unbleAutoReason: undefined,
  imPlatform: undefined,
  orderNo: undefined,
  ext: undefined,
  startTime: undefined,
  endTime: undefined,
  mp3Url: undefined,
  mp3Status: undefined,
  mp4Url: undefined,
  imPdf: undefined,
  streamId: undefined,
  transcodingId: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const tenantSelect = ref()

/** 判断是否应该显示门店字段 */
const shouldShowTenantField = () => {
  return isSystemTenant() || isChainHeadquarters.value
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InquiryRecordApi.getInquiryRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  tenantSelect.value?.resetTid()
  queryParams.patientPref = null
  queryParams.doctorPref = null
  queryParams.tenantId = null
  setDefaultDateRange()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const detailRef = ref(null)
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
// 查看详情
const openDetail = (id: number) => {
  detailRef?.value.open(id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InquiryRecordApi.deleteInquiryRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InquiryRecordApi.exportInquiryRecord(queryParams)
    download.excel(data, '问诊记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


const setDefaultDateRange = () =>{
  console.log("set deft")
  const today = new Date()
  const threeDaysAgo = new Date()
  threeDaysAgo.setDate(today.getDate() - 6)
  // 设置时间部分为00:00:00和23:59:59
  const startOfDay = new Date(threeDaysAgo.toISOString().split('T')[0] + 'T00:00:00')
  const endOfDay = new Date(today.toISOString().split('T')[0] + 'T23:59:59')
  queryParams.createTime[0] = formatDate(startOfDay)
  queryParams.createTime[1] = formatDate(endOfDay)
}

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  let res = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return res;
}

const checkTenantType = async() => {
    try {
        isChainHeadquarters.value = getTenantType() === StoreTypeEnum.CHAIN_HEADQUARTER.value
    } catch (error) {
      console.error('获取租户信息失败', error)
      isChainHeadquarters.value = false
    } finally {
      getList()
    }
  }

/** 初始化 **/
onMounted(() => {
  setDefaultDateRange()
  checkTenantType()
})
</script>
