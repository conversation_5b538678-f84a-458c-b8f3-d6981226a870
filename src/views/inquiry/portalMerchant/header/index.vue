<template>
  <div class="header">
    <div class="left-header">
      <!-- 门店选择框 -->
      <span style="min-width: 60px">门店名称</span>
      <el-select
        @change="selectStore"
        v-model="selectedStoreName"
        filterable
        placeholder="请选择门店"
        style="max-width: 200px"
      >
        <el-option v-for="item in storeList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <!-- 立即续费 status === 1 -->
<!--   未作 暂时隐藏
      <div class="package-info" v-if="isShowInfo">
        <div v-if="packageInfo.hasPackage === 1">
          <div class="infos-container" v-if="packageInfo.status === 1">
            <div class="infos" style="display: flex; width: fit-content">
              <div class="infos-day">
                问诊套餐：【<span
                  :title="packageInfo.packageName"
                  style="
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                    white-space: nowrap;
                    width: 50px;
                    display: inline-block;
                  "
                  >{{ packageInfo.packageName }}kkkkkkkkkkkkkkkkkkkkkk</span
                >】
                <span>剩余{{ packageInfo.cishu }}次到期</span>
              </div>
              <div class="valid-date" style="flex-wrap: nowrap; white-space: nowrap">
                有效期至:{{ packageInfo.severEnd }}
              </div>
            </div>
            <div class="btns" style="display: flex">
              <span
                @click="goRenew"
                style="background-color: #ffedc2; color: black; margin-left: 5px"
                >立即续费</span
              >
              <span
                @click="
                  router.push({
                    path: '/store/store',
                    state: {
                      type: 1
                    }
                  })
                "
                style="color: black"
                >开通记录</span
              >
            </div>
          </div>
        </div>
        <div v-else>
          <div class="infos-container no-package">
            <div class="infos">您暂无服务套餐</div>
            <div class="btns">
              <span
                @click="goRenew"
                style="background-color: #ffedc2; color: black; margin-left: 5px"
                >立即开通</span
              >
              <span
                @click="
                  router.push({
                    path: '/store/store',
                    state: {
                      type: 1
                    }
                  })
                "
                style="color: black"
                >开通记录</span
              >
            </div>
          </div>
        </div>
      </div>
-->

    </div>

    <div class="right-header">
      <!-- 二维码 -->
      <div style="display: flex; align-items: center; margin-left: auto">
<!-- 未作 暂时隐藏
        <el-dropdown
          v-if="qrCodeList && qrCodeList.length"
          trigger="click"
          class="code-dropdown"
          @visible-change="onChanegeCodeDrowdown"
        >
          <div class="el-dropdown-link code-container" v-if="!isSystemTenant()" style="display: flex">
            <img src="@/assets/形状.png" alt="11" />
            <span
              :class="{ 'is-show-code': isShowCode }"
              style="
                text-align: justify; /* 使文本两端对齐 */
                overflow-wrap: break-word; /* 强制文本换行 */
                word-wrap: break-word;
                display: inline-block;
              "
              >下载推广二维码</span
            >
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="code" class="header-code-menu">
                <div class="header-qr-code">
                  <div class="imgs">
-->
                    <!-- <div
                  class="code-item"
                  v-for="(item, index) in qrCodeList"
                  :key="index"
                >
                  <div class="code-content" v-if="item.qrCodeSource !== 3">
                    <div class="code-title">
                      {{ mapCodeText(item.qrCodeSource).title }}
                    </div>
                    <div class="code-text">
                      {{ mapCodeText(item.qrCodeSource).text }}
                    </div>
                    <div class="code-img">
                      <img :src="item.qrCodeUrl" alt="" />
                    </div>
                    <div class="store-name" :title="selectedStoreName">
                      {{ selectedStoreName }}
                    </div>
                    <div class="code-step">
                      <span>{{ mapCodeText(item.qrCodeSource).step[0] }}</span>
                      <img src="@/assets/header/arrows.png" alt="" />
                      <span>{{ mapCodeText(item.qrCodeSource).step[1] }}</span>
                      <img src="@/assets/header/arrows.png" alt="" />
                      <span>{{ mapCodeText(item.qrCodeSource).step[2] }}</span>
                      <img src="@/assets/header/arrows.png" alt="" />
                      <span>{{ mapCodeText(item.qrCodeSource).step[3] }}</span>
                    </div>
                  </div>
                  <div class="code-logo" v-if="item.qrCodeSource !== 3">
                    <img src="@/assets/header/logo-green.png" alt="" />
                  </div>
                  <div
                    class="code-img-container"
                    v-if="item.qrCodeSource === 3"
                  >
                    <img :src="item.qrCodeUrl" alt="" />
                  </div>
                </div> -->
<!--  
                    <div class="code-item">
                      <div class="code-content">
                        <div class="code-img-container">
                          <img :src="qrCodeUrl" alt="" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="code-btns">
                    <div class="more-code" @click="goDrugstoreInfo('')"> 更多二维码 </div>
                    <div class="all-down" @click="downAll">全部下载</div>
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
-->
        <el-dropdown
          trigger="click"
          :hide-on-click="false"
          class="app-dropdown"
          @visible-change="onChanegeAppDrowdown"
        >
          <div class="el-dropdown-link code-container"  style="display: flex">
            <img src="@/assets/手机.png" alt="" />
            <span
              :class="{ 'is-show-code': isShowApp }"
              style="
                text-align: justify; /* 使文本两端对齐 */
                overflow-wrap: break-word; /* 强制文本换行 */
                word-wrap: break-word;
                display: inline-block;
              "
              >App下载</span
            >
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="code" class="header-code-menu">
                <div class="header-qr-code">
                  <div class="img-container">
                    <img :src="downUrl" alt="" />
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- 账号管理 -->
        <el-dropdown @visible-change="showDrowdown"  trigger="click" @command="handleCommand">
          <span class="el-dropdown-link icon_01">
             <img :src="avatarUrl || defaultAvatar" style="width: 28px; height: 28px" />
            <span
          class="account-span"
          :title="userInfo.account"
          style="margin-left: 5px; margin-right: 5px"
          >{{ userInfo.account }}</span>
            <img v-if="dropdownStatus" src="@/assets/header/arrow-top.png" />
            <img v-else src="@/assets/header/arrow-bottom.png" />
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="个人中心">
                <img src="@/assets/header/account-manage.png" />
                个人中心
              </el-dropdown-item>

              <el-dropdown-item command="退出登录">
                <img src="@/assets/header/logout.png" />
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      
      </div>
      <!-- <reminder-dialog ref="reminderDialogRef"/> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { getStoreList, changeStore, getCureList } from './storeApi.ts'
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/modules/user'
import {
  getAccessToken,
  getRefreshToken,
  getTenantId,
  removeToken,
  setToken,
  setTenantId, isSystemTenant, setTenantType
} from '@/utils/auth'

// import store from "@/store";
// import { mapState } from "vuex";
// import {loginOut} from "@/api/login/index.ts";
// import {
//   clearStorageBeforeLogout,
//   isElectron,
//   formatDuring,
//   destoryWebsocketInElectron,
//   getUserInfo,
//   emitAccountIDToElectron,
// } from "@/utils";
// import eventBus from "@/utils/eventBus";
// import {
//   packageRemainingTime,
//   getMenus,
//   getStoreList,
//   qrCode,
// } from "@/api/auth";
// import ReminderDialog from "@/components/reminderDialog.vue";

import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { CACHE_KEY, useCache, deleteUserCache } from '@/hooks/web/useCache'
import defaultAvatar from '@/assets/header/default-avatar.png'; // 导入本地图片
import { useTagsViewStore } from '@/store/modules/tagsView'

const { wsCache } = useCache()
let qrCodeUrl = ref('')
let downUrl = ref('')
const avatarUrl = ref('')
const tagsViewStore = useTagsViewStore()

onMounted(async () => {
  const userId = useUserStore().getUser.id
  let res = await getStoreList(userId)
  storeList.value = res
  console.log('res', res, useUserStore().getUser.avatar)
  avatarUrl.value = useUserStore().getUser.avatar
  //?useUserStore().getUser.avatar:wsCache.get(CACHE_KEY.USER).user.avatar其他·用户·第一次登录时用户图像不能·获取到·
  userInfo.value.account = useUserStore().getUser.nickname
  selectedStoreName.value = getTenantId()
  if(!isSystemTenant()){
    let Arr = await getCureList()
    qrCodeUrl.value = Arr[0].qrCodeUrl
    downUrl.value = Arr[1].qrCodeUrl
  }

})
const selectStore = async () => {
  console.log('selectedStoreName', getAccessToken())
  let res = await changeStore({
    userId: useUserStore().getUser.id,
    tenantId: selectedStoreName.value,
    token: getAccessToken()
  })
  console.log('res111', res)
  //  setToken(res.accessToken)
  setTenantId(res.tenantId)
  setTenantType(res.tenantType);
  setToken({
    accessToken: res.accessToken,
    refreshToken: res.refreshToken
  })
  //重新设置token和门店id后刷新页面
  tagsViewStore.delAllVisitedViews()  //清楚tags标签页
  await router.push('/home') //跳转home页面
  router.go(0)
}
const router = useRouter()
const storeList = ref([])
const selectedStoreName = ref('')
const dropdownStatus = ref(false)
const userInfo = ref({})
const codeUrl = ref('@/assets/header/scan-icon.png')
const codeHighlightUrl = ref('@/assets/header/scan-icon-highlight.png')
const appUrl = ref('@/assets/header/app-icon.png')
const appHighlightUrl = ref('@/assets/header/app-icon-highlight.png')

const isShowCode = ref(false) // 下载推广二维码是否展示
const isShowApp = ref(false) //  App下载否展示
const isShowInfo = ref(true) // 是否展示服务包信息
// const storeId=ref(""); // 荷叶id
// const lzId=ref("");// 灵芝id
const qrCodeList = ref([
  { qrCodeSource: 2, qrCodeUrl: '@/assets/header/app-icon-highlight.png' },
  { qrCodeSource: 2, qrCodeUrl: '@/assets/header/app-icon-highlight.png' }
]) // 二维码
const packageInfo = ref({
  hasPackage: 1,
  status: 1,
  packageName: 'aaa',
  severEnd: '2022-01-01',
  curStatus: 1,
  curPackageName: '1111111111111111111111111111111112222222222222222222eeeeeeeeeeeee'
})


// const isShowQrCode=ref(true);
// const currentPopO2oFlag=ref(0);

// computed: {
//   ...mapState("menuStore", ["isLeftMenuCollapse"]),
// },
// mounted() {
//   let userInfo = localStorage.getItem("userInfo");

//   this.userInfo = userInfo ? JSON.parse(userInfo) : {};
// },
// created() {
//   this.storeId = localStorage.getItem("storeId");
//   this.lzId = localStorage.getItem("lzId");

//   // 没有门店 全部门店 不展示服务包信息及二维码
//   this.isShowQrCode = this.storeId === "-1" || !this.storeId ? false : true;
//   this.isShowPatientPackageInfo();

//   // 购买成功后 更新服务包信息
//   eventBus.$on("updateServiceInfo", () => {
//     this.getPatientPackage();
//   });

//   // 未绑定门店 && 第一次新建门店成功
//   eventBus.$on("updateStoreIdAndLzId", (data) => {
//     this.storeId = data.storeId;
//     this.lzId = data.lzId;
//     this.isShowPatientPackageInfo();
//     this.isShowQrCode = true;
//     this.getPatientPackage();
//     this.getQrCode();
//   });

//   this.getStoreList(); // 手动刷新页面 会走这里
//   this.getPatientPackage();

//   if (this.isShowQrCode) {
//     this.getQrCode();
//   }

//   eventBus.$on("RefreshStoreList", () => this.getStoreList()); // 事件通知 重新拿数据 (后续进入home页面, 重新判断跳转逻辑, 或者门店入驻列表点选门店 需更新header)
// },

// // 是否展示灵芝服务包信息
//  function isShowPatientPackageInfo() {
//   if (storeId.value === "-1" || !lzId.value ||lzId.value === "null") {
//     isShowInfo.value = false;
//   } else {
//     isShowInfo.value = true;
//   }
// }
// // 二维码
// function getQrCode() {
//   let params = {
//     drugstoreId: storeId.value,
//   };
//   qrCode(params)
//     .then((res) => {
//       if (res.data.code === 0) {
//         qrCodeList.value =
//           res.data.result &&
//           res.data.result.filter((item) => item.qrCodeUrl);
//       }
//     })
//     .catch((err) => {
//       ElMessage.error(err.msg || err);
//     });
// }
function mapCodeText(type) {
  // type 1：药店 2：用户 3：问诊
  if (type === 1) {
    return {
      title: '微信小程序',
      text: '快速破零 玩转私域流量',
      step: ['微信扫码', '店铺选药加购', '支付订单', '骑手配送']
    }
  } else if (type === 3) {
    return {
      title: '扫码问医生',
      text: '3万+专业医生 3秒快速接通',
      step: ['微信扫码', '填写信息', '咨询医生', '查看处方']
    }
  } else if (type === 2) {
    return {
      title: '扫码关注公众号',
      text: '线上问诊 线上购药',
      step: ['微信扫码', '关注公众号', '访问店铺首页', '下单购药']
    }
  }
}
// // 套餐包数据
// function getPatientPackage() {
//   let params = {
//     lzId: lzId.value,
//   };
//   if (!params.lzId || params.lzId === "null") return;
//   packageRemainingTime(params)
//     .then((res) => {
//       if (res.data.code === 0) {
//         packageInfo.value = res.data.result;
//       } else {
//         isShowInfo.value = false;
//       }
//     })
//     .catch((err) => {
//       isShowInfo.value = false;
//       ElMessage.error(err.msg || err);
//     });
// }
// 下载图片
function downImg(src, downName, index) {
  let ele = document.createElement('a')
  ele.setAttribute('id', index)
  ele.href = 'data:image/png;base64,' + src
  ele.setAttribute('download', downName)
  document.body.append(ele)
  ele.click()
  ele.remove()
}
// 全部下载
function downAll() {
  if (qrCodeList.value && qrCodeList.value.length) {
    qrCodeList.value.forEach((item, index) => {
      downImg(item.qrCodeBase64, item.downloadName, index)
    })
  }
}

// 立即续费
function goRenew() {
  router.push({
    path: '/inquiry/portal-merchant/renew-immediately'
  })
}
// // 开通记录
// function goDrugstoreInfo(active) {
//   if (active === "3") {
//     eventBus.$emit("changeActiveIndex", { activeIndex: active });
//   }
// router.push({
//     path: "/storeInformation/storeInformationCollection",
//     query: {
//       activeIndex: active,
//     },
//   });
// }
const changeNum = () => {
  router.push({ path: '/user/profile' })
  //       let userId = useUserStore().getUser.id
  //       let data ={
  //         userId:userId,
  //         tenantId:getTenantId()
  //       }
  // let res = await changeStore(data)
  // console.log('res222',res)
}

function onChanegeCodeDrowdown(command) {
  isShowCode.value = command
}
function onChanegeAppDrowdown(command) {
  isShowApp.value = command
}
function showDrowdown(st) {
  dropdownStatus.value = st
}
const handleLogout = async () => {
  // loginOut()
  //   .then((res) => {
  //     // console.log('res234',res)
  //     if(res){
  //     removeToken();
  //     router.push({
  //       path: "/login",
  //     });

  //   }} )
  //  console.log('res234',res)
  await useUserStore().loginOut()
  tagsViewStore.delAllViews()
  router.push({
    path: '/login'
  })
}
function handleCommand(command) {
  switch (command) {
    case '个人中心':
      changeNum()
      break
    case '门店管理':
      router.push(`/storeInformation/storeManagementList?type=drugstore`)
      break
    case '门店申请':
      router.push(`/storeInformation/storeApplicant?type=drugstoreSettled`)
      break
    case '退出登录':
      handleLogout()
      break
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
.wider-header {
  left: 74px;
  width: calc(100vw - 74px);
}
.infos {
  display: flex;
  width: fit-content;
  // max-width: 300px;
  .infos-day {
    margin-right: 20px;
    flex-wrap: nowrap;
    white-space: nowrap;
    display: flex;
  }
  .valid-date {
    flex-wrap: nowrap;
    white-space: nowrap;
  }
}
// .code-container{
//   white-space: nowrap;
// }
.overflowHiden {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  width: 50px;
  display: inline-block;
}
.up-down {
  text-align: justify; /* 使文本两端对齐 */
  overflow-wrap: break-word; /* 强制文本换行 */
  word-wrap: break-word;
  display: inline-block;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  font-size: 14px;
  height: 50px;
  line-height: 50px;
  // padding: 0 20px;
  transition:
    width 0.4s,
    left 0.4s;
  background: #f7f8f9;
  width: 100%;

  .left-header {
    width: 70%;
  }
  .right-header {
    width: 30%;
    line-height: 50px;
  }
  .el-select :deep(.el-input input) {
    width: 300px;
    height: 36px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 20px;
  }

  > span {
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 17px;
    margin-left: 20px;
  }

  .el-dropdown {
    // float: right;
    cursor: pointer;

    .el-dropdown-link img {
      width: 10px;
      height: 6px;
      margin-left: 8px;
      vertical-align: middle;
    }

    > span {
      font-size: 14px;
      font-family: Helvetica;
      text-align: left;
      color: #333333;
      line-height: 17px;
    }
    &.app-dropdown {
      margin-right: 10px;
    }
    &.code-dropdown {
      margin-right: 10px;
    }
    .icon_01{
      padding-right:23px;
    }
    .el-dropdown-link {
      .account-span {
        display: inline-block;
        max-width: 90px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        vertical-align: middle;
      }
      &.code-container {
        & > img {
          width: 16px;
          height: 16px;
          vertical-align: middle;
          margin-right: 6px;
        }
        & > span {
          color: #333333;
          vertical-align: middle;
          &.is-show-code {
            color: #00b955;
          }
        }
      }
    }
  }

  & > div {
    display: flex;
    align-items: center;
    .package-info {
      margin-left: 10px;
      .infos-container {
        display: flex;
        align-items: center;
        width: 100%;
        overflow: auto;
        .info {
          width: fit-content;
          max-width: 300px;
          margin-right: 10px;
          .infos-day {
            white-space: nowrap;
            flex-wrap: nowrap;
            display: flex;
            // align-items: center;
            .package-name {
              display: inline-block;
              max-width: 130px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
          }
          & > div {
            height: 20px;
            line-height: 20px;
            &.valid-date {
              & > span:first-child {
                color: #999;
              }
            }
          }
        }
        .btns {
          display: flex;
          flex-wrap: nowrap;
          // @media (max-width: 1366px) {
          //   flex-direction: column;
          // }
          & > span {
            display: inline-block;
            // color: #efefef;
            width: 90px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            &:hover {
              color: #fff;
              background-color: #efefef;
              border-radius: 2px;
            }
          }
        }
        &.no-package {
          .btns {
            flex-direction: row;
          }
        }
      }
    }
  }
}

.el-dropdown-menu {
  img {
    vertical-align: middle;
    width: 16px;
    height: 16px;
  }
}

.el-dropdown-menu__item.header-code-menu {
  &:hover {
    background-color: #fff !important;
  }
  padding: 0 10px;
  .header-qr-code {
    .imgs {
      display: flex;
      .code-item {
        width: 268px;
        margin-right: 10px;

        .code-img-container {
          & > img {
            width: 100%;
            height: 100%;
          }
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .code-btns {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
      & > div {
        color: #333333;
        width: 130px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        border-radius: 5px;
        cursor: pointer;
        &.more-code {
          border: 1px solid #eeeeee;
          margin-right: 8px;
        }
        &.all-down {
          color: #ffffff;
          background: #00b955;
          border-radius: 4px;
        }
      }
    }
    .img-container {
      width: 270px;
      & > img {
        width: 100%;
        height: auto;
      }
    }
    .btn-down {
      color: #ffffff;
      height: 36px;
      line-height: 36px;
      text-align: center;
      cursor: pointer;
      margin-top: 10px;
      background-color: #00b955;
      border-radius: 4px;
    }
  }
}
</style>

