<template>
  <div class="home-page-container">
    <div class="panel-1">
      <el-row class="w-100%">
        <el-col :span="18">
          <!-- 待办数据 -->
          <div
            class="pending-info bg-white rounded-lg pt-20px pb-20px flex justify-between items-start"
          >
            <div
              class="pending-info__item flex-1 pl-20px flex flex-col justify-stretch items-start"
              v-for="(todoValue, todoKey) in todoDashboard"
              :key="todoKey"
            >
              <div
                class="title w-100% text-xl font-bold text-[#333333] flex justify-start items-center"
              >
                <img class="mr-8px" :src="todoValue.icon" />
                {{ todoValue.label }}
              </div>
              <div
                class="pending-info__item-content w-100% mt-20px gap-18px flex flex-col justify-between"
              >
                <div
                  class="item flex items-center"
                  v-for="item in todoValue.list"
                  :key="item.value"
                >
                  <div class="label flex-1 text-[#666666] text-base">{{ item.label }}</div>
                  <div class="value flex-1 text-[#28272C] text-base font-medium">{{
                    item.value
                  }}</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 运营数据 -->
          <div class="operational-data bg-white rounded-lg pt-16px pb-16px mt-12px">
            <div class="operational-data__header pl-16px pr-16px flex justify-between items-center">
              <div class="title text-xl font-bold text-[#333333]">经营数据</div>
              <div
                class="date ml-10px text-sm text-[#999999] flex-1 flex justify-start items-center"
                >统计时间: 2025-01-02 13:34:26
                <el-icon class="cursor-pointer ml-8px"><Refresh /></el-icon>
              </div>
              <div
                class="btn flex items-center bg-[#EAEAEA] rounded p-2px items-center cursor-pointer"
              >
                <div
                  class="btn-item text-sm pt-4px pb-4px pl-10px pr-10px text-[#666] font-medium flex items-center justify-center rounded"
                  v-for="item in operationalTabs"
                  :key="item.name"
                  @click="setOperationlActive(item.value)"
                  :class="{ active: item.value === operationalActive }"
                  >{{ item.name }}</div
                >
              </div>
            </div>
            <div class="operational-data__content pt-16px pb-16px flex justify-start items-center">
              <template v-for="item in operateDashboard" :key="item">
                <div class="operational-data_item flex-1 pl-20px">
                  <div class="title text-[#28272C] text-base">{{ item.label }}</div>
                  <div class="num text-[#28272C] text-4xl font-semibold mt-16px">{{
                    item.value
                  }}</div>
                  <div
                    class="increase text-[#666666] text-base mt-20px flex justify-start items-center"
                    >同比：<span class="text-[#222222]">{{ item.rate }}</span
                    ><img class="icon-rate ml-8px" :src="item.rateIcon" />
                  </div>
                </div>
              </template>
            </div>
          </div>
          <!-- 预警数据 -->
          <div class="warning-data bg-white rounded-lg pt-16px pb-16px mt-12px">
            <div class="warning-data__header pl-16px pr-16px flex justify-between items-center">
              <div class="title text-xl font-bold text-[#333333]">预警数据</div>
              <div
                class="date ml-10px text-sm text-[#999999] flex-1 flex justify-start items-center"
                >统计时间: 2025-01-02 13:34:26
                <el-icon class="cursor-pointer ml-8px"><Refresh /></el-icon>
              </div>
              <div
                class="btn flex items-center bg-[#EAEAEA] rounded p-2px items-center cursor-pointer"
              >
                <div
                  class="btn-item text-sm pt-4px pb-4px pl-10px pr-10px text-[#666] font-medium flex items-center justify-center rounded"
                  v-for="item in warningTabs"
                  :key="item.name"
                  @click="setWarningActive(item.value)"
                  :class="{ active: item.value === warningActive }"
                  >{{ item.name }}</div
                >
              </div>
            </div>
            <div class="warning-data__content pt-16px pr-16px pl-16px">
              <el-table :data="medicines" style="width: 100%">
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="药品名称" width="200" />
                <el-table-column prop="specification" label="规格" width="200" />
                <el-table-column prop="manufacturer" label="生产厂家" />
                <el-table-column prop="stock" label="可用库存" width="100" />
                <el-table-column label="上次采购价" width="120">
                  <template #default="{ row }">
                    <span>￥{{ row.previousPrice }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="药帮忙抢购价" width="160">
                  <template #default="{ row }">
                    <span class="text-[#F00909] font-bold">￥{{ row.promotionPrice }}</span
                    ><span class="text-xs text-[#999999] line-through ml-3px"
                      >￥{{ row.promotionPrice }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column width="90">
                  <template #default>
                    <el-button type="success">抢购</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-row justify="end" class="mt-16px">
                <el-pagination
                  layout="total, sizes, jumper, prev, pager, next"
                  :page-size="10"
                  :total="30"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </el-row>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div
            class="cash-register w-100% h-198px bg-[#25C46E] rounded-lg flex justify-center items-center"
          >
            <div
              class="name text-white text-4xl font-bold flex justify-center items-center gap-10px cursor-pointer"
            >
              <img class="w-40px h-40px" src="@/assets/home/<USER>" /> 收银台</div
            >
          </div>
          <div class="banner w-100% h-129px rounded-lg mt-12px">
            <img class="w-100% h-100% rounded-lg" src="@/assets/home/<USER>" />
          </div>
          <div class="yao-order w-100% rounded-lg mt-12px bg-white p-16px">
            <div class="yao-order__title text-xl font-bold text-[#000000] leading-none"
              >药帮忙订单</div
            >
            <div class="yao-order__content mt-13px flex justify-between items-center">
              <div class="yao-order__num text-base text-[#000000]">待入库：406</div>
              <div class="yap-order__btn">
                <el-button type="success">一键入库</el-button>
              </div>
            </div>
          </div>
          <div class="quick-entry bg-white rounded-lg mt-12px p-16px">
            <div class="quick-entry__header flex justify-between items-center">
              <div class="quick-entry__title leading-none text-xl font-bold text-[#000000]"
                >快捷入口</div
              >
              <div
                class="quick-entry__setting leading-none flex items-center gap-5px cursor-pointer text-sm text-[#999999]"
                ><el-icon size="14px"><Setting /></el-icon> 自定义</div
              >
            </div>
            <div class="quick-entry__content mt-20px">
              <div class="quick-entry__list flex justify-start items-start flex-wrap">
                <template v-for="item in quickEntryList" :key="item.label">
                  <div class="w-25% mb-4px flex justify-center items-center">
                    <div
                      class="quick-entry__ite flex justify-center items-center gap-5px flex-col hover:bg-[#F5F5F5] rounded-lg h-80px w-80px cursor-pointer"
                    >
                      <img class="w-40px h-40px" :src="item.icon" />
                      <div class="name leading-none text-[#000000] text-xs">{{ item.label }}</div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Refresh, Setting } from '@element-plus/icons-vue'

import orderIcon from '@/assets/home/<USER>'
import marketingIcon from '@/assets/home/<USER>'
import storeIcon from '@/assets/home/<USER>'

import iconArrowDown from '@/assets/home/<USER>'
import iconArrowUp from '@/assets/home/<USER>'

import iconEntry1 from '@/assets/home/<USER>'
import iconEntry2 from '@/assets/home/<USER>'
import iconEntry3 from '@/assets/home/<USER>'
import iconEntry4 from '@/assets/home/<USER>'
import iconEntry5 from '@/assets/home/<USER>'
import iconEntry6 from '@/assets/home/<USER>'

// 待办数据
const todoDashboard = ref({
  order: {
    label: '订单待办',
    icon: orderIcon,
    list: [
      { label: '今日预约单', value: '4' },
      { label: '异常配送', value: '1' },
      { label: '到期预约单', value: '2' },
      { label: '用户催单', value: '2' }
    ]
  },
  marketing: {
    label: '营销待办',
    icon: marketingIcon,
    list: [
      { label: '即将到期活动', value: '4' },
      { label: '活动成交订单数', value: '472' },
      { label: '活动成交金额条数', value: '23' },
      { label: '短信推送点击次数', value: '1562' }
    ]
  },
  store: {
    label: '店铺待办',
    icon: storeIcon,
    list: [
      { label: '处方待登记', value: '4' },
      { label: '待养护药品', value: '472' }
    ]
  }
})

// 经营数据
const operationalActive = ref(0)
const operationalTabs = [
  {
    name: '当日',
    value: 0
  },
  {
    name: '当月',
    value: 1
  }
]
const setOperationlActive = (val) => {
  operationalActive.value = val
}
const operateDashboard = ref([
  {
    label: '销售总金额(元)',
    value: '2000.89',
    rate: '23.7%',
    rateIcon: iconArrowDown
  },
  {
    label: '医保收入金额(元)',
    value: '19682.37',
    rate: '15.9%',
    rateIcon: iconArrowDown
  },
  {
    label: '开立处方量(张)',
    value: '127',
    rate: '15.9%',
    rateIcon: iconArrowUp
  },
  {
    label: '客流量(人次)',
    value: '2037',
    rate: '15.9%',
    rateIcon: iconArrowUp
  },
  {
    label: '客单价(元)',
    value: '73.82',
    rate: '15.9%',
    rateIcon: iconArrowUp
  }
])

// 预警数据
const warningActive = ref(0)
const warningTabs = [
  {
    name: '商品缺货',
    value: 0
  },
  {
    name: '商品效期',
    value: 1
  },
  {
    name: '商品积货',
    value: 2
  },
  {
    name: '供应商效期',
    value: 3
  }
]
const setWarningActive = (val) => {
  warningActive.value = val
}
const medicines = [
  {
    id: 1,
    name: '蒲地蓝消炎口服液(寿牌)',
    specification: '10ml*10支',
    manufacturer: '济川药业集团有限公司',
    stock: 173,
    previousPrice: 34.63,
    promotionPrice: 34.22
  },
  {
    id: 2,
    name: '999感冒灵颗粒',
    specification: '10g*9袋',
    manufacturer: '华润三九(枣庄)药业有限公司',
    stock: 82,
    previousPrice: 12.75,
    promotionPrice: 12.85
  },
  {
    id: 3,
    name: '硝苯地平控释片(拜新同)',
    specification: '30mg*7s 滴膜衣',
    manufacturer: '拜耳医药保健有限公司',
    stock: 102,
    previousPrice: 23.25,
    promotionPrice: 23.01
  },
  {
    id: 4,
    name: '阿尔马尔盐酸阿罗洛尔片',
    specification: '10mg*10s糖衣',
    manufacturer: '住友制药(苏州)有限公司',
    stock: 45,
    previousPrice: 31.52,
    promotionPrice: 31.38
  },
  {
    id: 5,
    name: '万艾可枸橼酸西地那非片',
    specification: '0.1g*5s 薄膜衣',
    manufacturer: '辉瑞制药(大连)有限公司',
    stock: 173,
    previousPrice: 431.68,
    promotionPrice: 3430.79
  },
  {
    id: 6,
    name: '倪福达硝苯地平缓释片(Ⅱ)',
    specification: '20mg*30s',
    manufacturer: '青岛黄海制药有限责任公司',
    stock: 23,
    previousPrice: 19.12,
    promotionPrice: 12 // 特殊标注处理
  },
  {
    id: 7,
    name: '硝苯地平控释片(拜新同)',
    specification: '30mg*7s 薄膜衣',
    manufacturer: '拜耳医药保健有限公司',
    stock: 102,
    previousPrice: 23.25,
    promotionPrice: 23.01
  },
  {
    id: 8,
    name: '阿尔马尔盐酸阿罗洛尔片',
    specification: '10mg*10s糖衣',
    manufacturer: '住友制药(苏州)有限公司',
    stock: 45,
    previousPrice: 31.52,
    promotionPrice: 31.38
  },
  {
    id: 9,
    name: '倪福达硝苯地平缓释片(Ⅱ)',
    specification: '20mg*30s',
    manufacturer: '青岛黄海制药有限责任公司',
    stock: 23,
    previousPrice: 19.12,
    promotionPrice: 12 // 特殊标注处理
  },
  {
    id: 10,
    name: '万艾可枸橼酸西地那非片',
    specification: '0.1g*5s 薄膜衣',
    manufacturer: '辉瑞制药(大连)有限公司',
    stock: 173,
    previousPrice: 431.68,
    promotionPrice: 3430.79
  }
]

// 快捷入口
const quickEntryList = ref([
  {
    label: '处方登记',
    icon: iconEntry1
  },
  {
    label: '采购/要货',
    icon: iconEntry2
  },
  {
    label: '零售流水对账',
    icon: iconEntry3
  },
  {
    label: '问医生',
    icon: iconEntry4
  },
  {
    label: '药品管理',
    icon: iconEntry5
  },
  {
    label: '库存查询',
    icon: iconEntry6
  }
])
</script>

<style lang="scss" scoped>
.home-page-container {
  width: calc(100% + 16px);
  margin-top: -8px;
  margin-left: -8px;
  --el-color-success: #00b955;

  .panel-1 {
    .el-col.el-col-18 {
      padding-right: 6px;
    }
    .el-col.el-col-6 {
      padding-left: 6px;
    }
  }

  .pending-info {
    .pending-info__item {
      border-right: 1px solid #e7e7e7;

      &:last-child {
        border-right: none;
      }

      .title {
        line-height: 1;
      }

      .pending-info__item-content {
        .item {
          .label {
            line-height: 1;
          }

          .value {
            line-height: 1;
          }
        }
      }
    }
  }

  .operational-data {
    .operational-data__header {
      .title {
        line-height: 1;
      }

      .date {
        line-height: 1;

        .el-icon {
          color: #000000;
          font-size: 15px;
        }
      }

      .btn {
        line-height: 1;

        .btn-item {
          &.active {
            background-color: #fff;
            color: #25c46e;
            font-weight: bold;
          }
        }
      }
    }

    .operational-data__content {
      .operational-data_item {
        border-right: 1px solid #e7e7e7;

        &:last-child {
          border-right: none;
        }

        .title {
          line-height: 1;
        }

        .num {
          line-height: 1;
        }

        .increase {
          line-height: 1;

          .icon-rate {
            width: 11px;
            height: 14px;
          }
        }
      }
    }
  }

  .warning-data {
    .warning-data__header {
      .title {
        line-height: 1;
      }

      .date {
        line-height: 1;

        .el-icon {
          color: #000000;
          font-size: 15px;
        }
      }

      .btn {
        line-height: 1;

        .btn-item {
          &.active {
            background-color: #fff;
            color: #25c46e;
            font-weight: bold;
          }
        }
      }
    }

    .warning-data__content {
      .el-table {
        ::v-deep {
          .el-table__header-wrapper {
            .el-table__header {
              tr {
                background-color: #f0f0f0 !important;
                cursor: pointer;

                th.el-table__cell {
                  background-color: #f0f0f0 !important;
                  color: #222222;
                  font-size: 15px;
                  font-weight: 600;

                  &:first-child {
                    border-radius: 4px 0 0 4px !important;
                  }

                  &:last-child {
                    border-radius: 0 4px 4px 0 !important;
                  }
                }
              }
            }
          }

          .el-table__body-wrapper {
            .el-table__body {
              .el-table__row {
                .el-table__cell {
                  color: #222222;
                  font-size: 15px;
                }
              }
            }
          }
        }
      }

      .el-pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        ::v-deep {
          .el-pagination__total {
            line-height: 1;
            color: #222222;
            font-size: 14px;
          }
          .el-pagination__sizes {
            margin-left: 12px;

            .el-select {
              width: 110px;

              .el-select__wrapper {
                height: 28px;
                min-height: unset;

                .el-select__placeholder {
                  line-height: 1;
                  color: #222222;
                  font-size: 14px;
                }
              }
            }
          }
          .el-pagination__jump {
            margin-left: 12px;

            .el-input__inner {
              height: 26px;
              line-height: 1;
              color: #222222;
              font-size: 14px;
            }

            .el-pagination__goto {
              margin-right: 4px;
              line-height: 1;
              color: #222222;
              font-size: 14px;
            }

            .el-pagination__classifier {
              margin-left: 4px;
              line-height: 1;
              color: #222222;
              font-size: 14px;
            }
          }

          .btn-prev {
            width: 24px;
            min-width: unset;
            height: 24px;
            line-height: 1;
            margin-left: 12px;
            padding: 0;
          }

          .btn-next {
            width: 24px;
            min-width: unset;
            height: 24px;
            line-height: 1;
            padding: 0;
          }

          .el-pager {
            li {
              width: 24px;
              min-width: unset;
              height: 24px;
              line-height: 1;
              padding: 0;
              color: #222222;
              font-size: 14px;
            }
            li.is-active,
            li:hover {
              color: #00b955 !important;
            }
          }
        }
      }
    }
  }
}
</style>
