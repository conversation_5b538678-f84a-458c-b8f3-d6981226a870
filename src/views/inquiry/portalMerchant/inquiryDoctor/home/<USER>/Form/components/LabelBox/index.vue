<script setup lang="ts">
interface Props {
  required?: boolean
  desc?: string
}

withDefaults(defineProps<Props>(), {
  required: false,
  desc: ''
})
</script>
<template>
  <div class="label-box h-box">
    <span v-if="required" class="required">*</span>
    <p><slot></slot></p>
    <span v-show="desc">{{desc}}</span>
  </div>
</template>

<style scoped lang="scss">
.label-box {
  line-height: 32px;
  height: 32px;
  .required {
    color: #ff0000;
  }
  p {
    font-face: PingFangSC;
    font-weight: 400;
    line-height: 16px;
    color: #222222;
  }
}
</style>
