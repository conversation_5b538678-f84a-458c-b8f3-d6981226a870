<template>
  <div class="header">
    <div class="left-header"></div>
    <div class="right-header">
      <div class="flex justify-end items-center">
        <!-- 二维码 -->
        <el-dropdown
          trigger="click"
          :hide-on-click="false"
          class="app-dropdown mr-10px"
          @visible-change="onChanegeAppDrowdown"
        >
          <div class="el-dropdown-link code-container flex justify-center items-center">
            <img src="@/assets/手机.png" />
            <span
              :class="{ 'is-show-code': isShowApp }"
              style="
                text-align: justify; /* 使文本两端对齐 */
                overflow-wrap: break-word; /* 强制文本换行 */
                word-wrap: break-word;
                display: inline-block;
              "
              >App下载</span
            >
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="code" class="header-code-menu">
                <div class="header-qr-code">
                  <div class="img-container">
                    <img :src="downUrl" />
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 账号管理 -->
        <el-dropdown trigger="click" @visible-change="showDrowdown" @command="handleCommand">
          <span class="el-dropdown-link pr-20px">
            <img :src="avatarUrl || defaultAvatar" style="width: 28px; height: 28px" />
            <span class="account-span ml-5px mr-5px" :title="userInfo.account">{{
              userInfo.account
            }}</span>
            <img :src="dropdownStatus ? ArrowTop : ArrowBottom" />
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="个人中心">
                <img src="@/assets/header/account-manage.png" />
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="修改指纹">
                <img src="@/assets/header/fingerprint.png" />
                修改指纹
              </el-dropdown-item>
              <el-dropdown-item command="指纹审方">
                <div class="w-100% flex justify-center"
                  ><el-switch v-model="isValidFingerSwitch" :before-change="onBeforeValidFingerSwitchChange"
                /></div>
              </el-dropdown-item>
              <el-dropdown-item command="退出登录">
                <img src="@/assets/header/logout.png" />
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { isSystemTenant } from '@/utils/auth'
import * as StoreApi from '@/api/system/store'
import { useCache } from '@/hooks/web/useCache'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { ElMessage } from 'element-plus'

import defaultAvatar from '@/assets/header/default-avatar.png' // 导入本地图片
import ArrowTop from '@/assets/header/arrow-top.png'
import ArrowBottom from '@/assets/header/arrow-bottom.png'

const message = useMessage() // 消息弹窗
const { wsCache } = useCache()
const router = useRouter()
const userStore = useUserStore()
const tagsViewStore = useTagsViewStore()

const avatarUrl = ref('')
const userInfo = ref<any>({})

const isShowApp = ref(false) // App下载否展示
let qrCodeUrl = ref('')
let downUrl = ref('')

const dropdownStatus = ref(false)

const isValidFingerSwitch = ref(true) // 默认开启

onMounted(async () => {
  avatarUrl.value = userStore.getUser.avatar
  userInfo.value.account = userStore.getUser.nickname
  if (!isSystemTenant()) {
    let Arr = await StoreApi.getInquiryQrCodeList()
    qrCodeUrl.value = Arr[0].qrCodeUrl
    downUrl.value = Arr[1].qrCodeUrl
  }
})

function onChanegeAppDrowdown(command: boolean) {
  isShowApp.value = command
}

function showDrowdown(command: boolean) {
  dropdownStatus.value = command
}

// 个人中心
const handleNg2UserCenter = () => {
  router.push({ path: '/user/profile' })
}
// 修改指纹
const handleUpdateFinger = () => {
  ElMessage('功能开发中...')
}
// 是否指纹审方
const onBeforeValidFingerSwitchChange = (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    message
      .confirm(`是否 [ ${isValidFingerSwitch.value ? '关闭' : '打开'} ] 指纹审方?`)
      .then(() => {
        // TODO提交更改
        return resolve(true)
      })
      .catch(() => {
        reject(new Error())
      })
  })
}
// 退出登录
const handleLogout = async () => {
  await useUserStore().loginOut()
  tagsViewStore.delAllViews()
  router.push({
    path: '/login'
  })
}
function handleCommand(command: string) {
  switch (command) {
    case '个人中心':
      handleNg2UserCenter()
      break
    case '修改指纹':
      handleUpdateFinger()
      break
    case '指纹审方':
      break
    case '退出登录':
      handleLogout()
      break
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
.header {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  font-size: 14px;
  transition:
    width 0.4s,
    left 0.4s;
  background: #f7f8f9;

  .left-header {
  }
  .right-header {
    flex-shrink: 0;
  }

  .el-select :deep(.el-input input) {
    width: 300px;
    height: 36px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 20px;
  }

  > span {
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 17px;
    margin-left: 20px;
  }

  .el-dropdown {
    cursor: pointer;

    .el-dropdown-link img {
      width: 10px;
      height: 6px;
      margin-left: 8px;
      vertical-align: middle;
    }

    > span {
      font-size: 14px;
      font-family: Helvetica;
      text-align: left;
      color: #333333;
      line-height: 17px;
    }

    .el-dropdown-link {
      &.code-container {
        & > img {
          width: 16px;
          height: 16px;
          vertical-align: middle;
          margin-right: 6px;
        }
        & > span {
          color: #333333;
          vertical-align: middle;
          &.is-show-code {
            color: #00b955;
          }
        }
      }

      .account-span {
        display: inline-block;
        max-width: 90px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        vertical-align: middle;
      }
    }
  }
}

.el-dropdown-menu {
  img {
    vertical-align: middle;
    width: 16px;
    height: 16px;
  }
}

.el-dropdown-menu__item.header-code-menu {
  &:hover {
    background-color: #fff !important;
  }
  padding: 0 10px;
  .header-qr-code {
    .imgs {
      display: flex;
      .code-item {
        width: 268px;
        margin-right: 10px;

        .code-img-container {
          & > img {
            width: 100%;
            height: 100%;
          }
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .code-btns {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
      & > div {
        color: #333333;
        width: 130px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        border-radius: 5px;
        cursor: pointer;
        &.more-code {
          border: 1px solid #eeeeee;
          margin-right: 8px;
        }
        &.all-down {
          color: #ffffff;
          background: #00b955;
          border-radius: 4px;
        }
      }
    }
    .img-container {
      width: 270px;
      & > img {
        width: 100%;
        height: auto;
      }
    }
    .btn-down {
      color: #ffffff;
      height: 36px;
      line-height: 36px;
      text-align: center;
      cursor: pointer;
      margin-top: 10px;
      background-color: #00b955;
      border-radius: 4px;
    }
  }
}
</style>
