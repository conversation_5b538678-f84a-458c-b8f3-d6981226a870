<template>

  <el-select
    style="width: 240px"
    v-model="templateId"
    filterable
    remote
    clearable
    reserve-keyword
    placeholder="请输入处方笺模版名称"
    @change="choosed"
    :remote-method="searchTemplateList">
    <el-option
      v-for="item in templateList"
      :key="item.id"
      :label="item.name"
      :value="item.id"/>
  </el-select>


</template>
<script lang="ts" setup>
import {InquiryPrescriptionTemplateApi, InquiryPrescriptionTemplateVO} from "@/api/inquiry/prescriptiontemplate";

const templateList = ref<InquiryPrescriptionTemplateVO[]>([]) // 处方笺模版id
const templateId = ref()

defineOptions({ name: 'PreTemplateSelect' })

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  disable: false,
  name:undefined,
  id:undefined
})

const props = defineProps({
  id: {
    type: undefined
  },
  multiple: {
    type: Boolean
  }
});

onMounted(() => {
  if (props.id) {
    templateId.value = props.id
    searchTemplate(templateId.value)
  }
})

watch(
  () => props.id,
  (val: any) => {
    templateId.value = val
    searchTemplate(templateId.value)
  },
  {immediate:false}
)

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const choosed = async () =>{
  emit('success',templateId.value)
}

const searchTemplateList = async (query:any) =>{
  queryParams.name = query
  const rs = await InquiryPrescriptionTemplateApi.getInquiryPrescriptionTemplatePage(queryParams)
  templateList.value = rs.list
}

const searchTemplate = async (query:any) =>{
  queryParams.id = query
  const rs = await InquiryPrescriptionTemplateApi.getInquiryPrescriptionTemplatePage(queryParams)
  templateList.value = rs.list
}

</script>
<style lang="scss" scoped>

</style>
