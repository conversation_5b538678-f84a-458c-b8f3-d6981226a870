<template>
  <ContentWrap>
    <div class="action-wrap p-6px flex justify-between items-stretch">
      <!-- 新建商品 -->
      <div class="action-left flex flex-col">
        <span class="action-head leading-none">新建商品</span>
        <div class="action-content mt-16px flex justify-start items-center">
          <button
            class="action-button"
            @click="handleNg2RouterLink('/goods/product/product-add-single')"
            v-hasPermi="['saas:product:info:create']"
          >
            <img class="action-button-image" src="@/assets/inquiry/product/icon-add.png" />
            <div class="action-button-text">
              <span class="strong">单个新增</span>
              <span>适合创建单个商品</span>
            </div>
          </button>
          <button class="action-button" @click="handleNg2RouterLink('')" v-hasPermi="['saas:product:info:create']">
            <img class="action-button-image" src="@/assets/inquiry/product/icon-add-plus.png" />
            <div class="action-button-text">
              <span class="strong">批量新增</span>
              <span>适合创建大量商品</span>
            </div>
          </button>
          <button
            class="action-button"
            @click="handleNg2RouterLink('/goods/product/productStoreForm')"
            v-if="tenantType == 3"
          >
            <img class="action-button-image" src="@/assets/inquiry/product/icon-store.png" />
            <div class="action-button-text">
              <span class="strong">门店商品(5)</span>
              <span>门店创建的商品</span>
            </div>
          </button>
        </div>
      </div>
      <!-- 其他操作 -->
      <div class="action-right flex flex-col">
        <span class="action-head leading-none">其他操作</span>
        <div class="link-content mt-16px flex flex-wrap justify-start items-start">
          <template v-for="linkItem in computedQuickLinks.hotLink" :key="linkItem.name">
            <div class="link-action-box">
              <span class="action-link" v-if="!linkItem.tenantType || linkItem.tenantType.includes(tenantType)"  @click="handleNg2RouterLink(linkItem.url)">{{
                linkItem.name
              }}</span>
            </div>
          </template>
          <el-dropdown
            trigger="hover"
            class="link-action-box"
            popper-class="link-action-box-popper"
          >
            <div class="action-link"
              >全部<el-icon>
                <DArrowRight size="14" />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <template v-for="category in computedQuickLinks.categoryLink" :key="category.name">
                  <li class="el-dropdown-item-head-title">{{ category.name }}</li>
                  <template v-for="linkItem in category.list" :key="linkItem.name">
                    <el-dropdown-item @click="handleNg2RouterLink(linkItem.url)">
                      <span>{{ linkItem.name }}</span>
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </ContentWrap>

  <ContentWrap>
    <el-form
      class="query-form"
      ref="refQueryForm"
      :model="queryForm"
      :inline="true"
      label-width="110px"
      :style="{ overflow: 'hidden', height: isExpandQueryForm ? 'auto' : '44px' }"
    >
      <!-- 商品信息 -->
      <el-form-item label="商品信息" prop="mixedQuery">
        <!-- (110+280)*2+12=792 -->
        <el-input
          v-model.trim="queryForm.mixedQuery"
          :placeholder="computedPrefInputConfig.placeholder"
          :maxlength="computedPrefInputConfig.maxlength"
          clearable
          class="!w-682px"
        >
          <template #prepend>
            <el-select
              v-model="queryForm.mixedQueryType"
              placeholder="关键字"
              clearable
              class="!w-140px"
            >
              <el-option label="多个条形码" value="1" />
              <el-option label="多个商品编码" value="2" />
              <el-option label="多个标准库ID" value="3" />
              <el-option label="单个通用名称" value="4" />
              <el-option label="单个品牌名称" value="5" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <!-- 商品分类 -->
      <el-form-item label="商品分类" prop="productCategory">
        <el-select
          v-model="queryForm.productCategory"
          placeholder="请选择商品分类"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_PRODUCT_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 所属范围 -->
      <el-form-item label="所属范围" prop="businessScope">
        <el-select
          v-model="queryForm.businessScope"
          placeholder="请选择所属范围"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_BUSINESS_SCOPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 处方分类 -->
      <el-form-item label="处方分类" prop="presCategory">
        <el-select
          v-model="queryForm.presCategory"
          placeholder="请选择处方分类"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_PRES_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 含特殊药品复方制剂 -->
      <el-form-item label="含特殊药品复方制剂" prop="hasSpecialDrugCompound">
        <el-select
          v-model="queryForm.hasSpecialDrugCompound"
          placeholder="请选择含特殊药品复方制剂"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 是否特价 -->
      <el-form-item label="是否特价" prop="specialPrice">
        <el-select
          v-model="queryForm.specialPrice"
          placeholder="请选择是否特价"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 积分商品 -->
      <el-form-item label="积分商品" prop="integral">
        <el-select
          v-model="queryForm.integral"
          placeholder="请选择是否积分商品"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 标签 -->
      <el-form-item label="标签" prop="remark">
        <el-select v-model="queryForm.remark" placeholder="请选择标签" clearable class="!w-280px">
          <el-option label="A类" value="1" />
          <el-option label="B类" value="2" />
          <el-option label="C类" value="3" />
          <el-option label="D类" value="4" />
          <el-option label="E类" value="5" />
        </el-select>
      </el-form-item>

      <!-- 医保匹配状态 -->
      <el-form-item label="医保匹配状态" prop="medicareMatchStatus">
        <el-select
          v-model="queryForm.medicareMatchStatus"
          placeholder="请选择医保匹配状态"
          clearable
          class="!w-280px"
        >
          <el-option label="已匹配" value="1" />
          <el-option label="未匹配" value="0" />
        </el-select>
      </el-form-item>

      <!-- 医保项目编码 -->
      <el-form-item label="医保项目编码" prop="medicareProjectCode">
        <el-input
          v-model="queryForm.medicareProjectCode"
          placeholder="请输入医保项目编码"
          clearable
          class="!w-280px"
          maxlength="50"
        />
      </el-form-item>

      <!-- 生产厂家 -->
      <el-form-item label="生产厂家" prop="manufacturer">
        <el-input
          v-model="queryForm.manufacturer"
          placeholder="请输入生产厂家"
          clearable
          class="!w-280px"
          maxlength="50"
        />
      </el-form-item>

      <!-- 是否有标识码 -->
      <el-form-item label="是否有标识码" prop="drugIdentCode">
        <el-select
          v-model="queryForm.drugIdentCode"
          placeholder="请选择是否有标识码"
          clearable
          class="!w-280px"
        >
          <el-option label="有标识码" value="1" />
          <el-option label="无标识码" value="0" />
        </el-select>
      </el-form-item>

      <!-- 存储条件 -->
      <el-form-item label="存储条件" prop="storageWay">
        <el-select
          v-model="queryForm.storageWay"
          placeholder="请选择存储条件"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_STORAGE_WAY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 资质是否过期 -->
      <el-form-item label="资质是否过期" prop="productValidity">
        <el-select
          v-model="queryForm.productValidity"
          placeholder="请选择资质是否过期"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 停售状态 -->
      <el-form-item label="停售状态" prop="stopSale">
        <el-select
          v-model="queryForm.stopSale"
          placeholder="请选择停售状态"
          clearable
          class="!w-280px"
        >
          <el-option label="未停售" value="0" />
          <el-option label="已停售" value="1" />
        </el-select>
      </el-form-item>

      <!-- 禁采状态 -->
      <el-form-item label="禁采状态" prop="purchaseDisabled">
        <el-select
          v-model="queryForm.purchaseDisabled"
          placeholder="请选择禁采状态"
          clearable
          class="!w-280px"
        >
          <el-option label="未禁采" value="0" />
          <el-option label="总部禁采" value="1" />
          <el-option label="总部&门店禁采" value="2" />
        </el-select>
      </el-form-item>

      <!-- 审批状态 -->
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-select
          v-model="queryForm.approvalStatus"
          placeholder="请选择审批状态"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_BPM_APPROVAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 医保限制 -->
      <el-form-item label="医保限制" prop="medicareLimit">
        <el-select
          v-model="queryForm.medicareLimit"
          placeholder="请选择医保限制"
          clearable
          class="!w-280px"
        >
          <el-option label="有限制" value="1" />
          <el-option label="无限制" value="0" />
        </el-select>
      </el-form-item>

      <!-- 创建人 -->
      <el-form-item label="创建人" prop="createBy">
        <el-input
          v-model="queryForm.createBy"
          placeholder="请输入创建人"
          clearable
          class="!w-280px"
          maxlength="50"
        />
      </el-form-item>

      <!-- 创建时间 -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryForm.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-280px"
        />
      </el-form-item>
    </el-form>

    <div class="action-form w-100% pl-110px flex justify-start items-stretch">
      <el-button type="primary" @click="handleQuery">查询</el-button>
      <el-button type="default" @click="resetQuery">重置</el-button>
      <div
        class="action-from-expand ml-12px cursor-pointer select-none flex justify-start items-center"
        @click="isExpandQueryForm = !isExpandQueryForm"
      >
        <span class="action-txt">{{ isExpandQueryForm ? '收起' : '展开' }}</span>
        <el-icon size="14"><component :is="isExpandQueryForm ? ArrowUp : ArrowDown" /></el-icon>
      </div>
    </div>
  </ContentWrap>

  <ContentWrap>
    <!-- 列表按钮 -->
    <div class="table-header-action w-100% mb-10px flex justify-between items-center">
      <div class="header-left">&nbsp;</div>
      <div class="header-right">
        <FilterColumn :allColumns="columns" @column-filter="handleFilterConfirm" />
        <DownloadExport :codeArr="columns" :title="title" :perm="perm"/>
        <el-dropdown
          class="el-dropdown-link ml-12px"
          trigger="hover"
          @visible-change="onTableMoreActionVisibleChange"
          @command="onTableMoreActionCommand"
        >
          <el-button type="default">
            <div class="dropdown-link-text flex justify-center items-center">
              更多操作<el-icon size="14">
                <component :is="isExpandTableMoreAction ? ArrowUp : ArrowDown" />
              </el-icon>
            </div>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="批量停售">批量停售</el-dropdown-item>
              <el-dropdown-item command="批量解停">批量解停</el-dropdown-item>
              <el-dropdown-item command="批量删除">批量删除</el-dropdown-item>
              <el-dropdown-item command="批量禁采">批量禁采</el-dropdown-item>
              <el-dropdown-item command="批量取消禁采">批量取消禁采</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <!-- 列表 -->
    <BETable
      class="table-center"
      :table-data="queryTable.list"
      :columns="columnsData"
      :showIndex="true"
      :btnClass="false"
      :loading="queryTable.loading"
      @action="handleTableAction"
      :keyLocalStorage="'ProductList'"
      @column-order-change="handleColumnOrderChange"
    >
      <!-- 自定义列内容插槽 -->
      <template #col-mixedQuery="{ row }">
        <div class="product-info-container">
          <div style="position: relative">
            <div
              v-if="['处方药', '甲类OTC', '乙类OTC'].includes(row.presCategory)"
              :class="['prescription-category', getPrescriptionCategoryClass(row.presCategory)]"
            >
              {{ row.presCategory }}
            </div>
            <el-image
              product-info-container
              :src="
                row.images?.[0] ||
                'https://oss-ec-test.ybm100.com//ybm/product/min/defaultPhoto.jpg'
              "
              class="product-image"
              fit="contain"
            />
          </div>
          <div style="flex: 1" class="item-content common-info">
            <div class="item-hover product-name"
              ><span v-show="row.brandName">【{{ row.brandName }}】</span>{{ row.commonName }}
              <span v-show="row.spec">[{{ row.spec }}]</span> {{ row.unit }}
              <el-icon class="item-cxt-copy" @click="copyBtn('1', row)" size="14"
                ><CopyDocument /></el-icon
            ></div>
            <div class="item-hover"
              >条形码：{{ row.barcode
              }}<el-icon
                size="14"
                v-show="row.barcode"
                class="item-cxt-copy"
                @click="copyBtn('2', row)"
                ><CopyDocument /></el-icon
            ></div>
            <div class="item-hover"
              >批准文号：{{ row.approvalNumber }}
              <el-icon
                size="14"
                v-show="row.approvalNumber"
                class="item-cxt-copy"
                @click="copyBtn('3', row)"
                ><CopyDocument /></el-icon
            ></div>
            <div class="item-hover"
              >生产厂家：{{ row.manufacturer }}
              <el-icon
                size="14"
                v-show="row.manufacturer"
                class="item-cxt-copy"
                @click="copyBtn('4', row)"
                ><CopyDocument /></el-icon
            ></div>
            <div class="item-hover"
              >标准库ID：{{ row.stdlibId }}
              <el-icon
                size="14"
                v-show="row.stdlibId"
                class="item-cxt-copy"
                @click="copyBtn('5', row)"
                ><CopyDocument /></el-icon
            ></div>
            <!-- <div class="text-gray-500">SKU编码：{{ row.midStdlibId }}</div>
              <div class="text-gray-500">原商品编码：{{ row.midStdlibId }}</div> -->
          </div>
        </div>
      </template>
      <template #col-useInfo="{ row }">
        <div class="item-content">
          <div class="item-hover"
            >医保项目名称：{{ row.medicareProjectName }}
            <el-icon
              size="14"
              v-show="row.medicareProjectName"
              class="item-cxt-copy"
              @click="copyBtn('6', row)"
              ><CopyDocument /></el-icon
          ></div>
          <div class="item-hover"
            >医保项目编码：{{ row.medicareProjectCode }}
            <el-icon
              size="14"
              v-show="row.medicareProjectCode"
              class="item-cxt-copy"
              @click="copyBtn('7', row)"
              ><CopyDocument /></el-icon
          ></div>
          <div class="item-hover"
            >医保项目等级：{{ row.medicareCatalog }}
            <el-icon
              size="14"
              v-show="row.medicareCatalog"
              class="item-cxt-copy"
              @click="copyBtn('8', row)"
              ><CopyDocument /></el-icon
          ></div>
          <div class="item-hover"
            >最小包装数量: {{ row.unbundledQuantity }}
            <el-icon size="14" class="item-cxt-copy" @click="copyBtn('9', row)"
              ><CopyDocument /></el-icon
          ></div>
        </div>
      </template>
      <template #col-status="{ row }">
        <dict-tag :type="DICT_TYPE.PRODUCT_STATUS" :value="row.status" />
      </template>
      <template #col-businessScope="{ row }">
        <dict-tag :type="DICT_TYPE.PRODUCT_BUSINESS_SCOPE" :value="row.businessScope" />
      </template>
      <template #col-presCategory="{ row }">
        <dict-tag :type="DICT_TYPE.PRODUCT_PRES_CATEGORY" :value="row.presCategory" />
      </template>
      <template #col-retailPrice="{ row }">
        <div class="flex items-center" v-if="editingRow !== row || editingField !== 'retailPrice'"
          ><div class="mr-10px">{{ row.retailPrice }}</div>
          <el-icon
            v-if="row.memberPrice"
            @click="startEdit(row, 'retailPrice')"
            :size="16"
            color="#00b955"
            ><Edit /></el-icon
        ></div>
        <div v-else>
          <el-input-number
            v-model="editValue"
            style="width: 80%"
            :min="0"
            :max="999999.99"
            :precision="2"
            @change="validateRetailPrice(row)"
          />
          <el-icon class="el-input__icon" color="#00b955" @click="saveEdit(row)"><Check /></el-icon>
          <el-icon class="el-input__icon" color="#f56c6c" @click="cancelEdit"><Close /></el-icon>
        </div>
      </template>
      <template #col-memberPrice="{ row }">
        <div class="flex items-center" v-if="editingRow !== row || editingField !== 'memberPrice'"
          ><div class="mr-10px">{{ row.memberPrice }} </div>
          <el-icon
            v-if="row.memberPrice"
            @click="startEdit(row, 'memberPrice')"
            :size="16"
            color="#00b955"
            ><Edit /></el-icon
        ></div>
        <div v-else>
          <el-input-number
            style="width: 80%"
            v-model="editValue"
            :min="0"
            :max="999999.99"
            :precision="2"
            @change="validateMemberPrice(row)"
          />
          <el-icon class="el-input__icon" color="#00b955" @click="saveEdit(row)"><Check /></el-icon>
          <el-icon class="el-input__icon" color="#f56c6c" @click="cancelEdit"><Close /></el-icon>
        </div>
      </template>
      <template #col-dosageForm="{ row }">
        <dict-tag :type="DICT_TYPE.PRODUCT_DOSAGE_FORM" :value="row.dosageForm" />
      </template>
      <template #col-stopSale="{ row }">
        <div>{{ row.productFlag.stopSale ? '已停售' : '未停售' }}</div>
      </template>
      <template #col-purchaseDisabled="{ row }">
        <div>{{ row.purchaseDisabled == 0 ? '未禁采' : row.purchaseDisabled == 1 ? '总部禁采' : '总部&门店禁采' }}</div>
      </template>
      <template #col-storageWay="{ row }">
        <dict-tag :type="DICT_TYPE.PRODUCT_STORAGE_WAY" :value="row.storageWay" />
      </template>
      <template #col-createTime="{ row }">
        <div>{{ formatDate(row.createTime) }}</div>
      </template>
      ></BETable
    >
    <Pagination
      v-model:page="queryTable.pageNo"
      v-model:limit="queryTable.pageSize"
      :total="queryTable.total"
      @pagination="queryDataList"
    />
  </ContentWrap>

  <BanDialogView ref="pickDialogRefs" />
  <PurchaseAudit :apprType="auditType" :title="title" ref="auditRef" />
  <!-- Dialog 组件 -->
  <el-dialog title="选择删除范围" v-model="dialogVisible" width="30%" :before-close="cancelDelete">
    <el-radio-group v-model="deleteType">
      <el-radio :label="1">仅删除商品列表</el-radio>
      <el-radio :label="2">删除全部记录</el-radio>
    </el-radio-group>

    <template #footer>
      <el-button @click="cancelDelete">取消</el-button>
      <el-button type="primary" @click="confirmDelete">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {
  ArrowUp,
  ArrowDown,
  DArrowRight,
  CopyDocument,
  Edit,
  Check,
  Close
} from '@element-plus/icons-vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ProductInfoApi, ProductInfoVO } from '@/api/inquiry/product/product'
import { ProductRecycleApi } from '@/api/inquiry/product/recycle'
import { DownloadExport } from '@/components/DownloadExport'
import { FilterColumn } from '@/components/FilterColumn'
import BanDialogView from './components/BanDialogView.vue'
import { dateFormatter, formatDate } from '@/utils/formatTime'
import PurchaseAudit from '@/views/gspStandard/model/purchaseAudit.vue'
import { isColumns } from '@/utils/is'
import { CACHE_KEY, useCache, deleteUserCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

defineOptions({ name: 'ProductList' })

const route = useRoute()
const router = useRouter()
const message = useMessage()
const columnsData = ref([])
const title = ref('商品信息')
const auditType = ref('')
const auditRef = ref() // 审批弹窗
const tenantType = ref()

// 判断总部&门店&单店
const getIsChainHeadOrSingleStore = computed(() => {
  return true
})
const perm = ref(['saas:product:info:export'])

// 点击跳转
const handleNg2RouterLink = async (url: string) => {
  if (url) {
    router.push({ path: url })
  }
}
const handleColumnOrderChange = (newColumns) => {
  columnsData.value = [...newColumns]
}
// 其他操作
const computedQuickLinks = computed(() => {
  const links = [
    {
      category: 'inventory',
      name: '库存查询',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '报损报溢',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '药品养护',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '货位管理',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '期初库存导入',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '库存拆零',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '追溯码/UDI码补录',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '中药饮片清装斗',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'product',
      name: '批量修改',
      url: '',
      hotLinkSort: -1,
      tenantType:[1,3] //总部单店
    },
    {
      category: 'product',
      name: '拆零规则',
      url: '/goods/product/unbundled-info',
      hotLinkSort: -1,
      tenantType:[1,3] //总部单店
    },
    {
      category: 'product',
      name: '售价调整',
      url: '/goods/product/product-gsp/product-price-adjustment',
      hotLinkSort: -1
    },
    {
      category: 'product',
      name: '商品回收站',
      url: '/goods/product/recycle',
      hotLinkSort: -1
    },
    {
      category: 'product',
      name: '价签打印',
      url: '',
      hotLinkSort: -1
    }
  ]
 
   // 热链排序逻辑保持不变
   const hotLinksArr = ['库存查询', '批量修改', '拆零规则', '报损报溢', '货位管理']
  links.forEach((link) => {
    link.hotLinkSort = -1
    if (hotLinksArr.includes(link.name)) {
      link.hotLinkSort = hotLinksArr.indexOf(link.name)
    }
  })

  // 租户类型过滤
  const filteredLinks = links.filter(link => 
    !link.tenantType || link.tenantType.includes(tenantType.value)
  )

  // 获取热链并排序
  const hotLink = filteredLinks
    .filter(link => link.hotLinkSort !== -1)
    .sort((a, b) => a.hotLinkSort - b.hotLinkSort)

  // 分类链接
  const inventoryLink = filteredLinks.filter(link => link.category === 'inventory')
  const productLink = filteredLinks.filter(link => link.category === 'product')

  return {
    hotLink, // 已排序的热链
    categoryLink: [
      {
        name: '库存',
        list: inventoryLink
      },
      {
        name: '商品',
        list: productLink
      }
    ]
  }
})

// 查询表单
const queryForm = reactive({
  mixedQueryType: undefined as string | undefined, // 商品信息类型
  mixedQuery: undefined, // 商品信息
  productCategory: undefined, // 商品分类
  businessScope: undefined, // 所属范围
  presCategory: undefined, // 处方分类
  hasSpecialDrugCompound: undefined as string | undefined, // 含特殊药品复方制剂
  specialPrice: undefined as string | undefined, // 是否特价
  integral: undefined as string | undefined, // 积分商品
  remark: undefined, // 标签
  medicareMatchStatus: undefined as string | undefined, // 医保匹配状态
  medicareProjectCode: undefined, // 医保项目编码
  manufacturer: undefined, // 生产厂家
  drugIdentCode: undefined as string | undefined, // 是否有标识码
  storageWay: undefined, // 存储条件
  productValidity: undefined as string | undefined, // 资质是否过期
  stopSale: undefined as string | undefined, // 停售状态
  purchaseDisabled: undefined as string | undefined, // 禁采状态
  approvalStatus: undefined, // 审批状态
  medicareLimit: undefined as string | undefined, // 医保限制
  createBy: undefined, // 创建人
  createTime: [] // 创建时间
})
// 查询table
const queryTable = reactive({
  pageNo: 1,
  pageSize: 10,
  loading: false,
  total: 0,
  list: [] as ProductInfoVO[]
})
//表头
const columns = ref([
  { prop: 'mixedQuery', label: '商品信息', slot: true, mWidth: '450', align: 'left' }, // slot #col+prop标记该列使用自定义插槽},
  { prop: 'useInfo', label: '医保信息', slot: true, mWidth: '300', align: 'left' },
  { prop: 'firstCategory', label: '商品分类', slot: false, mWidth: '128', align: 'left' },
  { prop: 'businessScope', label: '所属范围', slot: true, mWidth: '108', align: 'left' },
  { prop: 'presCategory', label: '处方分类', slot: true, mWidth: '96', align: 'left' },
  { prop: 'retailPrice', label: '零售价(元)', slot: true, mWidth: '200', align: 'left' },
  { prop: 'memberPrice', label: '会员价(元)', slot: true, mWidth: '200', align: 'left' },
  { prop: 'status', label: '审批状态', slot: true, mWidth: '128', align: 'left' },
  { prop: 'dosageForm', label: '剂型', slot: true, mWidth: '128', align: 'left' },
  { prop: 'productValidity', label: '有效期', slot: false, mWidth: '128', align: 'left' },
  { prop: 'origin', label: '产地', slot: false, mWidth: '128', align: 'left' },
  {
    prop: 'approvalValidityPeriod',
    label: '批准文号有效期',
    slot: false,
    mWidth: '128',
    align: 'left'
  },
  { prop: 'remark', label: '标签', slot: false, mWidth: '128', align: 'left' },
  { prop: 'specialPrice', label: '特价商品', slot: false, mWidth: '128', align: 'left' },
  { prop: 'integral', label: '积分商品', slot: false, mWidth: '128', align: 'left' },
  { prop: 'storageWay', label: '储存条件', slot: true, mWidth: '128', align: 'left' },
  // { prop: '', label: '养护类型', slot: false, mWidth: '128', align: 'left' },
  {
    prop: 'hasSpecialDrugCompound',
    label: '含特殊药品复方制剂',
    slot: false,
    mWidth: '128',
    align: 'left'
  },
  // { prop: '', label: '资质过期', slot: false, mWidth: '128', align: 'left' },
  { prop: 'inputTaxRate', label: '进项税率', slot: false, mWidth: '128', align: 'left' },
  { prop: 'outputTaxRate', label: '销项税率', slot: false, mWidth: '128', align: 'left' },
  { prop: 'createBy', label: '创建人', slot: false, mWidth: '128', align: 'left' },
  { prop: 'createTime', label: '创建时间', slot: true, mWidth: '180', align: 'left' },
  { prop: 'drugIdentCode', label: '药品标识码', slot: false, mWidth: '128', align: 'left' },
  // { prop: '', label: '医保匹配状态', slot: false, mWidth: '128', align: 'left' },
  // { prop: '', label: '医保限制', slot: false, mWidth: '128', align: 'left' },
  { prop: 'stopSale', label: '停售状态', slot: true, mWidth: '128', align: 'left' },
  { prop: 'purchaseDisabled', label: '禁采状态', slot: true, mWidth: '128', align: 'left' },
  {
    prop: 'actions',
    label: '操作',
    width: 180,
    fixed: 'right',
    type: 'actions', // 新增类型标识
    actions: [
      // 操作按钮配置
      // v-hasPermi="['saas:product:info:update']"
      { label: '编辑', method: 'update', show: (row) => [1, 3].includes(tenantType.value),perm:['saas:product:info:update']},
      { label: '禁采', method: 'pick', show: (row) =>  (row.purchaseDisabled == 0) && [1, 3].includes(tenantType.value)  },
      { label: '取消禁采', method: 'cancelPick', show: (row) =>  (row.purchaseDisabled == 1 || row.purchaseDisabled == 2) && [1, 3].includes(tenantType.value)  },
      { label: '停售', method: 'stop', show: (row) =>  !row.productFlag.stopSale },
      { label: '解停', method: 'stop', show: (row) =>  row.productFlag.stopSale },
      { label: '删除', method: 'delete', show: (row) => [1, 3].includes(tenantType.value),perm:['saas:product:info:delete'] }
    ]
  }
  //{ prop: 'operate', label: '操作',slot: true,width:"180",fixed:"right"},
])

// 动态获取处方分类的样式类
const getPrescriptionCategoryClass = (category: string) => {
  switch (category) {
    case '处方药':
      return 'bg-red'
    case '甲类OTC':
    case '乙类OTC':
      return 'bg-green'
    default:
      return ''
  }
}

//复制
const copyBtn = async (type: string, row: any) => {
  let drugName = ''
  switch (type) {
    case '1':
      drugName = `【${row.brandName}】${row.commonName}[${row.spec}]${row.unit}`
      break
    case '2':
      drugName = row.barcode
      break
    case '3':
      drugName = row.approvalNumber
      break
    case '4':
      drugName = row.manufacturer
      break
    case '5':
      drugName = row.stdlibId
      break
  }
  try {
    await navigator.clipboard.writeText(drugName)
    message.success('复制成功')
    return true
  } catch (err) {
    message.error('复制失败')
    return false
  }
}

// 商品信息输入框
const computedPrefInputConfig = computed(() => {
  let placeholder = ''
  let maxlength = 0
  switch (queryForm.mixedQueryType) {
    case '1':
      placeholder = '最多可录入50个条形码，以英文逗号隔开，最多700个字符'
      maxlength = 700
      break
    case '2':
      placeholder = '最多可录入50个商品编码，以英文逗号隔开，最多700个字符'
      maxlength = 700
      break
    case '3':
      placeholder = '最多可录入50个标准库ID，以英文逗号隔开，最多700个字符'
      maxlength = 700
      break
    case '4':
      placeholder = '仅可录入1个通用名称，支持模糊搜索，最大长度50'
      maxlength = 50
      break
    case '5':
      placeholder = '仅可录入1个品牌名称，支持模糊搜索，最大长度50'
      maxlength = 50
      break
    default:
      placeholder = '请输入商品编码/通用名称/品牌名称/条形码/助记码/批准文号/生产厂家/标准库ID'
      maxlength = 50
      break
  }
  return { placeholder, maxlength }
})

// 查询
const handleQuery = () => {
  queryTable.pageNo = 1
  queryDataList()
}
// 重置
const refQueryForm = ref<any>(null)
const resetQuery = () => {
  refQueryForm.value?.resetFields()
  queryForm.mixedQueryType = undefined
  handleQuery()
}
// 展开&收起
const isExpandQueryForm = ref(false) // 展开查询表单

//操作
const handleTableAction = async ({ method, row }) => {
  switch (method) {
    case 'update':
      //编辑
      auditType.value = 'edit'
      auditRef.value.openModal(row,'edit')
      break
    case 'pick':
      //禁采
      pickDialogRefs.value.open(method, row)
      break
      case 'cancelPick':
      //取消禁采
      row.purchaseDisabled = '0'
      await ProductInfoApi.updateProhibition({ids:[row.id],purchaseDisabled:'0'})
      message.success('取消采禁成功')
      break
    case 'stop':
      //停售
      row.productFlag.stopSale = !(row.productFlag.stopSale)
      await ProductInfoApi.updateProductInfo({...row})
      break
    case 'delete':
      //停售
      handleDelete(row)
      break
  }
}
const handleFilterConfirm = (data: any) => {
  columnsData.value = data
}
// 列表查询
const queryDataList = async () => {
  try {
    queryTable.loading = true
    const respData = await ProductInfoApi.getProductInfoPage({
      pageNo: queryTable.pageNo,
      pageSize: queryTable.pageSize,
      ...queryForm
    })
    queryTable.total = respData.total || 0
    if (Array.isArray(respData.list)) {
      queryTable.list = respData.list.map((item) => {
        return {
          ...item,
          retailPrice: item.useInfo?.retailPrice,
          memberPrice: item.useInfo?.memberPrice
        }
      })
    }
  } catch (err) {
    console.log(err)
  } finally {
    queryTable.loading = false
  }
}
/** 价格修改模块 */
const editingRow = ref(null)
const editingField = ref('')
const editValue = ref(0)
const originalValue = ref(0)
//开始编辑
const startEdit = (row, field) => {
  editingRow.value = row
  editingField.value = field
  editValue.value = parseFloat(row[field])
  originalValue.value = parseFloat(row[field])
}
//取消编辑
const cancelEdit = () => {
  editingRow.value = null
  editingField.value = ''
}
//保存
const saveEdit = (row) => {
  if (editingField.value === 'retailPrice') {
    row.retailPrice = editValue.value.toString()
  } else {
    row.memberPrice = editValue.value.toString()
  }
  message.success('修改成功')
  cancelEdit()
}

const validateRetailPrice = (row) => {
  if (editValue.value < parseFloat(row.memberPrice)) {
    message.error('零售价不得小于会员价！')
    editValue.value = originalValue.value
  }
}

const validateMemberPrice = (row) => {
  if (editValue.value > parseFloat(row.retailPrice)) {
    message.error('会员价不得大于零售价！')
    editValue.value = originalValue.value
  }
}
// table更多操作
const isExpandTableMoreAction = ref<boolean>(false) // 展开更多操作
const onTableMoreActionVisibleChange = (visible: boolean) => {
  isExpandTableMoreAction.value = visible
}
const onTableMoreActionCommand = (command: string | number | object) => {
  console.log(command)
  // 请先选择一个商品
}
/** 操作按钮 */
const pickDialogRefs = ref()

/** 删除按钮操作 */
const deleteType = ref<number>(1)
const dialogVisible = ref<boolean>(false)
const deleteRowId = ref<number>()
const deleteRow = ref<ProductInfoVO>({} as ProductInfoVO)

const handleDelete = async (row: ProductInfoVO): Promise<void> => {
  deleteRow.value = row
  try {
    // 1. 校验回收站数据量
    const data = await ProductRecycleApi.getProductPage({ pageNo: 1, pageSize: 1 })
    const recycleCount: number = data.total
    if (recycleCount >= 1000) {
      const confirmResult = await ElMessageBox.confirm(
        '您的商品回收站存在1000条历史删除数据，请前往商品回收站删除历史数据后，再操作',
        '提示',
        {
          confirmButtonText: '去删除',
          cancelButtonText: '取消'
        }
      )
      if (confirmResult === 'confirm') {
        // 跳转到回收站页面
        router.push({ name: 'ProductRecycleInfo' })
      }
      return
    }

    // 2. 校验是否有库存
    const hasStock = await ProductInfoApi.checkProductStock(row.id)

    // 3. 有库存时的二次确认
    if (hasStock.data) {
      const confirmResult = await ElMessageBox.confirm('该商品存在库存，是否继续删除?', '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
      if (confirmResult !== 'confirm') {
        return
      }
    }

    // 4. 选择删除范围
    deleteRowId.value = row.id
    dialogVisible.value = true
  } catch (error) {
    console.error('Delete failed:', error)
    cancelDelete()
  }
}

/** 取消删除操作 */
const cancelDelete = () => {
  dialogVisible.value = false
}
/** 确认删除操作 */
const confirmDelete = async () => {
  try {
    // 发起删除
    await ProductInfoApi.deleteProduct({
      idList: [deleteRowId.value],
      deleteType: deleteType.value
    })
    message.success('操作成功')
    // 刷新列表
    await queryDataList()
  } catch {
    message.error('操作失败，请稍后重试')
  }
  dialogVisible.value = false
}

onMounted(() => {
  //获取门店类型
  tenantType.value = wsCache.get(CACHE_KEY.TenantType)
   //判断当前表头是否有缓存
  columns.value = isColumns('ProductList',columns.value)
  columnsData.value = columns.value
  queryDataList()
})
</script>

<style lang="scss" scoped>
.action-wrap {
  .action-head {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
  }
  .action-left {
    flex-shrink: 0;

    .action-content {
      .action-button {
        width: 240px;
        height: 64px;
        padding: 12px;
        box-sizing: border-box;
        background: #ffffff;
        box-shadow: 0 5px 5px 0 #0000001a;
        border: 1px solid #cdcdcd;
        border-radius: 4px;
        margin-right: 16px;
        cursor: pointer;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &:hover {
          border-color: #00b955;
          // background-color: #ecf5ff;
        }

        &:last-child {
          margin-right: 0;
        }

        .action-button-image {
          width: 40px;
          height: 40px;
          border-radius: 4px;
        }

        .action-button-text {
          margin-left: 12px;
          line-height: 1;
          color: #666666;
          font-size: 14px;
          font-weight: 400;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;

          .strong {
            margin-bottom: 8px;
            line-height: 1;
            color: #000000;
            font-size: 18px;
            font-weight: 550;
          }
        }
      }
    }
  }

  .action-right {
    flex-shrink: 0;

    .link-content {
      width: 320px;
      background-color: #f5f5f5;
      padding: 12px;
      padding-bottom: 0;
      box-sizing: border-box;
      border-radius: 4px;

      .link-action-box {
        width: 33.3333%;
        margin-bottom: 12px;
        line-height: 1;
        display: inline-flex;
        justify-content: flex-start;
        align-items: center;

        .action-link {
          cursor: pointer;
          color: #000000;
          font-size: 14px;

          &:hover {
            color: #00b955;
          }

          &:focus-visible {
            outline: none;
          }
        }

        &.el-dropdown {
          ::v-deep {
            .el-tooltip__trigger {
              display: flex;
            }
          }
        }
      }
    }
  }
}
.link-action-box-popper {
  .el-dropdown-item-head-title {
    padding: 6px 8px;
    box-sizing: border-box;
    color: #000000;
    font-size: 14px;
    font-weight: 600;
  }
}

.query-form {
  .el-form-item {
    margin: 0 12px 12px 0;

    ::v-deep(.el-form-item__label) {
      line-height: 1;
      align-items: center;
      text-align: right;
      word-break: break-all;
    }
  }
}
.action-form {
  .action-from-expand {
    &:hover {
      color: #00b955;
    }

    .action-txt {
      font-size: var(--el-font-size-base);
    }
  }
}

.table-header-action {
  .header-left {
    flex-shrink: 0;
  }

  .header-right {
    flex-shrink: 0;
    .el-dropdown-link {
      .el-button {
        outline: none;

        .dropdown-link-text {
          line-height: 1;
          color: #000000;
          font-size: 14px;
        }
      }
    }
  }
}

.table-center {
  .product-info-container {
    display: flex;
    align-items: center;
    .prescription-category {
      position: absolute;
      top: 0;
      left: 0;
      background-color: #3b82f6; /* 蓝色 */
      color: white;
      font-size: 10px;
      padding: 0 4px;
      z-index: 99;
    }
    .product-image {
      position: relative;
      width: 60px;
      height: 60px;
      margin-right: 8px;
    }
  }
}
.common-info {
  font-family: PingFangSC;
  font-size: 14px;
  color: #222222;
}
.item-content {
  .item-hover {
    flex-grow: 1;
    .item-cxt-copy {
      flex-shrink: 0;
      margin-left: 2px;
      cursor: pointer;
      display: none;
    }
    &:hover {
      color: #00b955 !important;
      font-weight: 550;
      .item-cxt-copy {
        display: inline-block;
      }
    }
  }
}
</style>
