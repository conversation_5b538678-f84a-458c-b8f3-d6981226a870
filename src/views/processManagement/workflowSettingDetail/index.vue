<template>
  <SimpleProcessDesigner :model-id="modelId" :classifyType="classifyType" />
</template>
<script setup lang="ts">
import { SimpleProcessDesigner } from '@/views/processManagement/model/SimpleProcessDesignerV2/src'


defineOptions({
  name: 'SimpleWorkflowDesignEditor'
})
const { query } = useRoute() // 路由的查询
const modelId = query.id as string
const classifyType = query.classifyType as string
</script>
<style lang="scss" scoped></style>