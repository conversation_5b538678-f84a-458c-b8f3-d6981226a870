package com.xyy.saas.inquiry.signature.server.controller.app.ca.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - CA认证分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquirySignatureCaAuthPageReqVO extends PageParam {

    @Schema(description = "用户id", example = "16240")
    private Long userId;

    @Schema(description = "签章平台 0-自签署 1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "实名认证状态 0: 待认证，1: 认证完成，2: 认证失败", example = "2")
    private Integer certifyStatus;

    @Schema(description = "签名状态 0: 未签名，1: 已签名", example = "2")
    private Integer signatureStatus;

    @Schema(description = "免签授权状态 0: 未授权，1: 已授权，", example = "1")
    private Integer authorizeFreeSignStatus;

    @Schema(description = "免签授权截止时间")
    private LocalDateTime authorizeFreeSignDdl;

    @Schema(description = "授权协议签署状态 0: 未签署，1: 已签署", example = "1")
    private Integer authorizeAgreementStatus;

    @Schema(description = "兼职协议状态 0: 未签署，1: 已签署", example = "1")
    private Integer partTimeAgreementStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}