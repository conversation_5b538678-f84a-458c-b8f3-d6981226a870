package com.xyy.saas.inquiry.enums.file;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/11/21 10:57
 */
@Getter
@RequiredArgsConstructor
public enum FileTypeEnum {
    PDF(1, "pdf"),
    PNG(2, "png"),
    JPG(3, "jpg");

    private final int code;

    private final String type;

    public static boolean isImage(String url) {
        return StringUtils.endsWithIgnoreCase(url, "jpg")
            || StringUtils.endsWithIgnoreCase(url, "png");
    }

}
