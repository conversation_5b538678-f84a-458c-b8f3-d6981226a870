package com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 门店药师关系 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_tenant_pharmacist_relation")
@KeySequence("saas_inquiry_tenant_pharmacist_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryTenantPharmacistRelationDO extends BaseDO /*TenantBaseDO*/ {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 药师ID
     */
    private Long pharmacistId;

    /**
     * 门店id
     */
    private Long tenantId;
    /**
     * 药师状态 0启用 1禁用
     */
    private Integer status;

}