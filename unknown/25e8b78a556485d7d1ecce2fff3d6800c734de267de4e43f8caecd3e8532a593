package com.xyy.saas.inquiry.product.server.service.bpm.handler;

import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.api.bpm.handler.BpmApprovalHandler;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.BPM_APPROVE_STATUS_INVALID;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.*;
import static com.xyy.saas.inquiry.product.consts.ProductConstant.*;

/**
 * 商品首营审批
 * 门店建品审批
 * 审批拆零审批
 */
@Slf4j
@Component
public class ProductApprovalHandler implements BpmApprovalHandler {

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Override
    public void handleApproval(BpmBusinessRelationDto businessDto) {
        String businessPref = businessDto.getBusinessPref();
        BpmBusinessTypeEnum businessType = BpmBusinessTypeEnum.getByCode(businessDto.getBusinessType());
        if (businessType == null) {
            return;
        }
        log.info("[BpmEvent][{} businessPref({}) 处理]", businessType.desc, businessPref);
        // 获取商品信息
        ProductInfoDO productInfo = productInfoMapper.selectOne(ProductInfoDO::getPref, businessPref);
        if (productInfo == null) {
            log.error("[BpmEvent][{} businessPref({}) 商品不存在]", businessType.desc, businessPref);
            return;
        }

        // 状态转换
        Integer approvalStatus = businessDto.getApprovalStatus();
        if (approvalStatus == null) {
            throw exception(BPM_APPROVE_STATUS_INVALID, approvalStatus);
        }
        ProductStatusEnum status = switch (approvalStatus) {
            case ApprovalStatus.REJECT -> businessType.auditRejectStatus;
            case ApprovalStatus.APPROVE -> ProductStatusEnum.USING;
            default -> null;
        };
        
        if (status == null) {
            log.error("[BpmEvent][{} businessPref({}) 审批状态({}) 不支持]", businessType.desc, businessPref, approvalStatus);
            return;
        }
        
        // 更新商品状态
        ProductInfoDO updateObj = new ProductInfoDO().setId(productInfo.getId()).setStatus(status.code);
        productInfoMapper.updateById(updateObj);
        log.info("[BpmEvent][{} businessPref({}) 商品状态更新为({})]", businessType.desc, businessPref, status.desc);
    }

    @Override
    public BpmBusinessTypeEnum[] supportBusinessTypes() {
        return new BpmBusinessTypeEnum[]{
            BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE,
            BpmBusinessTypeEnum.PRODUCT_TO_HEADQUARTERS_APPROVE
        };
    }
} 