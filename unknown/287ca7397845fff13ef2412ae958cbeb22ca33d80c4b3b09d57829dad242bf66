package cn.iocoder.yudao.module.system.convert.dict;

import cn.iocoder.yudao.module.system.api.dict.dto.DictDataPageReqDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictTypeRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataPageReqVO;
import cn.iocoder.yudao.module.system.controller.app.dict.vo.AppDictDataRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictTypeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface DictConvert {

    DictConvert INSTANCE = Mappers.getMapper(DictConvert.class);


    DictTypeRespDTO convert(DictTypeDO dictType);

    DictDataPageReqVO convertPageDto(DictDataPageReqDTO pageReqDTO);

    List<DictDataRespDTO> convertDto(List<DictDataDO> list);

    List<DictDataRespDTO> convertVOList2DtoList(List<AppDictDataRespVO> list);

}
