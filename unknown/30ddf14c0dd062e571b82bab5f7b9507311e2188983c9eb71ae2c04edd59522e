package com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * @Author: xucao
 * @Date: 2024/12/11 16:48
 * @Description: trtc调用腾讯云SDK通用返回结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TencentTrtcBaseRespDO implements Serializable {

    /**
     * 异常信息
     */
    private String errorInfo;

    /**
     * 文件id
     */
    private String fileId;


    /**
     * 转码任务id
     */
    private String taskId;

    /**
     * mp4地址
     */
    private String mp4Url;
}
