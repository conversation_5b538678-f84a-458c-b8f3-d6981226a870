package com.xyy.saas.inquiry.hospital.server.mq.message.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自动开方MQ
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionAutoIssueEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_AUTO_ISSUE";

    private String msg;

    @JsonCreator
    public PrescriptionAutoIssueEvent(@JsonProperty("msg") String msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
