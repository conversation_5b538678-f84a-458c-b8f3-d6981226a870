package com.xyy.saas.inquiry.enums.file;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 私有文件存储资源 字段枚举
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/23 16:44
 */
@Getter
@RequiredArgsConstructor
public enum PrivateUrlFieldEnum {

    ID_CARD_LIST("idCardList", true, "身份证图片列表");

    /**
     * 字段名称
     */
    private final String fieldName;
    /**
     * 是否多个
     */
    private final boolean multi;
    /**
     * 字段描述
     */
    private final String desc;

    /**
     * 获取某个bean中 存在私有url的数据Map
     *
     * @param bean 对象
     * @return 存在私有url的数据Map<Field, List < Url>>
     */
    public static Map<String, List<String>> getObjectPrivateUrlFields(Object bean) {
        final Map<String, List<String>> map = Map.of();
        if (bean == null) {
            return map;
        }
        // 匹配符合的字段
        List<Field> fields = Lists.newLinkedList();
        getBeanAllFields(bean.getClass(), fields);
        for (PrivateUrlFieldEnum fieldEnum : values()) {
            fields.stream().filter(f -> StrUtil.equals(f.getName(), fieldEnum.getFieldName())).findFirst().ifPresent(f -> {
                try {
                    if (Objects.nonNull(f.get(bean))) {
                        map.put(f.getName(), fieldEnum.isMulti() ? (List) f.get(bean) : Lists.newArrayList(f.get(bean).toString()));
                    }
                } catch (Exception ignore) {
                }
            });
        }
        return map;
    }

    /**
     * 递归获取对象所有字段，包含上游
     *
     * @param clazz  bean-class
     * @param fields 所有字段
     */
    private static void getBeanAllFields(Class<?> clazz, List<Field> fields) {
        if (Objects.nonNull(clazz.getSuperclass())) {
            getBeanAllFields(clazz.getSuperclass(), fields);
        }
        Field[] currBeanFields = clazz.getDeclaredFields();
        fields.addAll(Arrays.asList(currBeanFields));
    }

}
