package com.xyy.saas.inquiry.patient.controller.app.inquiry;

import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryComplainSaveReqVO;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryComplainService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;



import cn.iocoder.yudao.framework.common.pojo.CommonResult;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;



@Tag(name = "APP+PC - 问诊投诉举报")
@RestController
@RequestMapping(value = {"/admin-api/kernel/patient/inquiry-complain", "/app-api/kernel/patient/inquiry-complain"})
@Validated
public class InquiryComplainController {

    @Resource
    private InquiryComplainService inquiryComplainService;

    @PostMapping("/create")
    @Operation(summary = "创建问诊投诉记录")
    public CommonResult<Long> createInquiryComplain(@Valid @RequestBody InquiryComplainSaveReqVO createReqVO) {
        return success(inquiryComplainService.createInquiryComplain(createReqVO));
    }
}