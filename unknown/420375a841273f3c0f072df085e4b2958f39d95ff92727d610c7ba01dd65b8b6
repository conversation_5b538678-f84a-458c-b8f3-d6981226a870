package cn.iocoder.yudao.module.system.api.permission;

import cn.iocoder.yudao.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 权限 API 接口
 *
 * <AUTHOR>
 */
public interface PermissionApi {

    /**
     * 获得拥有多个角色的用户编号集合
     *
     * @param roleIds 角色编号集合
     * @return 用户编号集合
     */
    Set<Long> getUserRoleIdListByRoleIds(Collection<Long> roleIds);

    /**
     * 判断是否有权限，任一一个即可
     *
     * @param userId      用户编号
     * @param permissions 权限
     * @return 是否
     */
    boolean hasAnyPermissions(Long userId, String... permissions);

    /**
     * 判断是否有角色，任一一个即可
     *
     * @param userId 用户编号
     * @param roles  角色数组
     * @return 是否
     */
    boolean hasAnyRoles(Long userId, String... roles);

    /**
     * 获得登陆用户的部门数据权限
     *
     * @param userId 用户编号
     * @return 部门数据权限
     */
    DeptDataPermissionRespDTO getDeptDataPermission(Long userId);


    /**
     * 问诊设置用户角色(仅用于 签名管理-C核对/A调配/D发药)
     *
     * @param userId       角色编号
     * @param roleIds      角色编号集合
     * @param roleRangeIds 处理的角色id 范围
     */
    void assignUserRoleWithRoleRanges(Long userId, Set<Long> roleIds, Set<Long> roleRangeIds);

    /**
     * user分配某一系统角色
     *
     * @param userId   用户id
     * @param roleCode 用户code  如 医生/药师
     */
    void assignUserRoleWithSystemRoleCode(Long userId, String roleCode);

    /**
     * 查询用户角色ids
     *
     * @param userId    用户id
     * @param roleCodes 角色code
     * @return
     */
    List<RoleRespDTO> selectUserRoleByUserIdRoleCodes(Long userId, Set<String> roleCodes);

    /**
     * 查询问诊 查询问诊核对/发药/调配/药师角色列表
     *
     * @return
     */
    List<RoleRespDTO> selectWzCadRole();

    /**
     * 解除用户系统角色
     *
     * @param userId   用户id
     * @param tenantId 门店id
     * @param roleCode 系统角色id
     */
    void deleteUserSystemRole(Long userId, Long tenantId, String roleCode);
}
