package com.xyy.saas.inquiry.product.server.service.gsp;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_QUALITY_CHANGE_RECORD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.gsp.ProductQualityChangeRecordMapper;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link ProductQualityChangeRecordServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductQualityChangeRecordServiceImpl.class)
public class ProductQualityChangeRecordServiceImplTest extends BaseIntegrationTest {

    @Resource
    private ProductQualityChangeRecordServiceImpl productQualityChangeRecordService;

    @Resource
    private ProductQualityChangeRecordMapper productQualityChangeRecordMapper;

    @Test
    public void testCreateProductQualityChangeRecord_success() {
        // 准备参数
        ProductQualityChangeRecordSaveReqVO createReqVO = randomPojo(ProductQualityChangeRecordSaveReqVO.class).setId(null);

        // 调用
        Long productQualityChangeRecordId = productQualityChangeRecordService.saveOrUpdateQualityChange(createReqVO);
        // 断言
        assertNotNull(productQualityChangeRecordId);
        // 校验记录的属性是否正确
        ProductQualityChangeRecordDO productQualityChangeRecord = productQualityChangeRecordMapper.selectById(productQualityChangeRecordId);
        assertPojoEquals(createReqVO, productQualityChangeRecord, "id");
    }

    @Test
    public void testUpdateProductQualityChangeRecord_success() {
        // mock 数据
        ProductQualityChangeRecordDO dbProductQualityChangeRecord = randomPojo(ProductQualityChangeRecordDO.class);
        productQualityChangeRecordMapper.insert(dbProductQualityChangeRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ProductQualityChangeRecordSaveReqVO updateReqVO = randomPojo(ProductQualityChangeRecordSaveReqVO.class, o -> {
            o.setId(dbProductQualityChangeRecord.getId()); // 设置更新的 ID
        });

        // 调用
        productQualityChangeRecordService.saveOrUpdateQualityChange(updateReqVO);
        // 校验是否更新正确
        ProductQualityChangeRecordDO productQualityChangeRecord = productQualityChangeRecordMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, productQualityChangeRecord);
    }

    @Test
    public void testUpdateProductQualityChangeRecord_notExists() {
        // 准备参数
        ProductQualityChangeRecordSaveReqVO updateReqVO = randomPojo(ProductQualityChangeRecordSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> productQualityChangeRecordService.saveOrUpdateQualityChange(updateReqVO), PRODUCT_QUALITY_CHANGE_RECORD_NOT_EXISTS);
    }

    @Test
    public void testDeleteProductQualityChangeRecord_success() {
        // mock 数据
        ProductQualityChangeRecordDO dbProductQualityChangeRecord = randomPojo(ProductQualityChangeRecordDO.class);
        productQualityChangeRecordMapper.insert(dbProductQualityChangeRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbProductQualityChangeRecord.getId();

        // 调用
        productQualityChangeRecordService.deleteProductQualityChangeRecord(id);
       // 校验数据不存在了
       assertNull(productQualityChangeRecordMapper.selectById(id));
    }

    @Test
    public void testDeleteProductQualityChangeRecord_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> productQualityChangeRecordService.deleteProductQualityChangeRecord(id), PRODUCT_QUALITY_CHANGE_RECORD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetProductQualityChangeRecordPage() {
       // mock 数据
       ProductQualityChangeRecordDO dbProductQualityChangeRecord = randomPojo(ProductQualityChangeRecordDO.class, o -> { // 等会查询到
           o.setType(null);
           o.setPref(null);
           o.setApplicant(null);
           o.setApplicationTime(null);
           o.setApprovalStatus(null);
           o.setCurrentHandler(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       productQualityChangeRecordMapper.insert(dbProductQualityChangeRecord);
       // 测试 type 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setType(null)));
       // 测试 billNo 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setPref(null)));
       // 测试 applicant 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setApplicant(null)));
       // 测试 applicationTime 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setApplicationTime(null)));
       // 测试 approvalStatus 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setApprovalStatus(null)));
       // 测试 currentHandler 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setCurrentHandler(null)));
       // 测试 remark 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       productQualityChangeRecordMapper.insert(cloneIgnoreId(dbProductQualityChangeRecord, o -> o.setCreateTime(null)));
       // 准备参数
       ProductQualityChangeRecordPageReqVO reqVO = new ProductQualityChangeRecordPageReqVO();
       reqVO.setType(null);
       reqVO.setPref(null);
       reqVO.setApplicant(null);
       reqVO.setApplicationTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setApprovalStatus(null);
       reqVO.setCurrentHandler(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ProductQualityChangeRecordDO> pageResult = productQualityChangeRecordService.getProductQualityChangeRecordPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbProductQualityChangeRecord, pageResult.getList().get(0));
    }

}