package com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 审批流关联业务 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BpmBusinessRelationRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26585")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21079")
    @ExcelProperty("租户编号")
    private Long tenantId;

    @Schema(description = "租户编号（总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "21079")
    @ExcelProperty("租户编号（总部）")
    private Long headTenantId;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型")
    private Integer businessType;

    @Schema(description = "业务单据编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22519")
    @ExcelProperty("业务单据编号")
    private String businessPref;

    @Schema(description = "流程实例的编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4711")
    @ExcelProperty("流程实例的编号")
    private String processInstanceId;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请人")
    private String applicant;

    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("审批状态")
    private Integer approvalStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}