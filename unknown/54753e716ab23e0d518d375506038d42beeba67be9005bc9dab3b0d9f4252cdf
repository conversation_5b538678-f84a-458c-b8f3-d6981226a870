package com.xyy.saas.transmitter.server.service.task;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.task.TransmissionTaskRecordDO;
import com.xyy.saas.transmitter.server.dal.mysql.task.TransmissionTaskRecordMapper;
import com.xyy.saas.transmitter.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Import(TransmissionTaskRecordServiceImpl.class)
public class TransmissionTaskRecordServiceTest extends BaseIntegrationTest {

    @Resource
    private TransmissionTaskRecordServiceImpl taskService;

    @Resource
    private TransmissionTaskRecordMapper taskMapper;

    // ====================== 创建相关测试 ======================
    @Test
    @DisplayName("测试创建任务记录-成功场景")
    public void testCreateTaskRecord_Success() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO reqVO = buildTaskRecordCreateReqVO();

        // 执行测试
        Long id = taskService.createTransmissionTaskRecord(reqVO);

        // 验证结果
        assertNotNull(id);
        TransmissionTaskRecordDO record = taskMapper.selectById(id);
        assertPojoEquals(reqVO, record,"id");
    }

    @Test
    @DisplayName("测试创建或更新任务记录-新建场景")
    public void testCreateOrUpdateTask_Create() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO mainTask = buildTaskRecordCreateReqVO();
        mainTask.setId(null); // 确保是新建
        TransmissionTaskRecordSaveReqVO downstreamTask = buildTaskRecordCreateReqVO();

        // 执行测试 - 使用 new ArrayList<>() 替代 Collections.singletonList()
        taskService.createOrUpdateTask(mainTask, new ArrayList<>(Arrays.asList(downstreamTask)));

        // 验证结果
        assertNotNull(mainTask.getId());
        TransmissionTaskRecordDO mainTaskRecord = taskMapper.selectById(mainTask.getId());
        assertPojoEquals(mainTask, mainTaskRecord, "id");

        PageResult<TransmissionTaskRecordRespVO> pageResult = taskService.getTransmissionTaskRecordPage(
            TransmissionTaskRecordPageReqVO.builder().upstreamTaskId(mainTask.getId()).build());
        assertNotNull(pageResult);
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(downstreamTask, pageResult.getList().get(0), "id");
    }

    @Test
    @DisplayName("测试创建或更新任务记录-无下游任务场景")
    public void testCreateOrUpdateTask_NoDownstreamTaskId() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO reqVO = buildTaskRecordCreateReqVO();

        // 执行测试 - 使用 new ArrayList<>() 替代 Collections.emptyList()
        taskService.createOrUpdateTask(reqVO, new ArrayList<>());

        // 验证结果
        assertNotNull(reqVO.getId());
        TransmissionTaskRecordDO record = taskMapper.selectById(reqVO.getId());
        assertPojoEquals(reqVO, record, "id");
        assertEquals(0L,record.getUpstreamTaskId());
    }

    // ====================== 更新相关测试 ======================
    @Test
    @DisplayName("测试更新任务记录状态")
    public void testUpdateTaskRecordStatus() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO reqVO = buildTaskRecordCreateReqVO();
        Long id = taskService.createTransmissionTaskRecord(reqVO);

        // 执行测试
        TransmissionTaskRecordSaveReqVO updateReqVO = TransmissionTaskRecordSaveReqVO.builder()
                                                      .id(id)
                                                      .requestStatus(2).build();// 更新为成功状态
        taskService.updateTransmissionTaskRecord(updateReqVO);

        // 验证结果
        TransmissionTaskRecordRespVO record = taskService.getTransmissionTaskRecord(id);
        assertEquals(2, record.getRequestStatus());
    }

    @Test
    @DisplayName("测试创建或更新任务记录-更新场景")
    public void testCreateOrUpdateTask_Update() {
        // 准备测试数据 - 先创建一条记录
        TransmissionTaskRecordSaveReqVO createReqVO = buildTaskRecordCreateReqVO();
        Long id = taskService.createTransmissionTaskRecord(createReqVO);

        // 准备更新数据
        TransmissionTaskRecordSaveReqVO updateReqVO = buildTaskRecordCreateReqVO();
        updateReqVO.setId(id);
        updateReqVO.setRequestStatus(2); // 更新状态
        updateReqVO.setRetryCount(1); // 更新重试次数
        updateReqVO.setErrorMessage("测试错误"); // 更新错误信息

        // 执行测试 - 使用 new ArrayList<>() 替代 Collections.emptyList()
        taskService.createOrUpdateTask(updateReqVO, new ArrayList<>());

        // 验证结果
        TransmissionTaskRecordDO record = taskMapper.selectById(id);
        assertPojoEquals(updateReqVO, record, "id");
        assertEquals(2, record.getRequestStatus());
        assertEquals(1, record.getRetryCount());
        assertEquals("测试错误", record.getErrorMessage());
    }

    // ====================== 查询相关测试 ======================
    @Test
    @DisplayName("测试获取任务记录-按ID查询")
    public void testGetTaskRecord_ById() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO reqVO = buildTaskRecordCreateReqVO();
        Long id = taskService.createTransmissionTaskRecord(reqVO);

        // 执行测试
        TransmissionTaskRecordRespVO record = taskService.getTransmissionTaskRecord(id);

        // 验证结果
        assertNotNull(record);
        assertPojoEquals(reqVO, record,"id");
    }

    @Test
    @DisplayName("测试获取任务记录-查询不存在记录")
    public void testGetTaskRecord_NotExists() {
        assertThrows(ServiceException.class, () -> taskService.getTransmissionTaskRecord(9999L));
    }

    @Test
    @DisplayName("测试获取任务记录-按状态查询")
    public void testGetTaskRecordByStatus() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO reqVO1 = buildTaskRecordCreateReqVO();
        reqVO1.setRequestStatus(0); // 未请求
        Long id1 = taskService.createTransmissionTaskRecord(reqVO1);

        TransmissionTaskRecordSaveReqVO reqVO2 = buildTaskRecordCreateReqVO();
        reqVO2.setRequestStatus(1); // 请求中
        Long id2 = taskService.createTransmissionTaskRecord(reqVO2);

        // 构建查询条件
        TransmissionTaskRecordPageReqVO pageReqVO = TransmissionTaskRecordPageReqVO.builder()
                                                    .requestStatus(0).build();

        // 执行测试
        PageResult<TransmissionTaskRecordRespVO> pageResult = taskService.getTransmissionTaskRecordPage(pageReqVO);

        // 验证结果
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(reqVO1, pageResult.getList().get(0),"id");
    }

    @Test
    @DisplayName("测试获取任务记录-按时间范围查询")
    public void testGetTaskRecordByTimeRange() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO reqVO = buildTaskRecordCreateReqVO();
        Long id = taskService.createTransmissionTaskRecord(reqVO);

        // 构建查询条件
        LocalDateTime now = LocalDateTime.now();
        TransmissionTaskRecordPageReqVO pageReqVO = TransmissionTaskRecordPageReqVO.builder()
                                                    .createTime(new LocalDateTime[]{now.minusDays(1), now.plusDays(1)}).build();

        // 执行测试
        PageResult<TransmissionTaskRecordRespVO> pageResult = taskService.getTransmissionTaskRecordPage(pageReqVO);

        // 验证结果
        assertEquals(1, pageResult.getTotal());
        assertPojoEquals(reqVO, pageResult.getList().get(0),"id");
    }

    @Test
    @DisplayName("测试获取任务记录-多条件组合查询")
    public void testGetTaskRecordByMultiConditions() {
        // 准备测试数据
        TransmissionTaskRecordSaveReqVO reqVO = buildTaskRecordCreateReqVO();
        reqVO.setOrganType(1);
        reqVO.setNodeType(1);
        reqVO.setRequestStatus(0);
        Long id = taskService.createTransmissionTaskRecord(reqVO);

        // 构建查询条件
        TransmissionTaskRecordPageReqVO pageReqVO = TransmissionTaskRecordPageReqVO.builder()
                                                    .organType(1)
                                                    .nodeType(1)
                                                    .requestStatus(0)
                                                    .build();

        // 执行测试
        PageResult<TransmissionTaskRecordRespVO> pageResult = taskService.getTransmissionTaskRecordPage(pageReqVO);

        // 验证结果
        assertEquals(1, pageResult.getTotal());
        assertPojoEquals(reqVO, pageResult.getList().get(0),"id");
    }

    // ====================== 辅助方法 ======================
    private TransmissionTaskRecordSaveReqVO buildTaskRecordCreateReqVO() {
        return TransmissionTaskRecordSaveReqVO.builder()
                .upstreamTaskId(0L)
                .tenantId(1L)
                .servicePackId(1)
                .configItemId(1)
                .organId(1)
                .organType(1)
                .nodeType(1)
                .fullName("张三")
                .idCard("123456789012345678")
                .apiCode("TEST001")
                .requestStatus(0)
                .allowRetry(true)
                .maxRetryCount(3)
                .retryCount(0)
                .priority(5)
                .build();
    }
} 