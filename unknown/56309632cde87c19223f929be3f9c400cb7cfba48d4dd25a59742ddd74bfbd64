package com.xyy.saas.inquiry.im.server.service.message.strategy.imcallback;

import com.xyy.saas.inquiry.enums.tencent.TencentImCallBackEventEnum;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.convert.message.InquiryImMessageConvert;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/11/29 14:11
 * @Description: 单聊消息发送后回调处理策略
 */
@Component
public class AfterSendMsgHandleStrategy implements TencentImCallBackHandleStrategy{

    @Resource
    private InquiryImMessageService inquiryImMessageService;

    /**
     * 策略执行器
     *
     * @param callBackReqVO 回调参数
     */
    @Override
    public void executeStrategy(TencentImCallBackReqVO callBackReqVO) {
        //保存消息
       inquiryImMessageService.createInquiryImMessage(InquiryImMessageConvert.INSTANCE.converOutVO2VO(callBackReqVO));
    }

    /**
     * 获取策略对应的事件
     *
     * @return 事件枚举
     */
    @Override
    public TencentImCallBackEventEnum getEvent() {
        return TencentImCallBackEventEnum.C2C_CALLBACK_AFTER_SEND_MSG;
    }
}
