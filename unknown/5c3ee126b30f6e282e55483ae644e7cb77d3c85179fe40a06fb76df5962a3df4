package com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 科室诊断关联 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_diagnosis_department_relation")
@KeySequence("saas_inquiry_diagnosis_department_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryDiagnosisDepartmentRelationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 诊断编码
     */
    private String diagnosisCode;
    /**
     * 诊断名称
     */
    private String diagnosisName;
    /**
     * 医院科室id
     */
    private Long deptId;
    /**
     * 科室编码,eg:101
     */
    private String deptPref;
    /**
     * 科室名称,eg:内科
     */
    private String deptName;
}