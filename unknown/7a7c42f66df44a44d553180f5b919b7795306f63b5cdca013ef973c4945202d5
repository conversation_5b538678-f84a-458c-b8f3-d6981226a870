package com.xyy.saas.inquiry.signature.api.ca.dto;

import com.xyy.saas.inquiry.pojo.forward.ForwardCaInfo;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/03 21:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncCreateCaDto implements Serializable {

    private Long userId;

    private String name;

    private String idCard;

    private String mobile;

    private ForwardCaInfo caInfo;

    private ForwardPersonInfo personInfo;


}
