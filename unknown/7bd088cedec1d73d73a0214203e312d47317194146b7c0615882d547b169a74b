package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/01/22 19:18
 * @Description: 门店问诊统计看板出参
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DrugstoreInquiryBoardRespVO {

    @Schema(description = "待接诊数量", example = "5")
    private int waitReceptionNum;

    @Schema(description = "进行中数量", example = "4")
    private int receivingNum;
}
