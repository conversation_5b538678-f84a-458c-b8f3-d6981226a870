package com.xyy.saas.inquiry.product.server.config.forward.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralMatchProduct implements Serializable {

    private static final long serialVersionUID = -4170878237647676475L;

    /**
     * 商品编码
     */
    private String businessCode;

    /**
     * 商品分类id
     */
    private Integer spuCategory;

    /**
     * 通用名
     */
    private String generalName;

    /**
     * 批准文号
     */
    private String approvalNo;


    /**
     * 生产厂家名称
     */
    private String manufacturerName;

    /**
     * 生产厂家名称(new,不处理空格)
     */
    private String manufacturerNameNew;


    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 小包装条码
     */
    private String smallPackageCode;

    private String packageUnitName;

    /**
     * 品牌/商标
     */
    private String brand;

    /**
     * 产地
     */
    private String originPlace;

    /**
     * 停用状态类型:1-查启用状态、2-查停用和启用状态、3-查停用状态
     */
    private Integer disableStatusType = 1;
}
