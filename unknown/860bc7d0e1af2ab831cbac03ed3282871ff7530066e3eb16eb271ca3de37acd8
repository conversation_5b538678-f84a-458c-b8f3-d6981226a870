package com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 问诊配置选项查询 Request VO")
@Data
public class InquiryOptionConfigQueryReqVO implements Serializable {

    @Schema(description = "ID", example = "1")
    private Long id;
    /**
     * 目标类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum}
     */
    @Schema(description = "目标类型", example = "1")
    private Integer targetType;
    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    @Schema(description = "选项类型", example = "1")
    private List<Integer> optionTypeList;

    @Schema(description = "区域编码或者租户id", example = "1")
    private Long targetId;

    @Schema(description = "区域名称或者租户名称", example = "赵六")
    private String targetName;

    @Schema(description = "省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

}