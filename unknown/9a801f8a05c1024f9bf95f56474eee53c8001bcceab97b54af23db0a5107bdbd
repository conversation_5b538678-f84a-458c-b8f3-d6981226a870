package com.xyy.saas.inquiry.product.server.service.transfer;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_TRANSFER_RECORD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.transfer.ProductTransferRecordMapper;
import com.xyy.saas.inquiry.product.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link ProductTransferRecordServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductTransferRecordServiceImpl.class)
public class ProductTransferRecordServiceImplTest extends BaseIntegrationTest {

    @Resource
    private ProductTransferRecordServiceImpl productTransferRecordService;

    @Resource
    private ProductTransferRecordMapper productTransferRecordMapper;

    @Test
    public void testCreateProductTransferRecord_success() {
        // 准备参数
        ProductTransferRecordSaveReqVO createReqVO = randomPojo(ProductTransferRecordSaveReqVO.class).setId(null);

        // 调用
        Long productTransferRecordId = productTransferRecordService.createProductTransferRecord(createReqVO).getId();
        // 断言
        assertNotNull(productTransferRecordId);
        // 校验记录的属性是否正确
        ProductTransferRecordDO productTransferRecord = productTransferRecordMapper.selectById(productTransferRecordId);
        assertPojoEquals(createReqVO, productTransferRecord, "id");
    }

    @Test
    public void testUpdateProductTransferRecord_success() {
        // mock 数据
        ProductTransferRecordDO dbProductTransferRecord = randomPojo(ProductTransferRecordDO.class);
        productTransferRecordMapper.insert(dbProductTransferRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ProductTransferRecordSaveReqVO updateReqVO = randomPojo(ProductTransferRecordSaveReqVO.class, o -> {
            o.setId(dbProductTransferRecord.getId()); // 设置更新的 ID
        });

        // 调用
        productTransferRecordService.updateProductTransferRecord(updateReqVO);
        // 校验是否更新正确
        ProductTransferRecordDO productTransferRecord = productTransferRecordMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, productTransferRecord);
    }

    @Test
    public void testUpdateProductTransferRecord_notExists() {
        // 准备参数
        ProductTransferRecordSaveReqVO updateReqVO = randomPojo(ProductTransferRecordSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> productTransferRecordService.updateProductTransferRecord(updateReqVO), PRODUCT_TRANSFER_RECORD_NOT_EXISTS);
    }

    @Test
    public void testDeleteProductTransferRecord_success() {
        // mock 数据
        ProductTransferRecordDO dbProductTransferRecord = randomPojo(ProductTransferRecordDO.class);
        productTransferRecordMapper.insert(dbProductTransferRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbProductTransferRecord.getId();

        // 调用
        productTransferRecordService.deleteProductTransferRecord(id);
       // 校验数据不存在了
       assertNull(productTransferRecordMapper.selectById(id));
    }

    @Test
    public void testDeleteProductTransferRecord_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> productTransferRecordService.deleteProductTransferRecord(id), PRODUCT_TRANSFER_RECORD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetProductTransferRecordPage() {
       // mock 数据
       ProductTransferRecordDO dbProductTransferRecord = randomPojo(ProductTransferRecordDO.class, o -> { // 等会查询到
           o.setProductPref(null);
           o.setType(null);
           o.setSourceTenantId(null);
           o.setTargetTenantId(null);
           o.setStatus(null);
           o.setResult(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       productTransferRecordMapper.insert(dbProductTransferRecord);
       // 测试 productId 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setProductPref(null)));
       // 测试 type 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setType(null)));
       // 测试 sourceTenantId 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setSourceTenantId(null)));
       // 测试 targetTenantId 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setTargetTenantId(null)));
       // 测试 status 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setStatus(null)));
       // 测试 result 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setResult(null)));
       // 测试 remark 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       productTransferRecordMapper.insert(cloneIgnoreId(dbProductTransferRecord, o -> o.setCreateTime(null)));
       // 准备参数
       ProductTransferRecordPageReqVO reqVO = new ProductTransferRecordPageReqVO();
       reqVO.setProductPref(null);
       reqVO.setType(null);
       reqVO.setSourceTenantId(null);
       reqVO.setTargetTenantId(null);
       reqVO.setStatus(null);
       reqVO.setResult(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ProductTransferRecordDO> pageResult = productTransferRecordService.getProductTransferRecordPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbProductTransferRecord, pageResult.getList().get(0));
    }

}