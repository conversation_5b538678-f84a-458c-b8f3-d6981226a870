package cn.iocoder.yudao.module.system.dal.mysql.dict;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataSaveBatchReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xyy.saas.inquiry.constant.TenantConstant;
import org.apache.ibatis.annotations.Mapper;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Mapper
public interface DictDataMapper extends BaseMapperX<DictDataDO> {


    default DictDataDO selectByDictTypeAndValue(Long tenantId, String dictType, String value) {
        return selectOne(new LambdaQueryWrapper<DictDataDO>()
            .eq(DictDataDO::getDictType, dictType)
            .eq(DictDataDO::getValue, value)
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId)));
    }

    default DictDataDO selectByDictTypeAndLabel(Long tenantId, String dictType, String label) {
        return selectOne(new LambdaQueryWrapper<DictDataDO>()
            .eq(DictDataDO::getDictType, dictType)
            .eq(DictDataDO::getLabel, label)
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId)));
    }

    default List<DictDataDO> selectByDictTypeAndValues(Long tenantId, String dictType, Collection<String> values) {
        return selectList(new LambdaQueryWrapper<DictDataDO>()
            .eq(DictDataDO::getDictType, dictType)
            .in(DictDataDO::getValue, values)
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId)));
    }

    default long selectCountByDictType(String dictType) {
        return selectCount(new LambdaQueryWrapperX<DictDataDO>()
            .eq(DictDataDO::getDictType, dictType));
    }

    default PageResult<DictDataDO> selectPage(DictDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DictDataDO>()
            .likeIfPresent(DictDataDO::getLabel, reqVO.getLabel())
            .eqIfPresent(DictDataDO::getDictType, reqVO.getDictType())
            .eqIfPresent(DictDataDO::getStatus, reqVO.getStatus())
            .inIfPresent(DictDataDO::getId, reqVO.getDictIds())
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(reqVO.getTenantId()))
            .notIn(CollUtil.isNotEmpty(reqVO.getNoExistsIds()), DictDataDO::getId, reqVO.getNoExistsIds())
            .orderByDesc(Arrays.asList(DictDataDO::getDictType, DictDataDO::getSort)));
    }

    default List<DictDataDO> selectListByStatusAndDictType(Integer status, Long tenantId, String dictType, Integer endNode) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
            .eqIfPresent(DictDataDO::getStatus, status)
            .eqIfPresent(DictDataDO::getDictType, dictType)
            .eqIfPresent(DictDataDO::getEndNode, endNode)
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId)));
    }

    default List<DictDataDO> selectListByStatusAndDictType(Integer status, Long tenantId, List<String> dictTypes, Integer endNode) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
            .eqIfPresent(DictDataDO::getStatus, status)
            .inIfPresent(DictDataDO::getDictType, dictTypes)
            .eqIfPresent(DictDataDO::getEndNode, endNode)
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId)));
    }

    default List<DictDataDO> getDictDatas(Long tenantId, String dictType, String dictName) {
        return selectList(new LambdaQueryWrapperX<DictDataDO>()
            .eqIfPresent(DictDataDO::getDictType, dictType)
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId))
            .and((q) -> q.like(DictDataDO::getLabel, dictName).or().like(DictDataDO::getValue, dictName)));
    }


    List<DictDataDO> selectByTenantIdDictTypeAndValues(Long tenantId, String dictType, Collection<String> valueList);

    int insertOrUpdateBatch(DictDataSaveBatchReqVO reqVO);
}
