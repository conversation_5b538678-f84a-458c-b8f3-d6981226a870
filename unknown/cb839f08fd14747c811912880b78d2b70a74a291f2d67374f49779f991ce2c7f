package cn.iocoder.yudao.module.system.convert.appversion;

import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDetailDO;
import com.xyy.saas.inquiry.enums.system.AppVersionDetailBussnissTypeEnum;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.Mapper;

/**
 * @Author: xucao
 * @Date: 2025/01/22 13:30
 * @Description: app版本详情转换器
 */
@Mapper
public interface AppVersionDetailConvert {

    AppVersionDetailConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(AppVersionDetailConvert.class);

    default List<AppVersionDetailDO> convertVO2DO(AppVersionDO versionDO, List<Long> grayTenant) {
        if (ObjectUtils.isEmpty(grayTenant)) {
            return null;
        }
        return grayTenant.stream().distinct().map(item -> {
            return AppVersionDetailDO.builder().appVersionId(versionDO.getId()).bussnissType(AppVersionDetailBussnissTypeEnum.GRAY_TENANT.getCode()).tenantId(item).build();
        }).toList();

    }
}
