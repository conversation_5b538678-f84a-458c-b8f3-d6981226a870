dslType: contract
enable: true
name: 派药
format: json
functions:
  - path: "'/rainbow/api/hpcp/hospolmonitor/drugDelivery'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "'Q430'"
          "body":
            - "medicalNum": business.data['data']['inquiryPref'] # 就诊流水号，由医院端上传，必须保证同一家医院的就诊流水号是唯一的
              "billNum": business.data['data']['pref'] # 单据号，单据号并不是发票号，只是标识一个就诊流水号下一笔费用结算单据
              "p_controlResultNo": business.data['aux']['30002']['electronicRxSn'] # 电子处方平台流水号，市监管平台流水号
              "medicalType": "'10'" # 医疗类别，字典映射 10-药店购药 11-普通门诊
              "treatDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data['data']['inquiryStartTime']) # 就诊时间，格式YYYYMMDDHH24MISS，指的是患者实际就诊时间，不是HIS系统数据产生时间
              "updateBy": business.data['aux']['operateUserInfo']?.nickname # 经办人，医疗机构操作员姓名
              "invoiceNO": business.data['data']['ext']['setlInvoiceNumber'] # 发票号，票据上的发票号码
              "recipeList": # 下方是处方明细信息
                - "productName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 药品商品名，如为药品，提供商品名
                  "recipeSerialNum": _index_ # 同一个就诊下，处方流水号在中心端能够 唯一标识一条处方明细信息 目录类别为1，则上传处方明细流水号； 其余收费明细流水号
                  "listCat": "'1'" # 目录类别，非空1:药品；2:诊疗项目；3:服务设施；4:医用材料；5：转诊
                  "medicalItemCat": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '01' : '03'" # 医疗项目类别，非空 字典映射
                  "recipeNum": business.data['data']['pref'] # 处方号，非空目录类别为1，则上传处方号；其余上传发票号；若没有发票号，则传单据号
                  "recipeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data['data']['auditPrescriptionTime']) # 收费日期，格式YYYYMMDDHH24MISS，非空
                  "productFactory": business.data['aux']['prescriptionDetail'][_index_]['manufacturer'] # 药品厂家，如为药品，提供商品厂家名
                  "hospitalChargeCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['standardId'],business.data['aux']['prescriptionDetail'][_index_]['productPref'])" # 医院收费项目编码，非空
                  "hospitalChargeName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 医院收费项目名称，非空
                  "priceitemCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['standardId'],business.data['aux']['prescriptionDetail'][_index_]['productPref'])" # 物价项目编码，非空物价局统一的医疗服务项目编码，如普通门诊诊察费：AAAA0001，不是医疗服务项目时，传医院收费编码
                  "centreChargeCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['standardId'],business.data['aux']['prescriptionDetail'][_index_]['productPref'])" # 医保收费项目编码，非空本地就医时对应患者的医保编号，如目录类别是药品时，项目编码指的是药品编码；如果目录类别是诊疗项目时，项目编码为诊疗项目编码；如果目录类别为医用材料时，项目编码为医用材料编码。自费收费时，默认为本地城镇职工医保收费项目编码不在医保三大目录内的项目，比如伙食费、快递费等项目编码传PAXNBL0001
                  "medicareFeeitemName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 医保收费项目名称，社保经办机构三大目录管理规范名称，非空
                  "price": business.data['aux']['prescriptionDetail'][_index_]['productPrice'] # 单价，float(8)，非空4位小数
                  "quantity": business.data['aux']['prescriptionDetail'][_index_]['quantity'] # 数量，float(8)，非空4位小数，按照目录库中的包装上传入，非招标按照实际情况传入
                  "money": business.data['aux']['prescriptionDetail'][_index_]['actualAmount'] # 金额，float(8)，非空4位小数
                  "spec": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 规格
                  "trialDoctorCode": business.data['data']['pharmacistPref'] # 审方药师编码
                  "trialDoctorName": business.data['data']['pharmacistName'] # 审方药师名称
                  "deliverPharmCode": business.data['data']['pharmacistPref'] # 发药药师编码
                  "deliverPharmName": business.data['data']['pharmacistName'] # 发药药师名称
                  "perQuantity": business.data['aux']['prescriptionDetail'][_index_]['singleDose'] # 每次用量，4位小数，按照目录库中的最小单位数量
                  "frequency": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'drug_use_frequency',business.data['aux']['prescriptionDetail'][_index_]['useFrequency'])" # 使用频次，字典映射
                  "medicationRoute": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'drug_directions',business.data['aux']['prescriptionDetail'][_index_]['directions'])" # 给药途径
                  "drugDoseUnit": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'drug_dose_unit',business.data['aux']['prescriptionDetail'][_index_]['singleUnit'])" # 单次给药剂量单位，药品时需要填写，字典映射
                  "groupNo": "'1'" # 配伍组号，药品时需要填写
                  "deptNum": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'dept_dict',business.data['aux']['clinicalCase']?.deptPref)" # 科室编码，非空字典映射，系统的科室编码
                  "deptName": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDictLabel(business,'dept_dict',business.data['aux']['clinicalCase']?.deptPref,business.data['aux']['clinicalCase']?.deptName)" # 科室名称，非空系统的科室名称
                  "doctorCode": business.data['aux']?.get('doctorInfo')?.doctorHospitalPref # 诊断医师编号，必须和Q370中的医生编号相同
                  "doctorName": business.data['data']['doctorName'] # 诊断医生姓名
                  "recipeDocTitle": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'doctor_title',business.data['aux']['doctorInfo']?.titleCode)" # 开方医生职称，字典映射
                  "selfPayRatio": "'100%'" # 自付比例，如果医保已经支付，此处填写医保的乙类自付比例：丙类自费时100%，乙类按照实际比例传入；否则默认传入本地城镇职工医保的自付比例
                  "keepUseFlag": "'1'" # 处方配送标识，字典映射：1-医院药房自取、2-医院派送、3-其他药店自取、4-其他药店派送
                  "recipedistribut": "'3'" # 处方配送标识，字典映射：1-医院药房自取、2-医院派送、3-其他药店自取、4-其他药店派送
    response:
      "infcode": "['package']['additionInfo'][errorCode]"
      "err_msg": "['package']['additionInfo'][errorMsg]"