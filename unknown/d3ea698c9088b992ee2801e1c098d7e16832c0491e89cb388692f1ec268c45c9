package com.xyy.saas.inquiry.patient.controller.admin.third.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 三方药品匹配失败记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ThirdPartyDrugMatchFailRecordPageReqVO extends PageParam {

    @Schema(description = "租户id", example = "1")
    private Long tenantId;

    @Schema(description = "三方平台代码", example = "1")
    private Integer transmissionOrganId;

    @Schema(description = "药品名称", example = "赵六")
    private String commonName;

    @Schema(description = "商品规格")
    private String attributeSpecification;

    @Schema(description = "69码")
    private String barCode;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "匹配失败原因")
    private String matchFailMsg;

    @Schema(description = "开始时间 - 结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}