package com.xyy.saas.inquiry.enums.doctor;

import lombok.Getter;

/**
 * @Author: xucao
 * @DateTime: 2025/3/17 16:23
 * @Description: 问诊医生评价状态枚举 0 未评价 1已评价
 **/
@Getter
public enum DoctorReviewStateEnum {
    UN_REVIEW(0,"未评价"),
    REVIEWED(1,"已评价");

    private Integer state;
    private String desc;

    DoctorReviewStateEnum(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }
}
