package com.xyy.saas.inquiry.enums.user;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * 员工状态
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/13 20:40
 */
@Getter
@RequiredArgsConstructor
public enum UserStatusEnum implements IntArrayValuable {

    ENABLE(0, "启用(正常)"),

    DISABLE(1, "禁用(冻结)"),

    QUIT(2, "离职");

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(UserStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static UserStatusEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(ENABLE);
    }


    public static UserStatusEnum convertSsoStatus(Integer code) {
        if (Objects.equals(code, DISABLE.getCode())) {
            return ENABLE;
        }
        return fromCode(code);
    }
}
