package com.xyy.saas.inquiry.product.api;


import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface ProductStdlibApi {

    /**
     * 查询自建标准库商品
     *
     * @param searchDto 查询条件
     * @param
     * @return 去重后的商品通用名列表
     */
    List<ProductStdlibDto> searchProductStdlibList(StdlibProductSearchDto searchDto, int limit);
}
