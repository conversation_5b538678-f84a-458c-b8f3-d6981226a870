package com.xyy.saas.inquiry.product.server.dal.mysql.product;

import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import lombok.Data;

@Data
public class ProductInfoDO2 extends ProductInfoDO {

    private ProductUseInfoDO useInfo;

    private ProductStdlibDO stdlib;


}