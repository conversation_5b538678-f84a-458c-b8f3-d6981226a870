package com.xyy.saas.inquiry.product.server.service.forward.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/26 9:47
 */
@Data
public class ProductStandardForwardDto implements Serializable {


    /**
     * 商品编码
     */
    private String pref;
    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品通用名称
     */
    private String commonName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 规格
     */
    private String attributeSpecification;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 使用频次
     */
    private String useFrequency;

    /**
     * 使用频次Value
     */
    private String useFrequencyValue;

    /**
     * 单次剂量
     */
    private String singleDose;

    /**
     * 单次剂量单位
     */
    private String singleUnit;

    /**
     * 最小使用剂量单位
     */
    private String minUseUtil;

    /**
     * 用药方法
     */
    private String directions;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 标准库id
     */
    private String standardId;

    /**
     * 剂型
     */
    private String dosageForm;
}
