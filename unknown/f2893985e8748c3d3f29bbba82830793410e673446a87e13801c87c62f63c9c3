package com.xyy.saas.inquiry.pharmacist.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionAuditOutTimeEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 处方审核超时MQ
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PrescriptionAuditOutTimeEvent.TOPIC
)
public class PrescriptionAuditOutTimeProducer extends EventBusRocketMQTemplate {


}
