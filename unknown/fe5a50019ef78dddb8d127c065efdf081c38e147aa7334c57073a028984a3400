### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "admin",
  "password": "admin123"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 批量更新订单
POST {{baseAdminSystemUrl}}/system/tenant/import/batch-update-package-relation
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "fileUrl": "https://files.test.ybm100.com/INVT/Lzinq/20250506/6f82e42c6a6a4633800783defb75d1fc.xlsx"
}

### 批量续费套餐
POST {{baseAdminSystemUrl}}/system/tenant/import/batch-open-package-relation
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "fileUrl": "https://files.test.ybm100.com/INVT/Lzinq/20250506/e46743fedd094be19177b812b26c2770.xlsx"
}

### 批量开通、编辑门店+开套餐
POST {{baseAdminSystemUrl}}/system/tenant/import/batch-tenant-and-package-relation
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "fileUrl": "https://files.test.ybm100.com/INVT/Lzinq/20250508/68fc774ee4b9bbb9bbc2a2af2b4cd0ca99aa76914a95cbd563071c3e439298bd.xlsx"
}



