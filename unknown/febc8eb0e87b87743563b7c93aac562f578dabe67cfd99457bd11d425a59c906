package com.xyy.saas.transmitter.server.convert.transmission;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.dto.InquiryClinicalCaseRespDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.OutPatientCaseTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.OutPatientCaseTransmitterDTO.Diag;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/24 17:39
 */
@Mapper
public interface TransmissionInternetConvert {

    TransmissionInternetConvert INSTANCE = Mappers.getMapper(TransmissionInternetConvert.class);


    default OutPatientCaseTransmitterDTO convert(InquiryClinicalCaseRespDto bean) {
        OutPatientCaseTransmitterDTO transmitterDTO = caseConvert(bean);
        fillDiagList(transmitterDTO);
        return transmitterDTO;
    }

    static void fillDiagList(OutPatientCaseTransmitterDTO transmitterDTO) {
        if (transmitterDTO == null) {
            return;
        }
        List<Diag> diagnosis = new ArrayList<>() {{
            int index1 = 0;
            int index2 = 0;
            if (CollUtil.isNotEmpty(transmitterDTO.getDiagnosisCode())) {
                for (int i = 0; i < transmitterDTO.getDiagnosisCode().size(); i++) {
                    Diag diag = new Diag();
                    diag.setDiagnosisCode(transmitterDTO.getDiagnosisCode().get(i));
                    diag.setDiagnosisName(transmitterDTO.getDiagnosisName().size() > i ? transmitterDTO.getDiagnosisName().get(i) : "");
                    diag.setDiagnosisClassify(Objects.equals(MedicineTypeEnum.ASIAN_MEDICINE.getCode(), transmitterDTO.getMedicineType()) ? MedicineTypeEnum.ASIAN_MEDICINE.getCode() : MedicineTypeEnum.CHINESE_MEDICINE.getCode());
                    diag.setIndex(index1++);
                    add(diag);
                }
            }
            if (CollUtil.isNotEmpty(transmitterDTO.getTcmDiagnosisCode())) {
                for (int i = 0; i < transmitterDTO.getTcmDiagnosisCode().size(); i++) {
                    Diag diag = new Diag();
                    diag.setDiagnosisCode(transmitterDTO.getTcmDiagnosisCode().get(i));
                    diag.setDiagnosisName(transmitterDTO.getTcmDiagnosisName().size() > i ? transmitterDTO.getTcmDiagnosisName().get(i) : "");
                    diag.setDiagnosisClassify(MedicineTypeEnum.ASIAN_MEDICINE.getCode());
                    diag.setIndex(index2++);
                    add(diag);
                }
            }
        }};
        transmitterDTO.setDiagnosis(diagnosis);
    }

    OutPatientCaseTransmitterDTO caseConvert(InquiryClinicalCaseRespDto bean);


}
